#pragma once

/* <editor-fold desc="MIT License">

Copyright(c) 2024 VSG Render Engine Abstraction

Permission is hereby granted, free of charge, to any person obtaining a copy of this software and associated documentation files (the "Software"), to deal in the Software without restriction, including without limitation the rights to use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of the Software, and to permit persons to whom the Software is furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.

</editor-fold> */

#include <vsg_abstraction/core/Types.h>
#include <vsg_abstraction/core/Export.h>

// 前向声明VSG类型
namespace vsg {
    class Visitor;
    class ConstVisitor;
    template<typename T> class ref_ptr;
}

namespace vsg_abstraction {

// 前向声明
class INode;
class IGroup;
class ITransform;
class IGeometry;
class IStateGroup;

/**
 * @brief 访问者抽象接口
 * 
 * 这是访问者模式的核心接口，用于遍历和操作场景图。
 * 设计目标：
 * 1. 与VSG Visitor接口保持兼容
 * 2. 支持类型安全的多态访问
 * 3. 提供灵活的遍历控制
 * 4. 支持VSG访问者的直接集成
 */
class VSG_ABSTRACTION_DECLSPEC IVisitor {
public:
    virtual ~IVisitor() = default;

    // ========== 遍历控制 ==========
    
    /**
     * @brief 遍历模式
     */
    enum class TraversalMode {
        TRAVERSE_NONE,              ///< 不遍历子节点
        TRAVERSE_PARENTS,           ///< 遍历父节点
        TRAVERSE_ALL_CHILDREN,      ///< 遍历所有子节点
        TRAVERSE_ACTIVE_CHILDREN    ///< 只遍历活跃的子节点
    };
    
    /**
     * @brief 获取遍历模式
     */
    virtual TraversalMode getTraversalMode() const { return TraversalMode::TRAVERSE_ALL_CHILDREN; }
    
    /**
     * @brief 设置遍历模式
     */
    virtual void setTraversalMode(TraversalMode mode) { traversalMode_ = mode; }

    // ========== 核心访问方法 ==========
    
    /**
     * @brief 访问基础节点
     * @param node 节点对象
     */
    virtual void apply(INode& node) = 0;
    
    /**
     * @brief 访问组节点
     * @param group 组节点对象
     */
    virtual void apply(IGroup& group) { apply(static_cast<INode&>(group)); }
    
    /**
     * @brief 访问变换节点
     * @param transform 变换节点对象
     */
    virtual void apply(ITransform& transform) { apply(static_cast<IGroup&>(transform)); }
    
    /**
     * @brief 访问几何节点
     * @param geometry 几何节点对象
     */
    virtual void apply(IGeometry& geometry) { apply(static_cast<INode&>(geometry)); }
    
    /**
     * @brief 访问状态组节点
     * @param stateGroup 状态组节点对象
     */
    virtual void apply(IStateGroup& stateGroup) { apply(static_cast<IGroup&>(stateGroup)); }

    // ========== 遍历辅助方法 ==========
    
    /**
     * @brief 遍历节点的子节点
     * @param node 父节点
     */
    virtual void traverse(INode& node);
    
    /**
     * @brief 遍历组节点的子节点
     * @param group 组节点
     */
    virtual void traverse(IGroup& group);

    // ========== VSG兼容性接口 ==========
    
    /**
     * @brief 获取VSG访问者对象
     * @return VSG访问者指针
     */
    virtual vsg::ref_ptr<vsg::Visitor> getVSGVisitor() const = 0;
    
    /**
     * @brief 从VSG访问者创建抽象访问者
     * @param vsgVisitor VSG访问者
     * @return 抽象访问者指针
     */
    static ref_ptr<IVisitor> createFromVSG(vsg::ref_ptr<vsg::Visitor> vsgVisitor);

protected:
    TraversalMode traversalMode_ = TraversalMode::TRAVERSE_ALL_CHILDREN;
};

/**
 * @brief 常量访问者抽象接口
 * 
 * 用于只读访问场景图的访问者接口
 */
class VSG_ABSTRACTION_DECLSPEC IConstVisitor {
public:
    virtual ~IConstVisitor() = default;

    // ========== 遍历控制 ==========
    
    /**
     * @brief 遍历模式
     */
    using TraversalMode = IVisitor::TraversalMode;
    
    /**
     * @brief 获取遍历模式
     */
    virtual TraversalMode getTraversalMode() const { return TraversalMode::TRAVERSE_ALL_CHILDREN; }
    
    /**
     * @brief 设置遍历模式
     */
    virtual void setTraversalMode(TraversalMode mode) { traversalMode_ = mode; }

    // ========== 核心访问方法 ==========
    
    /**
     * @brief 访问基础节点（常量版本）
     * @param node 节点对象
     */
    virtual void apply(const INode& node) = 0;
    
    /**
     * @brief 访问组节点（常量版本）
     * @param group 组节点对象
     */
    virtual void apply(const IGroup& group) { apply(static_cast<const INode&>(group)); }
    
    /**
     * @brief 访问变换节点（常量版本）
     * @param transform 变换节点对象
     */
    virtual void apply(const ITransform& transform) { apply(static_cast<const IGroup&>(transform)); }
    
    /**
     * @brief 访问几何节点（常量版本）
     * @param geometry 几何节点对象
     */
    virtual void apply(const IGeometry& geometry) { apply(static_cast<const INode&>(geometry)); }
    
    /**
     * @brief 访问状态组节点（常量版本）
     * @param stateGroup 状态组节点对象
     */
    virtual void apply(const IStateGroup& stateGroup) { apply(static_cast<const IGroup&>(stateGroup)); }

    // ========== 遍历辅助方法 ==========
    
    /**
     * @brief 遍历节点的子节点（常量版本）
     * @param node 父节点
     */
    virtual void traverse(const INode& node);
    
    /**
     * @brief 遍历组节点的子节点（常量版本）
     * @param group 组节点
     */
    virtual void traverse(const IGroup& group);

    // ========== VSG兼容性接口 ==========
    
    /**
     * @brief 获取VSG常量访问者对象
     * @return VSG常量访问者指针
     */
    virtual vsg::ref_ptr<vsg::ConstVisitor> getVSGConstVisitor() const = 0;
    
    /**
     * @brief 从VSG常量访问者创建抽象常量访问者
     * @param vsgVisitor VSG常量访问者
     * @return 抽象常量访问者指针
     */
    static ref_ptr<IConstVisitor> createFromVSG(vsg::ref_ptr<vsg::ConstVisitor> vsgVisitor);

protected:
    TraversalMode traversalMode_ = TraversalMode::TRAVERSE_ALL_CHILDREN;
};

/**
 * @brief 记录遍历访问者抽象接口
 * 
 * 专门用于渲染命令录制的访问者接口
 */
class VSG_ABSTRACTION_DECLSPEC IRecordTraversal : public IVisitor {
public:
    // ========== 命令录制相关 ==========
    
    /**
     * @brief 设置命令缓冲区
     * @param commandBuffer 命令缓冲区
     */
    virtual void setCommandBuffer(ref_ptr<class ICommandBuffer> commandBuffer) = 0;
    
    /**
     * @brief 获取命令缓冲区
     */
    virtual ref_ptr<class ICommandBuffer> getCommandBuffer() const = 0;
    
    /**
     * @brief 开始录制
     */
    virtual void begin() = 0;
    
    /**
     * @brief 结束录制
     */
    virtual void end() = 0;
    
    /**
     * @brief 重置录制状态
     */
    virtual void reset() = 0;

    // ========== 状态管理 ==========
    
    /**
     * @brief 推入状态
     * @param state 状态对象
     */
    virtual void pushState(ref_ptr<INode> state) = 0;
    
    /**
     * @brief 弹出状态
     */
    virtual void popState() = 0;
    
    /**
     * @brief 应用当前状态
     */
    virtual void applyCurrentState() = 0;

    // ========== 统计信息 ==========
    
    /**
     * @brief 获取录制统计信息
     */
    virtual RenderStatistics getStatistics() const = 0;
    
    /**
     * @brief 重置统计信息
     */
    virtual void resetStatistics() = 0;
};

/**
 * @brief 更新访问者抽象接口
 * 
 * 用于更新场景图的访问者接口
 */
class VSG_ABSTRACTION_DECLSPEC IUpdateVisitor : public IVisitor {
public:
    // ========== 时间管理 ==========
    
    /**
     * @brief 设置帧时间
     * @param frameTime 帧时间（秒）
     */
    virtual void setFrameTime(double frameTime) = 0;
    
    /**
     * @brief 获取帧时间
     */
    virtual double getFrameTime() const = 0;
    
    /**
     * @brief 设置增量时间
     * @param deltaTime 增量时间（秒）
     */
    virtual void setDeltaTime(double deltaTime) = 0;
    
    /**
     * @brief 获取增量时间
     */
    virtual double getDeltaTime() const = 0;

    // ========== 更新操作 ==========
    
    /**
     * @brief 更新变换矩阵
     * @param transform 变换节点
     */
    virtual void updateTransform(ITransform& transform) = 0;
    
    /**
     * @brief 更新包围盒
     * @param node 节点
     */
    virtual void updateBounds(INode& node) = 0;
    
    /**
     * @brief 更新动画
     * @param node 节点
     */
    virtual void updateAnimation(INode& node) = 0;
};

/**
 * @brief 剔除访问者抽象接口
 * 
 * 用于视锥体剔除的访问者接口
 */
class VSG_ABSTRACTION_DECLSPEC ICullVisitor : public IVisitor {
public:
    // ========== 视锥体管理 ==========
    
    /**
     * @brief 设置视锥体
     * @param frustum 视锥体参数 [left, right, bottom, top, near, far]
     */
    virtual void setFrustum(const std::array<double, 6>& frustum) = 0;
    
    /**
     * @brief 获取视锥体
     */
    virtual const std::array<double, 6>& getFrustum() const = 0;
    
    /**
     * @brief 设置视图矩阵
     * @param viewMatrix 视图矩阵
     */
    virtual void setViewMatrix(const dmat4& viewMatrix) = 0;
    
    /**
     * @brief 获取视图矩阵
     */
    virtual const dmat4& getViewMatrix() const = 0;
    
    /**
     * @brief 设置投影矩阵
     * @param projectionMatrix 投影矩阵
     */
    virtual void setProjectionMatrix(const dmat4& projectionMatrix) = 0;
    
    /**
     * @brief 获取投影矩阵
     */
    virtual const dmat4& getProjectionMatrix() const = 0;

    // ========== 剔除操作 ==========
    
    /**
     * @brief 检查节点是否在视锥体内
     * @param node 节点
     * @return 在视锥体内返回true
     */
    virtual bool isInFrustum(const INode& node) const = 0;
    
    /**
     * @brief 剔除节点
     * @param node 节点
     * @return 应该渲染返回true
     */
    virtual bool cull(const INode& node) = 0;

    // ========== 统计信息 ==========
    
    /**
     * @brief 获取剔除统计信息
     */
    struct CullStatistics {
        uint32_t totalNodes = 0;
        uint32_t culledNodes = 0;
        uint32_t visibleNodes = 0;
        double cullTime = 0.0;
    };
    
    /**
     * @brief 获取剔除统计信息
     */
    virtual CullStatistics getCullStatistics() const = 0;
    
    /**
     * @brief 重置剔除统计信息
     */
    virtual void resetCullStatistics() = 0;
};

} // namespace vsg_abstraction
