// include/vsg_webgpu/Device.h
#pragma once
#include <emscripten/html5_webgpu.h>
#include <memory>
#include <mutex>
#include <unordered_map>
#include <vsg/core/Object.h>
#include <vsg/core/ref_ptr.h>
#include <vsg/maths/mat4.h>
#include <vsg/vk/DeviceFeatures.h>
#include <webgpu/webgpu_cpp.h>

namespace vsg_webgpu
{
    // forward declarations
    class Queue;
    class CommandPool;
    class MemoryBufferPools;
    class DescriptorPools;

    /// WebGPU设备管理，对应VSG的Device类
    /// 提供与VSG Device相同的接口，但底层使用WebGPU实现
    class VSG_DECLSPEC Device : public vsg::Inherit<vsg::Object, Device>
    {
    public:
        Device();
        virtual ~Device();

        // 初始化WebGPU设备
        bool initialize(const wgpu::Surface& surface, const vsg::DeviceFeatures* deviceFeatures = nullptr);

        // WebGPU对象访问
        wgpu::Device getDevice() const { return _device; }
        wgpu::Instance getInstance() const { return _instance; }
        wgpu::Adapter getAdapter() const { return _adapter; }

        // 队列管理 - 对应VSG的Queue系统
        vsg::ref_ptr<Queue> getQueue(uint32_t queueFamilyIndex = 0, uint32_t queueIndex = 0);
        const std::vector<vsg::ref_ptr<Queue>>& getQueues() const { return _queues; }

        // 交换链管理
        wgpu::SwapChain getSwapChain() const { return _swapChain; }
        wgpu::TextureFormat getSwapChainFormat() const { return _swapChainFormat; }
        void resizeSwapChain(uint32_t width, uint32_t height);

        // 当前帧资源
        wgpu::TextureView getCurrentTextureView();
        wgpu::CommandEncoder createCommandEncoder();
        void submitCommandBuffer(wgpu::CommandBuffer commands);

        // 深度缓冲管理
        void createDepthBuffer(uint32_t width, uint32_t height);
        wgpu::TextureView getDepthTextureView() const { return _depthTextureView; }

        // 设备ID管理 - 对应VSG的deviceID系统
        static uint32_t maxNumDevices() { return 1; } // WebGPU通常只有一个设备
        const uint32_t deviceID = 0;

        // 内存和资源管理 - 对应VSG的内存管理系统
        vsg::ref_ptr<MemoryBufferPools> getMemoryBufferPools() { return _memoryBufferPools; }
        vsg::ref_ptr<DescriptorPools> getDescriptorPools() { return _descriptorPools; }

        // 命令池管理 - 对应VSG的CommandPool系统
        vsg::ref_ptr<CommandPool> getCommandPool(uint32_t queueFamilyIndex = 0);

        // 资源缓存系统
        class ResourceCache
        {
        public:
            ResourceCache(Device* device);

            // 渲染管线缓存
            wgpu::RenderPipeline getOrCreateRenderPipeline(size_t hash,
                                                           const std::function<wgpu::RenderPipeline()>& creator);

            // 计算管线缓存
            wgpu::ComputePipeline getOrCreateComputePipeline(size_t hash,
                                                             const std::function<wgpu::ComputePipeline()>& creator);

            // 绑定组缓存
            wgpu::BindGroup getOrCreateBindGroup(size_t hash,
                                                 const std::function<wgpu::BindGroup()>& creator);

            // 缓冲区缓存
            wgpu::Buffer getOrCreateBuffer(const std::string& key,
                                           const wgpu::BufferDescriptor& desc,
                                           const std::function<void(wgpu::Buffer)>& initializer = nullptr);

            // 纹理缓存
            wgpu::Texture getOrCreateTexture(const std::string& key,
                                             const wgpu::TextureDescriptor& desc);

            // 采样器缓存
            wgpu::Sampler getOrCreateSampler(size_t hash,
                                             const wgpu::SamplerDescriptor& desc);

            // 着色器模块缓存
            wgpu::ShaderModule getOrCreateShaderModule(const std::string& key,
                                                       const std::string& wgslCode);

            // 清理资源
            void clear();

        private:
            Device* _device;
            std::mutex _mutex;
            std::unordered_map<size_t, wgpu::RenderPipeline> _renderPipelines;
            std::unordered_map<size_t, wgpu::ComputePipeline> _computePipelines;
            std::unordered_map<size_t, wgpu::BindGroup> _bindGroups;
            std::unordered_map<std::string, wgpu::Buffer> _buffers;
            std::unordered_map<std::string, wgpu::Texture> _textures;
            std::unordered_map<size_t, wgpu::Sampler> _samplers;
            std::unordered_map<std::string, wgpu::ShaderModule> _shaderModules;
        };

        ResourceCache& getResourceCache() { return _resourceCache; }

        // 错误处理和调试
        void setErrorCallback(const std::function<void(const std::string&)>& callback);
        void logError(const std::string& message);

        // 功能查询 - 对应VSG的功能查询系统
        bool supportsFeature(const std::string& feature) const;
        const std::vector<std::string>& getSupportedFeatures() const { return _supportedFeatures; }

    private:
        // WebGPU核心对象
        wgpu::Instance _instance;
        wgpu::Adapter _adapter;
        wgpu::Device _device;
        wgpu::Surface _surface;
        wgpu::SwapChain _swapChain;
        wgpu::TextureFormat _swapChainFormat;

        // 深度缓冲
        wgpu::Texture _depthTexture;
        wgpu::TextureView _depthTextureView;

        // VSG兼容的管理对象
        std::vector<vsg::ref_ptr<Queue>> _queues;
        std::vector<vsg::ref_ptr<CommandPool>> _commandPools;
        vsg::ref_ptr<MemoryBufferPools> _memoryBufferPools;
        vsg::ref_ptr<DescriptorPools> _descriptorPools;

        // 资源缓存
        ResourceCache _resourceCache;

        // 功能支持
        std::vector<std::string> _supportedFeatures;

        // 错误处理
        std::function<void(const std::string&)> _errorCallback;

        // 内部初始化方法
        bool _initializeInstance();
        bool _requestAdapter();
        bool _requestDevice(const vsg::DeviceFeatures* deviceFeatures);
        void _setupFeatures();
    };

    // 实现
    Device::Device()
    {
        _instance = wgpu::CreateInstance();
    }

    bool Device::initialize(const wgpu::Surface& surface)
    {
        _surface = surface;

        // 请求适配器
        wgpu::RequestAdapterOptions adapterOpts{};
        adapterOpts.compatibleSurface = _surface;

        _adapter = _instance.RequestAdapter(&adapterOpts);
        if (!_adapter) return false;

        // 请求设备
        wgpu::DeviceDescriptor deviceDesc{};
        wgpu::FeatureName requiredFeatures[] = {
            wgpu::FeatureName::DepthClipControl,
            wgpu::FeatureName::Depth32FloatStencil8};
        deviceDesc.requiredFeatures = requiredFeatures;
        deviceDesc.requiredFeatureCount = 2;

        // 设置限制
        wgpu::RequiredLimits requiredLimits{};
        requiredLimits.limits.maxBindGroups = 4;
        requiredLimits.limits.maxUniformBufferBindingSize = 65536;
        requiredLimits.limits.maxStorageBufferBindingSize = 134217728;
        requiredLimits.limits.maxVertexAttributes = 16;
        requiredLimits.limits.maxVertexBuffers = 8;
        deviceDesc.requiredLimits = &requiredLimits;

        _device = _adapter.RequestDevice(&deviceDesc);
        if (!_device) return false;

        _queue = _device.GetQueue();

        // 配置交换链
        _swapChainFormat = _surface.GetPreferredFormat(_adapter);

        wgpu::SwapChainDescriptor swapChainDesc{};
        swapChainDesc.usage = wgpu::TextureUsage::RenderAttachment;
        swapChainDesc.format = _swapChainFormat;
        swapChainDesc.width = 1920; // 初始尺寸，后续会更新
        swapChainDesc.height = 1080;
        swapChainDesc.presentMode = wgpu::PresentMode::Fifo;

        _swapChain = _device.CreateSwapChain(_surface, &swapChainDesc);

        return true;
    }

} // namespace vsg_webgpu
