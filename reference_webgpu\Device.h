// include/rocky_webgpu/Device.h
#pragma once
#include <emscripten/html5_webgpu.h>
#include <memory>
#include <unordered_map>
#include <vsg/core/Object.h>
#include <vsg/core/ref_ptr.h>
#include <vsg/maths/mat4.h>
#include <webgpu/webgpu_cpp.h>

namespace rocky_webgpu
{

    // WebGPU设备管理，替代VSG的Device
    class Device : public vsg::Inherit<vsg::Object, Device>
    {
    public:
        Device();
        virtual ~Device();

        // 初始化WebGPU
        bool initialize(const wgpu::Surface& surface);

        // 获取WebGPU对象
        wgpu::Device getDevice() const { return _device; }
        wgpu::Queue getQueue() const { return _queue; }
        wgpu::SwapChain getSwapChain() const { return _swapChain; }
        wgpu::TextureFormat getSwapChainFormat() const { return _swapChainFormat; }

        // 当前帧资源
        wgpu::TextureView getCurrentTextureView();
        wgpu::CommandEncoder createCommandEncoder();
        void submitCommandBuffer(wgpu::CommandBuffer commands);

        // 创建深度缓冲
        void createDepthBuffer(uint32_t width, uint32_t height);
        wgpu::TextureView getDepthTextureView() const { return _depthTextureView; }

        // 资源缓存
        class ResourceCache
        {
        public:
            // 管线缓存
            wgpu::RenderPipeline getOrCreatePipeline(size_t hash,
                                                     const std::function<wgpu::RenderPipeline()>& creator);

            // 绑定组缓存
            wgpu::BindGroup getOrCreateBindGroup(size_t hash,
                                                 const std::function<wgpu::BindGroup()>& creator);

            // 缓冲区缓存
            wgpu::Buffer getOrCreateBuffer(const std::string& key,
                                           const wgpu::BufferDescriptor& desc,
                                           const std::function<void(wgpu::Buffer)>& initializer);

        private:
            std::unordered_map<size_t, wgpu::RenderPipeline> _pipelines;
            std::unordered_map<size_t, wgpu::BindGroup> _bindGroups;
            std::unordered_map<std::string, wgpu::Buffer> _buffers;
        };

        ResourceCache& getResourceCache() { return _resourceCache; }

    private:
        wgpu::Instance _instance;
        wgpu::Adapter _adapter;
        wgpu::Device _device;
        wgpu::Queue _queue;
        wgpu::Surface _surface;
        wgpu::SwapChain _swapChain;
        wgpu::TextureFormat _swapChainFormat;

        wgpu::Texture _depthTexture;
        wgpu::TextureView _depthTextureView;

        ResourceCache _resourceCache;
    };

    // 实现
    Device::Device()
    {
        _instance = wgpu::CreateInstance();
    }

    bool Device::initialize(const wgpu::Surface& surface)
    {
        _surface = surface;

        // 请求适配器
        wgpu::RequestAdapterOptions adapterOpts{};
        adapterOpts.compatibleSurface = _surface;

        _adapter = _instance.RequestAdapter(&adapterOpts);
        if (!_adapter) return false;

        // 请求设备
        wgpu::DeviceDescriptor deviceDesc{};
        wgpu::FeatureName requiredFeatures[] = {
            wgpu::FeatureName::DepthClipControl,
            wgpu::FeatureName::Depth32FloatStencil8};
        deviceDesc.requiredFeatures = requiredFeatures;
        deviceDesc.requiredFeatureCount = 2;

        // 设置限制
        wgpu::RequiredLimits requiredLimits{};
        requiredLimits.limits.maxBindGroups = 4;
        requiredLimits.limits.maxUniformBufferBindingSize = 65536;
        requiredLimits.limits.maxStorageBufferBindingSize = 134217728;
        requiredLimits.limits.maxVertexAttributes = 16;
        requiredLimits.limits.maxVertexBuffers = 8;
        deviceDesc.requiredLimits = &requiredLimits;

        _device = _adapter.RequestDevice(&deviceDesc);
        if (!_device) return false;

        _queue = _device.GetQueue();

        // 配置交换链
        _swapChainFormat = _surface.GetPreferredFormat(_adapter);

        wgpu::SwapChainDescriptor swapChainDesc{};
        swapChainDesc.usage = wgpu::TextureUsage::RenderAttachment;
        swapChainDesc.format = _swapChainFormat;
        swapChainDesc.width = 1920; // 初始尺寸，后续会更新
        swapChainDesc.height = 1080;
        swapChainDesc.presentMode = wgpu::PresentMode::Fifo;

        _swapChain = _device.CreateSwapChain(_surface, &swapChainDesc);

        return true;
    }

} // namespace rocky_webgpu
