// include/vsg_webgpu/Queue.h
#pragma once
#include <vsg/core/Object.h>
#include <vsg/core/ref_ptr.h>
#include <webgpu/webgpu_cpp.h>
#include <vector>
#include <mutex>

namespace vsg_webgpu
{
    // forward declarations
    class Device;
    class CommandBuffer;
    class Fence;
    class Semaphore;

    /// WebGPU队列封装，对应VSG的Queue类
    /// 提供与VSG Queue相同的接口，但底层使用WebGPU实现
    class VSG_DECLSPEC Queue : public vsg::Inherit<vsg::Object, Queue>
    {
    public:
        Queue(Device* device, wgpu::Queue queue, uint32_t queueFamilyIndex = 0, uint32_t queueIndex = 0);

        // WebGPU队列访问
        wgpu::Queue getQueue() const { return _queue; }
        operator wgpu::Queue() const { return _queue; }

        // 队列属性 - 对应VSG的Queue接口
        uint32_t queueFamilyIndex() const { return _queueFamilyIndex; }
        uint32_t queueIndex() const { return _queueIndex; }
        uint32_t queueFlags() const { return _queueFlags; }

        Device* getDevice() { return _device; }
        const Device* getDevice() const { return _device; }

        // 命令提交 - 对应VSG的submit方法
        struct SubmitInfo
        {
            std::vector<vsg::ref_ptr<CommandBuffer>> commandBuffers;
            std::vector<vsg::ref_ptr<Semaphore>> waitSemaphores;
            std::vector<vsg::ref_ptr<Semaphore>> signalSemaphores;
            vsg::ref_ptr<Fence> fence;
        };

        void submit(const std::vector<SubmitInfo>& submitInfos);
        void submit(const SubmitInfo& submitInfo);

        // 简化的提交方法
        void submit(vsg::ref_ptr<CommandBuffer> commandBuffer, vsg::ref_ptr<Fence> fence = {});
        void submit(const std::vector<vsg::ref_ptr<CommandBuffer>>& commandBuffers, vsg::ref_ptr<Fence> fence = {});

        // 队列同步
        void waitIdle();

        // 呈现支持 - 对应VSG的present方法
        struct PresentInfo
        {
            std::vector<vsg::ref_ptr<Semaphore>> waitSemaphores;
            // WebGPU的present是自动的，不需要显式调用
        };

        void present(const PresentInfo& presentInfo);

        // 时间戳查询支持
        void writeTimestamp(uint32_t queryIndex);

    protected:
        virtual ~Queue();

    private:
        Device* _device;
        wgpu::Queue _queue;
        uint32_t _queueFamilyIndex;
        uint32_t _queueIndex;
        uint32_t _queueFlags;
        
        std::mutex _submitMutex;

        // 内部提交实现
        void _submitCommandBuffers(const std::vector<vsg::ref_ptr<CommandBuffer>>& commandBuffers);
        void _handleSemaphores(const std::vector<vsg::ref_ptr<Semaphore>>& waitSemaphores,
                              const std::vector<vsg::ref_ptr<Semaphore>>& signalSemaphores);
    };

    VSG_type_name(vsg_webgpu::Queue);

} // namespace vsg_webgpu
