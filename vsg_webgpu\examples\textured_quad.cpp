/* <editor-fold desc="MIT License">

Copyright(c) 2024 VSG WebGPU Extension

Permission is hereby granted, free of charge, to any person obtaining a copy of this software and associated documentation files (the "Software"), to deal in the Software without restriction, including without limitation the rights to use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of the Software, and to permit persons to whom the Software is furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.

</editor-fold> */

#include <vsg_webgpu/all.h>
#include <vsg/all.h>
#include <iostream>

using namespace vsg_webgpu;

int main()
{
    try
    {
        std::cout << "VSG WebGPU Textured Quad Example" << std::endl;
        
        // 初始化WebGPU
        initializeWebGPU();
        
        // 创建设备
        auto device = Device::create();
        if (!device || !device->initialize())
        {
            std::cerr << "Failed to initialize WebGPU device" << std::endl;
            return 1;
        }
        
        // 创建窗口
        auto window = Window::create("VSG WebGPU Textured Quad", 800, 600);
        window->device = device;
        
#if !VSG_WEBGPU_USE_MOCK
        if (!window->create())
        {
            std::cerr << "Failed to create window" << std::endl;
            return 1;
        }
#endif
        
        // 创建场景图
        auto sceneGraph = vsg::Group::create();
        
        // 创建四边形顶点数据（带纹理坐标）
        auto vertices = vsg::vec3Array::create({
            {-0.5f, -0.5f, 0.0f}, // 左下
            { 0.5f, -0.5f, 0.0f}, // 右下
            { 0.5f,  0.5f, 0.0f}, // 右上
            {-0.5f,  0.5f, 0.0f}  // 左上
        });
        
        // 纹理坐标
        auto texCoords = vsg::vec3Array::create({
            {0.0f, 1.0f, 0.0f}, // 左下
            {1.0f, 1.0f, 0.0f}, // 右下
            {1.0f, 0.0f, 0.0f}, // 右上
            {0.0f, 0.0f, 0.0f}  // 左上
        });
        
        auto indices = vsg::ushortArray::create({
            0, 1, 2,  // 第一个三角形
            2, 3, 0   // 第二个三角形
        });
        
        // 创建几何体
        auto geometry = vsg::Geometry::create();
        geometry->arrays = {vertices, texCoords};
        geometry->indices = indices;
        geometry->commands = {vsg::DrawIndexed::create(6, 1, 0, 0, 0)};
        
        // 添加到场景图
        sceneGraph->addChild(geometry);
        
        // 创建渲染图
        auto renderGraph = RenderGraph::create(device.get(), window.get());
        renderGraph->addRenderPass("main", sceneGraph);
        
        // 创建记录遍历器
        auto recordTraversal = RecordTraversal::create();
        
        std::cout << "Starting render loop..." << std::endl;
        
        // 渲染循环
        int frameCount = 0;
        const int maxFrames = 120; // 2秒 @ 60fps
        
#if VSG_WEBGPU_USE_MOCK
        // Mock模式：模拟渲染
        while (frameCount < maxFrames)
        {
            renderGraph->render(recordTraversal);
            frameCount++;
            
            if (frameCount % 30 == 0)
            {
                std::cout << "Frame " << frameCount << " - Mock textured quad rendering" << std::endl;
            }
            
            std::this_thread::sleep_for(std::chrono::milliseconds(16));
        }
#else
        // 实际渲染循环
        while (window->valid() && frameCount < maxFrames)
        {
            if (!window->pollEvents()) break;
            
            renderGraph->render(recordTraversal);
            window->present();
            
            frameCount++;
            
            if (frameCount % 30 == 0)
            {
                auto stats = recordTraversal->getStatistics();
                std::cout << "Frame " << frameCount 
                         << " - Draw calls: " << stats.numDrawCalls 
                         << " - Triangles: " << stats.numTriangles << std::endl;
            }
        }
#endif
        
        std::cout << "Textured quad example completed!" << std::endl;
        std::cout << "Rendered " << frameCount << " frames" << std::endl;
        
        // 清理
        recordTraversal.reset();
        renderGraph.reset();
        sceneGraph.reset();
        
        if (window)
        {
            window->destroy();
            window.reset();
        }
        
        device.reset();
        shutdownWebGPU();
        
        return 0;
    }
    catch (const std::exception& e)
    {
        std::cerr << "Error: " << e.what() << std::endl;
        return 1;
    }
}
