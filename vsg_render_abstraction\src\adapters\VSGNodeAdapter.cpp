/* <editor-fold desc="MIT License">

Copyright(c) 2024 VSG Render Engine Abstraction

Permission is hereby granted, free of charge, to any person obtaining a copy of this software and associated documentation files (the "Software"), to deal in the Software without restriction, including without limitation the rights to use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of the Software, and to permit persons to whom the Software is furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.

</editor-fold> */

#include <vsg_abstraction/adapters/VSGNodeAdapter.h>

#ifdef VSG_ABSTRACTION_HAS_VSG
#    include <vsg/all.h>

namespace vsg_abstraction
{

    // ========== VSGNodeAdapter Implementation ==========

    VSGNodeAdapter::VSGNodeAdapter(vsg::ref_ptr<vsg::Node> vsgNode) :
        vsgNode_(vsgNode)
    {
        if (!vsgNode_)
        {
            throw std::invalid_argument("VSG node cannot be null");
        }
    }

    NodeType VSGNodeAdapter::getNodeType() const
    {
        if (!vsgNode_) return NodeType::Node;

        // 使用VSG的类型信息来确定节点类型
        if (dynamic_cast<vsg::Group*>(vsgNode_.get()))
        {
            if (dynamic_cast<vsg::MatrixTransform*>(vsgNode_.get()))
            {
                return NodeType::Transform;
            }
            else if (dynamic_cast<vsg::StateGroup*>(vsgNode_.get()))
            {
                return NodeType::StateGroup;
            }
            else
            {
                return NodeType::Group;
            }
        }
        else if (dynamic_cast<vsg::Geometry*>(vsgNode_.get()))
        {
            return NodeType::Geometry;
        }
        else
        {
            return NodeType::Node;
        }
    }

    const std::string& VSGNodeAdapter::getName() const
    {
        // VSG节点没有内置的名称属性，我们使用用户数据来存储
        static std::string emptyName;

        if (vsgNode_ && vsgNode_->getValue("name"))
        {
            auto nameValue = vsgNode_->getValue<vsg::stringValue>("name");
            if (nameValue)
            {
                cachedName_ = nameValue->value();
                return cachedName_;
            }
        }

        return emptyName;
    }

    void VSGNodeAdapter::setName(const std::string& name)
    {
        if (vsgNode_)
        {
            vsgNode_->setValue("name", vsg::stringValue::create(name));
            cachedName_ = name;
        }
    }

    uint64_t VSGNodeAdapter::getNodeId() const
    {
        // 使用VSG节点的地址作为唯一ID
        return reinterpret_cast<uint64_t>(vsgNode_.get());
    }

    void VSGNodeAdapter::accept(IVisitor& visitor)
    {
        visitor.apply(*this);
    }

    void VSGNodeAdapter::traverse(IVisitor& visitor)
    {
        visitor.traverse(*this);
    }

    std::array<double, 6> VSGNodeAdapter::getLocalBounds() const
    {
        if (vsgNode_)
        {
            auto bounds = vsgNode_->bound;
            return {bounds.min.x, bounds.min.y, bounds.min.z,
                    bounds.max.x, bounds.max.y, bounds.max.z};
        }
        return {0, 0, 0, 0, 0, 0};
    }

    std::array<double, 6> VSGNodeAdapter::getWorldBounds() const
    {
        // 简化实现，返回本地边界
        return getLocalBounds();
    }

    void VSGNodeAdapter::computeBounds()
    {
        if (vsgNode_)
        {
            // VSG会自动计算边界，这里不需要特殊处理
        }
    }

    void VSGNodeAdapter::setUserData(const std::string& key, void* data)
    {
        if (vsgNode_)
        {
            // 将void*包装为VSG的用户数据
            vsgNode_->setValue(key, vsg::Value<void*>::create(data));
        }
    }

    void* VSGNodeAdapter::getUserData(const std::string& key) const
    {
        if (vsgNode_)
        {
            auto value = vsgNode_->getValue<vsg::Value<void*>>(key);
            if (value)
            {
                return value->value();
            }
        }
        return nullptr;
    }

    void VSGNodeAdapter::removeUserData(const std::string& key)
    {
        if (vsgNode_)
        {
            vsgNode_->removeValue(key);
        }
    }

    bool VSGNodeAdapter::hasUserData(const std::string& key) const
    {
        if (vsgNode_)
        {
            return vsgNode_->getValue(key) != nullptr;
        }
        return false;
    }

    void* VSGNodeAdapter::getNativeHandle() const
    {
        return vsgNode_.get();
    }

    vsg::ref_ptr<vsg::Node> VSGNodeAdapter::getVSGNode() const
    {
        return vsgNode_;
    }

    ref_ptr<INode> VSGNodeAdapter::create(vsg::ref_ptr<vsg::Node> vsgNode)
    {
        if (!vsgNode)
        {
            return nullptr;
        }

        // 根据VSG节点类型创建相应的适配器
        if (auto vsgGroup = dynamic_cast<vsg::Group*>(vsgNode.get()))
        {
            if (auto vsgTransform = dynamic_cast<vsg::MatrixTransform*>(vsgNode.get()))
            {
                return std::make_shared<VSGTransformAdapter>(vsgTransform);
            }
            else if (auto vsgStateGroup = dynamic_cast<vsg::StateGroup*>(vsgNode.get()))
            {
                return std::make_shared<VSGStateGroupAdapter>(vsgStateGroup);
            }
            else
            {
                return std::make_shared<VSGGroupAdapter>(vsgGroup);
            }
        }
        else if (auto vsgGeometry = dynamic_cast<vsg::Geometry*>(vsgNode.get()))
        {
            return std::make_shared<VSGGeometryAdapter>(vsgGeometry);
        }
        else
        {
            return std::make_shared<VSGNodeAdapter>(vsgNode);
        }
    }

    // ========== VSGGroupAdapter Implementation ==========

    VSGGroupAdapter::VSGGroupAdapter(vsg::ref_ptr<vsg::Group> vsgGroup) :
        VSGNodeAdapter(vsgGroup), vsgGroup_(vsgGroup)
    {
        if (!vsgGroup_)
        {
            throw std::invalid_argument("VSG group cannot be null");
        }
    }

    void VSGGroupAdapter::addChild(ref_ptr<INode> child)
    {
        if (!child || !vsgGroup_) return;

        // 获取VSG节点
        auto vsgNode = child->getVSGNode();
        if (vsgNode)
        {
            vsgGroup_->addChild(vsgNode);
            invalidateChildrenCache();
        }
    }

    void VSGGroupAdapter::removeChild(ref_ptr<INode> child)
    {
        if (!child || !vsgGroup_) return;

        auto vsgNode = child->getVSGNode();
        if (vsgNode)
        {
            auto& children = vsgGroup_->children;
            auto it = std::find(children.begin(), children.end(), vsgNode);
            if (it != children.end())
            {
                children.erase(it);
                invalidateChildrenCache();
            }
        }
    }

    void VSGGroupAdapter::removeChild(size_t index)
    {
        if (!vsgGroup_ || index >= vsgGroup_->children.size()) return;

        vsgGroup_->children.erase(vsgGroup_->children.begin() + index);
        invalidateChildrenCache();
    }

    void VSGGroupAdapter::removeAllChildren()
    {
        if (vsgGroup_)
        {
            vsgGroup_->children.clear();
            invalidateChildrenCache();
        }
    }

    size_t VSGGroupAdapter::getNumChildren() const
    {
        return vsgGroup_ ? vsgGroup_->children.size() : 0;
    }

    ref_ptr<INode> VSGGroupAdapter::getChild(size_t index) const
    {
        if (!vsgGroup_ || index >= vsgGroup_->children.size())
        {
            return nullptr;
        }

        _updateChildrenCache();
        return index < children_.size() ? children_[index] : nullptr;
    }

    const std::vector<ref_ptr<INode>>& VSGGroupAdapter::getChildren() const
    {
        _updateChildrenCache();
        return children_;
    }

    ref_ptr<INode> VSGGroupAdapter::findChild(const std::string& name) const
    {
        _updateChildrenCache();

        for (auto& child : children_)
        {
            if (child && child->getName() == name)
            {
                return child;
            }
        }
        return nullptr;
    }

    void VSGGroupAdapter::_updateChildrenCache() const
    {
        if (childrenCacheValid_ || !vsgGroup_) return;

        children_.clear();
        children_.reserve(vsgGroup_->children.size());

        for (auto& vsgChild : vsgGroup_->children)
        {
            if (vsgChild)
            {
                auto adapter = VSGNodeAdapter::create(vsgChild);
                children_.push_back(adapter);
            }
        }

        childrenCacheValid_ = true;
    }

    void VSGGroupAdapter::_invalidateChildrenCache()
    {
        childrenCacheValid_ = false;
    }

    // ========== VSGTransformAdapter Implementation ==========

    VSGTransformAdapter::VSGTransformAdapter(vsg::ref_ptr<vsg::MatrixTransform> vsgTransform) :
        VSGGroupAdapter(vsgTransform), vsgTransform_(vsgTransform)
    {
        if (!vsgTransform_)
        {
            throw std::invalid_argument("VSG transform cannot be null");
        }
    }

    void VSGTransformAdapter::setMatrix(const mat4& matrix)
    {
        if (vsgTransform_)
        {
            vsg::dmat4 vsgMatrix;
            for (int i = 0; i < 4; ++i)
            {
                for (int j = 0; j < 4; ++j)
                {
                    vsgMatrix[i][j] = matrix[i][j];
                }
            }
            vsgTransform_->matrix = vsgMatrix;
        }
    }

    const mat4& VSGTransformAdapter::getMatrix() const
    {
        if (vsgTransform_)
        {
            // 缓存转换后的矩阵
            for (int i = 0; i < 4; ++i)
            {
                for (int j = 0; j < 4; ++j)
                {
                    cachedMatrix_[i][j] = static_cast<float>(vsgTransform_->matrix[i][j]);
                }
            }
        }
        return cachedMatrix_;
    }

    mat4 VSGTransformAdapter::getWorldMatrix() const
    {
        // 简化实现，返回本地矩阵
        return getMatrix();
    }

    void VSGTransformAdapter::setPosition(const vec3& position)
    {
        if (vsgTransform_)
        {
            vsgTransform_->matrix[3][0] = position[0];
            vsgTransform_->matrix[3][1] = position[1];
            vsgTransform_->matrix[3][2] = position[2];
        }
    }

    vec3 VSGTransformAdapter::getPosition() const
    {
        if (vsgTransform_)
        {
            return {
                static_cast<float>(vsgTransform_->matrix[3][0]),
                static_cast<float>(vsgTransform_->matrix[3][1]),
                static_cast<float>(vsgTransform_->matrix[3][2])};
        }
        return {0, 0, 0};
    }

    void VSGTransformAdapter::setRotation(const vec4& rotation)
    {
        // 简化实现，存储旋转但不更新矩阵
        cachedRotation_ = rotation;
    }

    vec4 VSGTransformAdapter::getRotation() const
    {
        return cachedRotation_;
    }

    void VSGTransformAdapter::setScale(const vec3& scale)
    {
        // 简化实现，存储缩放但不更新矩阵
        cachedScale_ = scale;
    }

    vec3 VSGTransformAdapter::getScale() const
    {
        return cachedScale_;
    }

    // ========== VSGGeometryAdapter Implementation ==========

    VSGGeometryAdapter::VSGGeometryAdapter(vsg::ref_ptr<vsg::Geometry> vsgGeometry) :
        VSGNodeAdapter(vsgGeometry), vsgGeometry_(vsgGeometry)
    {
        if (!vsgGeometry_)
        {
            throw std::invalid_argument("VSG geometry cannot be null");
        }
    }

    void VSGGeometryAdapter::setVertices(const std::vector<vec3>& vertices)
    {
        cachedVertices_ = vertices;
        verticesCacheValid_ = true;
        // 这里应该更新VSG几何体，但需要更复杂的实现
    }

    const std::vector<vec3>& VSGGeometryAdapter::getVertices() const
    {
        // 简化实现，返回缓存的顶点
        return cachedVertices_;
    }

    void VSGGeometryAdapter::setIndices(const std::vector<uint32_t>& indices)
    {
        cachedIndices_ = indices;
        indicesCacheValid_ = true;
        // 这里应该更新VSG几何体
    }

    const std::vector<uint32_t>& VSGGeometryAdapter::getIndices() const
    {
        return cachedIndices_;
    }

    void VSGGeometryAdapter::setNormals(const std::vector<vec3>& normals)
    {
        cachedNormals_ = normals;
        normalsCacheValid_ = true;
    }

    const std::vector<vec3>& VSGGeometryAdapter::getNormals() const
    {
        return cachedNormals_;
    }

    void VSGGeometryAdapter::setTexCoords(const std::vector<vec2>& texCoords)
    {
        cachedTexCoords_ = texCoords;
        texCoordsCacheValid_ = true;
    }

    const std::vector<vec2>& VSGGeometryAdapter::getTexCoords() const
    {
        return cachedTexCoords_;
    }

    void VSGGeometryAdapter::setPrimitiveTopology(PrimitiveTopology topology)
    {
        cachedTopology_ = topology;
    }

    PrimitiveTopology VSGGeometryAdapter::getPrimitiveTopology() const
    {
        return cachedTopology_;
    }

    // ========== VSGStateGroupAdapter Implementation ==========

    VSGStateGroupAdapter::VSGStateGroupAdapter(vsg::ref_ptr<vsg::StateGroup> vsgStateGroup) :
        VSGGroupAdapter(vsgStateGroup), vsgStateGroup_(vsgStateGroup)
    {
        if (!vsgStateGroup_)
        {
            throw std::invalid_argument("VSG state group cannot be null");
        }
    }

} // namespace vsg_abstraction

#endif // VSG_ABSTRACTION_HAS_VSG
