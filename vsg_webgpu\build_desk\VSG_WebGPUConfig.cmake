# VSG WebGPU Extension CMake Configuration File


####### Expanded from @PACKAGE_INIT@ by configure_package_config_file() #######
####### Any changes to this file will be overwritten by the next CMake run ####
####### The input file was VSG_WebGPUConfig.cmake.in                            ########

get_filename_component(PACKAGE_PREFIX_DIR "${CMAKE_CURRENT_LIST_DIR}/../../../" ABSOLUTE)

macro(set_and_check _var _file)
  set(${_var} "${_file}")
  if(NOT EXISTS "${_file}")
    message(FATAL_ERROR "File or directory ${_file} referenced by variable ${_var} does not exist !")
  endif()
endmacro()

macro(check_required_components _NAME)
  foreach(comp ${${_NAME}_FIND_COMPONENTS})
    if(NOT ${_NAME}_${comp}_FOUND)
      if(${_NAME}_FIND_REQUIRED_${comp})
        set(${_NAME}_FOUND FALSE)
      endif()
    endif()
  endforeach()
endmacro()

####################################################################################

include(CMakeFindDependencyMacro)

# 查找依赖项
if(NOT ON)
    find_dependency(vsg REQUIRED)
endif()

# 包含目标文件
if(NOT ON)
    include("${CMAKE_CURRENT_LIST_DIR}/VSG_WebGPUTargets.cmake")
endif()

# 设置变量
set(VSG_WebGPU_VERSION 1.0.0)
set(VSG_WebGPU_USE_MOCK_VSG ON)
set(VSG_WebGPU_USE_DAWN ON)
set(VSG_WebGPU_USE_EMSCRIPTEN OFF)

check_required_components(VSG_WebGPU)
