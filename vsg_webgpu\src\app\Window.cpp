/* <editor-fold desc="MIT License">

Copyright(c) 2024 VSG WebGPU Extension

Permission is hereby granted, free of charge, to any person obtaining a copy of this software and associated documentation files (the "Software"), to deal in the Software without restriction, including without limitation the rights to use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of the Software, and to permit persons to whom the Software is furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.

</editor-fold> */

#include <vsg_webgpu/app/Window.h>
#include <vsg_webgpu/vk/Device.h>

using namespace vsg_webgpu;

Window::Window(const std::string& title, uint32_t width, uint32_t height) :
    title(title), width(width), height(height),
    framebufferWidth(width), framebufferHeight(height)
{
    VSG_WEBGPU_LOG_DEBUG("Window::Window() - ", title, " ", width, "x", height);
}

Window::~Window()
{
    VSG_WEBGPU_LOG_DEBUG("Window::~Window()");
    destroy();
}

bool Window::create()
{
    VSG_WEBGPU_LOG_INFO("Creating window: ", title, " ", width, "x", height);
    
#if VSG_WEBGPU_USE_MOCK
    // Mock模式：模拟窗口创建
    _valid = true;
    VSG_WEBGPU_LOG_INFO("Mock window created successfully");
    return true;
#endif
    
    // 平台特定的窗口创建
#ifdef _WIN32
    if (!_createWin32Window())
    {
        VSG_WEBGPU_LOG_ERROR("Failed to create Win32 window");
        return false;
    }
#elif defined(__linux__)
    if (!_createX11Window())
    {
        VSG_WEBGPU_LOG_ERROR("Failed to create X11 window");
        return false;
    }
#elif defined(__APPLE__)
    if (!_createCocoaWindow())
    {
        VSG_WEBGPU_LOG_ERROR("Failed to create Cocoa window");
        return false;
    }
#else
    VSG_WEBGPU_LOG_ERROR("Unsupported platform");
    return false;
#endif
    
    // 创建WebGPU表面
    if (!_createWebGPUSurface())
    {
        VSG_WEBGPU_LOG_ERROR("Failed to create WebGPU surface");
        destroy();
        return false;
    }
    
    // 创建交换链
    if (!createSwapChain())
    {
        VSG_WEBGPU_LOG_ERROR("Failed to create swap chain");
        destroy();
        return false;
    }
    
    _valid = true;
    VSG_WEBGPU_LOG_INFO("Window created successfully");
    return true;
}

void Window::destroy()
{
    if (!_valid) return;
    
    VSG_WEBGPU_LOG_DEBUG("Destroying window");
    
    // 销毁WebGPU资源
    _destroyWebGPUSurface();
    
    // 销毁平台特定的窗口
#ifdef _WIN32
    _destroyWin32Window();
#elif defined(__linux__)
    _destroyX11Window();
#elif defined(__APPLE__)
    _destroyCocoaWindow();
#endif
    
    _valid = false;
    VSG_WEBGPU_LOG_DEBUG("Window destroyed");
}

bool Window::createSwapChain()
{
    if (!device || !surface) return false;
    
#if !VSG_WEBGPU_USE_MOCK
    // 获取首选格式
    swapChainFormat = surface.GetPreferredFormat(device->getAdapter());
    
    // 创建交换链描述符
    WGPUSwapChainDescriptor swapChainDesc = {};
    swapChainDesc.usage = WGPUTextureUsage_RenderAttachment;
    swapChainDesc.format = swapChainFormat;
    swapChainDesc.width = width;
    swapChainDesc.height = height;
    swapChainDesc.presentMode = WGPUPresentMode_Fifo;
    
    // 创建交换链
    swapChain = device->getDevice().CreateSwapChain(surface, &swapChainDesc);
    
    if (!swapChain)
    {
        VSG_WEBGPU_LOG_ERROR("Failed to create swap chain");
        return false;
    }
#endif
    
    VSG_WEBGPU_LOG_DEBUG("Swap chain created: ", width, "x", height);
    return true;
}

void Window::resizeSwapChain(uint32_t newWidth, uint32_t newHeight)
{
    if (width == newWidth && height == newHeight) return;
    
    width = newWidth;
    height = newHeight;
    framebufferWidth = newWidth;
    framebufferHeight = newHeight;
    
    if (device)
    {
        device->resizeSwapChain(width, height);
    }
    
    // 重新创建交换链
    createSwapChain();
    
    // 调用回调
    if (_resizeCallback)
    {
        _resizeCallback(width, height);
    }
    
    VSG_WEBGPU_LOG_DEBUG("Window resized to ", width, "x", height);
}

WGPUTextureView Window::getCurrentTextureView()
{
    if (!device) return {};
    
    return device->getCurrentTextureView();
}

void Window::present()
{
    // WebGPU的present是自动的，当交换链纹理被使用后会自动呈现
    // 这里主要是为了兼容VSG接口
}

bool Window::pollEvents()
{
    // 在实际实现中，这里会处理窗口事件
    // Mock模式下总是返回true
#if VSG_WEBGPU_USE_MOCK
    return _valid;
#endif
    
    // 平台特定的事件处理
#ifdef _WIN32
    // Win32事件处理
    MSG msg;
    while (PeekMessage(&msg, nullptr, 0, 0, PM_REMOVE))
    {
        if (msg.message == WM_QUIT)
        {
            _handleClose();
            return false;
        }
        
        if (msg.message == WM_SIZE)
        {
            uint32_t newWidth = LOWORD(msg.lParam);
            uint32_t newHeight = HIWORD(msg.lParam);
            _handleResize(newWidth, newHeight);
        }
        
        TranslateMessage(&msg);
        DispatchMessage(&msg);
    }
#elif defined(__linux__)
    // X11事件处理
    // 这里需要实现X11事件循环
#elif defined(__APPLE__)
    // Cocoa事件处理
    // 这里需要实现Cocoa事件循环
#endif
    
    return _valid;
}

void Window::waitEvents()
{
    // 等待事件
    // 在实际实现中，这里会阻塞等待窗口事件
}

void Window::setTitle(const std::string& newTitle)
{
    title = newTitle;
    
    // 平台特定的标题设置
#ifdef _WIN32
    if (_hwnd)
    {
        SetWindowTextA(static_cast<HWND>(_hwnd), title.c_str());
    }
#elif defined(__linux__)
    // X11标题设置
#elif defined(__APPLE__)
    // Cocoa标题设置
#endif
}

void Window::resize(uint32_t newWidth, uint32_t newHeight)
{
    resizeSwapChain(newWidth, newHeight);
}

void Window::setFullscreen(bool enable)
{
    fullscreen = enable;
    
    // 平台特定的全屏设置
#ifdef _WIN32
    if (_hwnd)
    {
        if (enable)
        {
            // 设置全屏
        }
        else
        {
            // 退出全屏
        }
    }
#elif defined(__linux__)
    // X11全屏设置
#elif defined(__APPLE__)
    // Cocoa全屏设置
#endif
}

void Window::setVisible(bool enable)
{
    visible = enable;
    
    // 平台特定的可见性设置
#ifdef _WIN32
    if (_hwnd)
    {
        ShowWindow(static_cast<HWND>(_hwnd), enable ? SW_SHOW : SW_HIDE);
    }
#elif defined(__linux__)
    // X11可见性设置
#elif defined(__APPLE__)
    // Cocoa可见性设置
#endif
}

// 平台特定的实现（简化版本）
#ifdef _WIN32
bool Window::_createWin32Window()
{
    // 这里需要实现完整的Win32窗口创建
    // 为了简化，我们只是模拟成功
    _hwnd = reinterpret_cast<void*>(0x12345678); // 模拟句柄
    return true;
}

void Window::_destroyWin32Window()
{
    _hwnd = nullptr;
}
#elif defined(__linux__)
bool Window::_createX11Window()
{
    // X11窗口创建实现
    return true;
}

void Window::_destroyX11Window()
{
    // X11窗口销毁实现
}
#elif defined(__APPLE__)
bool Window::_createCocoaWindow()
{
    // Cocoa窗口创建实现
    return true;
}

void Window::_destroyCocoaWindow()
{
    // Cocoa窗口销毁实现
}
#endif

bool Window::_createWebGPUSurface()
{
#if VSG_WEBGPU_USE_MOCK
    return true;
#endif
    
    if (!device) return false;
    
    // 平台特定的表面创建
#ifdef _WIN32
    WGPUSurfaceDescriptorFromWindowsHWND surfaceDesc = {};
    surfaceDesc.chain.sType = WGPUSType_SurfaceDescriptorFromWindowsHWND;
    surfaceDesc.hwnd = _hwnd;
    surfaceDesc.hinstance = GetModuleHandle(nullptr);
    
    WGPUSurfaceDescriptor desc = {};
    desc.nextInChain = &surfaceDesc.chain;
    
    surface = device->getInstance().CreateSurface(&desc);
#elif defined(__linux__)
    // X11表面创建
#elif defined(__APPLE__)
    // Metal表面创建
#endif
    
    return surface != nullptr;
}

void Window::_destroyWebGPUSurface()
{
    surface = {};
    swapChain = {};
}

void Window::_handleResize(uint32_t newWidth, uint32_t newHeight)
{
    resizeSwapChain(newWidth, newHeight);
}

void Window::_handleClose()
{
    if (_closeCallback)
    {
        _closeCallback();
    }
    _valid = false;
}
