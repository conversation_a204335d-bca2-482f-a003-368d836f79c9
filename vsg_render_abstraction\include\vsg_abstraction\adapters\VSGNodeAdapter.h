#pragma once

/* <editor-fold desc="MIT License">

Copyright(c) 2024 VSG Render Engine Abstraction

Permission is hereby granted, free of charge, to any person obtaining a copy of this software and associated documentation files (the "Software"), to deal in the Software without restriction, including without limitation the rights to use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of the Software, and to permit persons to whom the Software is furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND <PERSON>NINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.

</editor-fold> */

#include <vsg_abstraction/nodes/INode.h>
#include <vsg_abstraction/core/IVisitor.h>
#include <vsg_abstraction/core/Export.h>

// VSG头文件
#include <vsg/nodes/Node.h>
#include <vsg/nodes/Group.h>
#include <vsg/nodes/Transform.h>
#include <vsg/nodes/MatrixTransform.h>
#include <vsg/nodes/Geometry.h>
#include <vsg/nodes/StateGroup.h>
#include <vsg/core/Visitor.h>

namespace vsg_abstraction {

/**
 * @brief VSG节点适配器基类
 * 
 * 将VSG节点包装为抽象接口，提供统一的访问方式。
 * 这是VSG与抽象层之间的桥梁。
 */
class VSG_ABSTRACTION_DECLSPEC VSGNodeAdapter : public INode {
public:
    explicit VSGNodeAdapter(vsg::ref_ptr<vsg::Node> vsgNode);
    virtual ~VSGNodeAdapter() = default;

    // ========== 基本属性 ==========
    NodeType getNodeType() const override;
    const std::string& getName() const override;
    void setName(const std::string& name) override;
    uint64_t getNodeId() const override;

    // ========== 访问者模式 ==========
    void accept(IVisitor& visitor) override;
    void accept(IConstVisitor& visitor) const override;
    void traverse(IVisitor& visitor) override;
    void traverse(IConstVisitor& visitor) const override;

    // ========== 包围盒 ==========
    std::array<double, 6> getLocalBounds() const override;
    std::array<double, 6> getWorldBounds() const override;
    void computeBounds() override;

    // ========== 用户数据 ==========
    void setUserData(const std::string& key, void* data) override;
    void* getUserData(const std::string& key) const override;
    void removeUserData(const std::string& key) override;
    bool hasUserData(const std::string& key) const override;

    // ========== VSG兼容性接口 ==========
    vsg::ref_ptr<vsg::Node> getVSGNode() const override;
    void* getNativeHandle() const override;

    // ========== 静态工厂方法 ==========
    static ref_ptr<INode> create(vsg::ref_ptr<vsg::Node> vsgNode);

protected:
    vsg::ref_ptr<vsg::Node> vsgNode_;
    mutable std::string name_;
    mutable std::unordered_map<std::string, void*> userData_;
    mutable uint64_t nodeId_;
    
    // 辅助方法
    NodeType _determineNodeType() const;
    static uint64_t _generateNodeId();
};

/**
 * @brief VSG组节点适配器
 */
class VSG_ABSTRACTION_DECLSPEC VSGGroupAdapter : public VSGNodeAdapter, public IGroup {
public:
    explicit VSGGroupAdapter(vsg::ref_ptr<vsg::Group> vsgGroup);
    virtual ~VSGGroupAdapter() = default;

    // ========== 子节点管理 ==========
    void addChild(ref_ptr<INode> child) override;
    void removeChild(ref_ptr<INode> child) override;
    void removeChild(size_t index) override;
    void removeAllChildren() override;
    size_t getNumChildren() const override;
    ref_ptr<INode> getChild(size_t index) const override;
    const std::vector<ref_ptr<INode>>& getChildren() const override;
    ref_ptr<INode> findChild(const std::string& name) const override;

    // ========== VSG兼容性接口 ==========
    vsg::ref_ptr<vsg::Group> getVSGGroup() const override;

private:
    vsg::ref_ptr<vsg::Group> vsgGroup_;
    mutable std::vector<ref_ptr<INode>> children_;
    mutable bool childrenCacheValid_ = false;
    
    void _updateChildrenCache() const;
    void _invalidateChildrenCache();
};

/**
 * @brief VSG变换节点适配器
 */
class VSG_ABSTRACTION_DECLSPEC VSGTransformAdapter : public VSGGroupAdapter, public ITransform {
public:
    explicit VSGTransformAdapter(vsg::ref_ptr<vsg::MatrixTransform> vsgTransform);
    virtual ~VSGTransformAdapter() = default;

    // ========== 变换操作 ==========
    void setMatrix(const mat4& matrix) override;
    const mat4& getMatrix() const override;
    mat4 getWorldMatrix() const override;
    void setPosition(const vec3& position) override;
    vec3 getPosition() const override;
    void setRotation(const vec4& rotation) override;
    vec4 getRotation() const override;
    void setScale(const vec3& scale) override;
    vec3 getScale() const override;

    // ========== VSG兼容性接口 ==========
    vsg::ref_ptr<vsg::MatrixTransform> getVSGTransform() const override;

private:
    vsg::ref_ptr<vsg::MatrixTransform> vsgTransform_;
    mutable mat4 cachedMatrix_;
    mutable bool matrixCacheValid_ = false;
    
    void _updateMatrixCache() const;
    void _invalidateMatrixCache();
    mat4 _convertVSGMatrix(const vsg::dmat4& vsgMatrix) const;
    vsg::dmat4 _convertToVSGMatrix(const mat4& matrix) const;
    void _decomposeMatrix(const mat4& matrix, vec3& position, vec4& rotation, vec3& scale) const;
    mat4 _composeMatrix(const vec3& position, const vec4& rotation, const vec3& scale) const;
};

/**
 * @brief VSG几何节点适配器
 */
class VSG_ABSTRACTION_DECLSPEC VSGGeometryAdapter : public VSGNodeAdapter, public IGeometry {
public:
    explicit VSGGeometryAdapter(vsg::ref_ptr<vsg::Geometry> vsgGeometry);
    virtual ~VSGGeometryAdapter() = default;

    // ========== 几何数据 ==========
    void setVertices(const std::vector<vec3>& vertices) override;
    const std::vector<vec3>& getVertices() const override;
    void setNormals(const std::vector<vec3>& normals) override;
    const std::vector<vec3>& getNormals() const override;
    void setTexCoords(const std::vector<vec2>& texCoords) override;
    const std::vector<vec2>& getTexCoords() const override;
    void setIndices(const std::vector<uint32_t>& indices) override;
    const std::vector<uint32_t>& getIndices() const override;
    void setPrimitiveTopology(PrimitiveTopology topology) override;
    PrimitiveTopology getPrimitiveTopology() const override;

    // ========== VSG兼容性接口 ==========
    vsg::ref_ptr<vsg::Geometry> getVSGGeometry() const override;

private:
    vsg::ref_ptr<vsg::Geometry> vsgGeometry_;
    mutable std::vector<vec3> cachedVertices_;
    mutable std::vector<vec3> cachedNormals_;
    mutable std::vector<vec2> cachedTexCoords_;
    mutable std::vector<uint32_t> cachedIndices_;
    mutable bool verticesCacheValid_ = false;
    mutable bool normalsCacheValid_ = false;
    mutable bool texCoordsCacheValid_ = false;
    mutable bool indicesCacheValid_ = false;
    
    void _updateVerticesCache() const;
    void _updateNormalsCache() const;
    void _updateTexCoordsCache() const;
    void _updateIndicesCache() const;
    void _invalidateGeometryCache();
    
    PrimitiveTopology _convertVSGTopology(VkPrimitiveTopology vsgTopology) const;
    VkPrimitiveTopology _convertToVSGTopology(PrimitiveTopology topology) const;
};

/**
 * @brief VSG状态组节点适配器
 */
class VSG_ABSTRACTION_DECLSPEC VSGStateGroupAdapter : public VSGGroupAdapter, public IStateGroup {
public:
    explicit VSGStateGroupAdapter(vsg::ref_ptr<vsg::StateGroup> vsgStateGroup);
    virtual ~VSGStateGroupAdapter() = default;

    // ========== 状态管理 ==========
    void addStateCommand(ref_ptr<INode> stateCommand) override;
    void removeStateCommand(ref_ptr<INode> stateCommand) override;
    const std::vector<ref_ptr<INode>>& getStateCommands() const override;

    // ========== VSG兼容性接口 ==========
    vsg::ref_ptr<vsg::StateGroup> getVSGStateGroup() const override;

private:
    vsg::ref_ptr<vsg::StateGroup> vsgStateGroup_;
    mutable std::vector<ref_ptr<INode>> cachedStateCommands_;
    mutable bool stateCommandsCacheValid_ = false;
    
    void _updateStateCommandsCache() const;
    void _invalidateStateCommandsCache();
};

/**
 * @brief VSG访问者适配器
 * 
 * 将抽象访问者转换为VSG访问者，用于遍历VSG场景图
 */
class VSG_ABSTRACTION_DECLSPEC VSGVisitorAdapter : public vsg::Visitor {
public:
    explicit VSGVisitorAdapter(IVisitor& abstractVisitor);
    virtual ~VSGVisitorAdapter() = default;

    // ========== VSG访问者接口 ==========
    void apply(vsg::Node& node) override;
    void apply(vsg::Group& group) override;
    void apply(vsg::Transform& transform) override;
    void apply(vsg::MatrixTransform& matrixTransform) override;
    void apply(vsg::Geometry& geometry) override;
    void apply(vsg::StateGroup& stateGroup) override;

private:
    IVisitor& abstractVisitor_;
    std::unordered_map<vsg::Node*, ref_ptr<INode>> nodeAdapterCache_;
    
    ref_ptr<INode> _getOrCreateAdapter(vsg::Node& vsgNode);
};

/**
 * @brief VSG常量访问者适配器
 */
class VSG_ABSTRACTION_DECLSPEC VSGConstVisitorAdapter : public vsg::ConstVisitor {
public:
    explicit VSGConstVisitorAdapter(IConstVisitor& abstractVisitor);
    virtual ~VSGConstVisitorAdapter() = default;

    // ========== VSG常量访问者接口 ==========
    void apply(const vsg::Node& node) override;
    void apply(const vsg::Group& group) override;
    void apply(const vsg::Transform& transform) override;
    void apply(const vsg::MatrixTransform& matrixTransform) override;
    void apply(const vsg::Geometry& geometry) override;
    void apply(const vsg::StateGroup& stateGroup) override;

private:
    IConstVisitor& abstractVisitor_;
    mutable std::unordered_map<const vsg::Node*, ref_ptr<INode>> nodeAdapterCache_;
    
    ref_ptr<INode> _getOrCreateAdapter(const vsg::Node& vsgNode) const;
};

/**
 * @brief 抽象访问者到VSG访问者的适配器
 * 
 * 将VSG访问者转换为抽象访问者，用于在抽象层中使用VSG访问者
 */
class VSG_ABSTRACTION_DECLSPEC AbstractVisitorAdapter : public IVisitor {
public:
    explicit AbstractVisitorAdapter(vsg::ref_ptr<vsg::Visitor> vsgVisitor);
    virtual ~AbstractVisitorAdapter() = default;

    // ========== 抽象访问者接口 ==========
    void apply(INode& node) override;
    void apply(IGroup& group) override;
    void apply(ITransform& transform) override;
    void apply(IGeometry& geometry) override;
    void apply(IStateGroup& stateGroup) override;

    // ========== VSG兼容性接口 ==========
    vsg::ref_ptr<vsg::Visitor> getVSGVisitor() const override;

private:
    vsg::ref_ptr<vsg::Visitor> vsgVisitor_;
};

/**
 * @brief 抽象常量访问者到VSG常量访问者的适配器
 */
class VSG_ABSTRACTION_DECLSPEC AbstractConstVisitorAdapter : public IConstVisitor {
public:
    explicit AbstractConstVisitorAdapter(vsg::ref_ptr<vsg::ConstVisitor> vsgVisitor);
    virtual ~AbstractConstVisitorAdapter() = default;

    // ========== 抽象常量访问者接口 ==========
    void apply(const INode& node) override;
    void apply(const IGroup& group) override;
    void apply(const ITransform& transform) override;
    void apply(const IGeometry& geometry) override;
    void apply(const IStateGroup& stateGroup) override;

    // ========== VSG兼容性接口 ==========
    vsg::ref_ptr<vsg::ConstVisitor> getVSGConstVisitor() const override;

private:
    vsg::ref_ptr<vsg::ConstVisitor> vsgVisitor_;
};

} // namespace vsg_abstraction
