#pragma once

/* <editor-fold desc="MIT License">

Copyright(c) 2024 VSG WebGPU Extension

Permission is hereby granted, free of charge, to any person obtaining a copy of this software and associated documentation files (the "Software"), to deal in the Software without restriction, including without limitation the rights to use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of the Software, and to permit persons to whom the Software is furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.

</editor-fold> */

#include <vsg_webgpu/core/Export.h>
#include <vsg_webgpu/core/WebGPUHeaders.h>
#include <vsg/core/Inherit.h>
#include <vsg/core/Object.h>
#include <vsg/core/ref_ptr.h>
#include <vector>
#include <mutex>

namespace vsg_webgpu
{
    // forward declarations
    class Device;
    class CommandBuffer;

    /// WebGPU命令池封装，对应VSG的CommandPool类
    /// 在WebGPU中，命令编码器是直接从设备创建的，所以这个类主要用于管理命令缓冲区的生命周期
    class VSG_WEBGPU_DECLSPEC CommandPool : public vsg::Inherit<vsg::Object, CommandPool>
    {
    public:
        CommandPool(Device* device, uint32_t queueFamilyIndex = 0);

        // 对应VSG的CommandPool接口
        const uint32_t queueFamilyIndex;

        Device* getDevice() { return _device; }
        const Device* getDevice() const { return _device; }

        // 命令缓冲区分配和管理
        vsg::ref_ptr<CommandBuffer> allocate();
        void free(CommandBuffer* commandBuffer);
        void reset();

        // 批量操作
        std::vector<vsg::ref_ptr<CommandBuffer>> allocate(uint32_t count);
        void free(const std::vector<vsg::ref_ptr<CommandBuffer>>& commandBuffers);

        // 统计信息
        size_t getActiveCommandBufferCount() const;
        size_t getTotalCommandBufferCount() const;

    protected:
        virtual ~CommandPool();

    private:
        Device* _device;
        
        mutable std::mutex _mutex;
        std::vector<vsg::ref_ptr<CommandBuffer>> _activeCommandBuffers;
        std::vector<vsg::ref_ptr<CommandBuffer>> _availableCommandBuffers;

        // 内部方法
        vsg::ref_ptr<CommandBuffer> _createCommandBuffer();
    };

    VSG_type_name(vsg_webgpu::CommandPool);

} // namespace vsg_webgpu
