/* <editor-fold desc="MIT License">

Copyright(c) 2024 VSG WebGPU Extension

Permission is hereby granted, free of charge, to any person obtaining a copy of this software and associated documentation files (the "Software"), to deal in the Software without restriction, including without limitation the rights to use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of the Software, and to permit persons to whom the Software is furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.

</editor-fold> */

#include <vsg_webgpu/state/GraphicsPipeline.h>
#include <vsg_webgpu/vk/Context.h>
#include <vsg_webgpu/vk/Device.h>
#include <vsg_webgpu/vk/State.h>

using namespace vsg_webgpu;

// ShaderStage implementation
ShaderStage::ShaderStage(const std::string& wgslCode, const std::string& entryPoint) :
    wgslCode(wgslCode), entryPoint(entryPoint)
{
}

WGPUShaderModule ShaderStage::compile(Device* device) const
{
    if (!device) return {};
    
    return device->getResourceCache().getOrCreateShaderModule(
        wgslCode, // 使用代码作为key
        wgslCode
    );
}

int ShaderStage::compare(const vsg::Object& rhs_object) const
{
    auto& rhs = static_cast<const ShaderStage&>(rhs_object);
    if (wgslCode < rhs.wgslCode) return -1;
    if (wgslCode > rhs.wgslCode) return 1;
    if (entryPoint < rhs.entryPoint) return -1;
    if (entryPoint > rhs.entryPoint) return 1;
    return 0;
}

// VertexInputState implementation
VertexInputState::VertexInputState()
{
    slot = 0; // 默认槽位
}

void VertexInputState::apply(Context& context, WGPURenderPipelineDescriptor& pipelineDesc) const
{
    // 在WebGPU中，顶点输入状态通过vertex属性描述
    // 这里我们设置管线描述符的vertex部分
    static WGPUVertexState vertexState = {};
    vertexState.bufferCount = vertexBufferLayouts.size();
    vertexState.buffers = vertexBufferLayouts.data();
    
    pipelineDesc.vertex = vertexState;
}

int VertexInputState::compare(const vsg::Object& rhs_object) const
{
    auto& rhs = static_cast<const VertexInputState&>(rhs_object);
    if (vertexAttributes.size() < rhs.vertexAttributes.size()) return -1;
    if (vertexAttributes.size() > rhs.vertexAttributes.size()) return 1;
    return 0;
}

// PrimitiveState implementation
PrimitiveState::PrimitiveState()
{
    slot = 1; // 默认槽位
}

void PrimitiveState::apply(Context& context, WGPURenderPipelineDescriptor& pipelineDesc) const
{
    static WGPUPrimitiveState primitiveState = {};
    primitiveState.topology = topology;
    primitiveState.stripIndexFormat = stripIndexFormat;
    primitiveState.frontFace = frontFace;
    primitiveState.cullMode = cullMode;
    
    pipelineDesc.primitive = primitiveState;
}

int PrimitiveState::compare(const vsg::Object& rhs_object) const
{
    auto& rhs = static_cast<const PrimitiveState&>(rhs_object);
    if (topology < rhs.topology) return -1;
    if (topology > rhs.topology) return 1;
    return 0;
}

// DepthStencilState implementation
DepthStencilState::DepthStencilState()
{
    slot = 2; // 默认槽位
}

void DepthStencilState::apply(Context& context, WGPURenderPipelineDescriptor& pipelineDesc) const
{
    static WGPUDepthStencilState depthStencilState = {};
    depthStencilState.format = format;
    depthStencilState.depthWriteEnabled = depthWriteEnabled;
    depthStencilState.depthCompare = depthCompare;
    depthStencilState.stencilFront = stencilFront;
    depthStencilState.stencilBack = stencilBack;
    depthStencilState.stencilReadMask = stencilReadMask;
    depthStencilState.stencilWriteMask = stencilWriteMask;
    
    pipelineDesc.depthStencil = &depthStencilState;
}

int DepthStencilState::compare(const vsg::Object& rhs_object) const
{
    auto& rhs = static_cast<const DepthStencilState&>(rhs_object);
    if (format < rhs.format) return -1;
    if (format > rhs.format) return 1;
    return 0;
}

// MultisampleState implementation
MultisampleState::MultisampleState()
{
    slot = 3; // 默认槽位
}

void MultisampleState::apply(Context& context, WGPURenderPipelineDescriptor& pipelineDesc) const
{
    static WGPUMultisampleState multisampleState = {};
    multisampleState.count = count;
    multisampleState.mask = mask;
    multisampleState.alphaToCoverageEnabled = alphaToCoverageEnabled;
    
    pipelineDesc.multisample = multisampleState;
}

int MultisampleState::compare(const vsg::Object& rhs_object) const
{
    auto& rhs = static_cast<const MultisampleState&>(rhs_object);
    if (count < rhs.count) return -1;
    if (count > rhs.count) return 1;
    return 0;
}

// ColorBlendState implementation
ColorBlendState::ColorBlendState()
{
    slot = 4; // 默认槽位
    
    // 默认颜色目标
    WGPUColorTargetState defaultTarget = {};
    defaultTarget.format = WGPUTextureFormat_BGRA8Unorm;
    defaultTarget.writeMask = WGPUColorWriteMask_All;
    targets.push_back(defaultTarget);
}

void ColorBlendState::apply(Context& context, WGPURenderPipelineDescriptor& pipelineDesc) const
{
    static WGPUFragmentState fragmentState = {};
    fragmentState.targetCount = targets.size();
    fragmentState.targets = targets.data();
    
    pipelineDesc.fragment = &fragmentState;
}

int ColorBlendState::compare(const vsg::Object& rhs_object) const
{
    auto& rhs = static_cast<const ColorBlendState&>(rhs_object);
    if (targets.size() < rhs.targets.size()) return -1;
    if (targets.size() > rhs.targets.size()) return 1;
    return 0;
}

// GraphicsPipeline implementation
GraphicsPipeline::GraphicsPipeline()
{
}

GraphicsPipeline::GraphicsPipeline(const ShaderStages& shaderStages, 
                                  const GraphicsPipelineStates& pipelineStates) :
    stages(shaderStages), pipelineStates(pipelineStates)
{
}

GraphicsPipeline::~GraphicsPipeline()
{
    release();
}

WGPURenderPipeline GraphicsPipeline::getPipeline(uint32_t viewID) const
{
    if (viewID < _implementations.size() && _implementations[viewID])
    {
        return _implementations[viewID]->pipeline;
    }
    return {};
}

void GraphicsPipeline::compile(Context& context)
{
    // 确保有足够的实现槽位
    if (_implementations.size() <= context.viewID)
    {
        _implementations.resize(context.viewID + 1);
    }
    
    // 创建管线描述符
    WGPURenderPipelineDescriptor pipelineDesc = {};
    pipelineDesc.label = "VSG WebGPU Graphics Pipeline";
    
    // 应用所有管线状态
    for (auto& state : pipelineStates)
    {
        if (state)
        {
            state->apply(context, pipelineDesc);
        }
    }
    
    // 编译着色器
    std::vector<WGPUShaderModule> shaderModules;
    for (auto& stage : stages)
    {
        if (stage && context.device)
        {
            auto module = stage->compile(context.device);
            if (module)
            {
                shaderModules.push_back(module);
            }
        }
    }
    
    // 创建管线
    auto pipeline = context.createRenderPipeline(pipelineDesc);
    
    // 存储实现
    auto impl = std::make_unique<Implementation>();
    impl->pipeline = pipeline;
    impl->device = context.device;
    _implementations[context.viewID] = std::move(impl);
}

void GraphicsPipeline::release(uint32_t viewID)
{
    if (viewID < _implementations.size())
    {
        _implementations[viewID].reset();
    }
}

void GraphicsPipeline::release()
{
    _implementations.clear();
}

int GraphicsPipeline::compare(const vsg::Object& rhs_object) const
{
    auto& rhs = static_cast<const GraphicsPipeline&>(rhs_object);
    if (stages.size() < rhs.stages.size()) return -1;
    if (stages.size() > rhs.stages.size()) return 1;
    if (pipelineStates.size() < rhs.pipelineStates.size()) return -1;
    if (pipelineStates.size() > rhs.pipelineStates.size()) return 1;
    return 0;
}

// BindGraphicsPipeline implementation
BindGraphicsPipeline::BindGraphicsPipeline(vsg::ref_ptr<GraphicsPipeline> pipeline) :
    pipeline(pipeline)
{
    slot = 0; // 管线绑定通常在槽位0
}

int BindGraphicsPipeline::compare(const vsg::Object& rhs_object) const
{
    auto& rhs = static_cast<const BindGraphicsPipeline&>(rhs_object);
    if (pipeline < rhs.pipeline) return -1;
    if (pipeline > rhs.pipeline) return 1;
    return 0;
}

void BindGraphicsPipeline::record(vsg::CommandBuffer& commandBuffer) const
{
    // VSG CommandBuffer接口 - 这里主要是为了兼容
}

void BindGraphicsPipeline::record(State& state) const
{
    if (pipeline)
    {
        auto webgpuPipeline = pipeline->getPipeline(state.viewID);
        if (webgpuPipeline)
        {
            state.getBoundResources().renderPipeline = webgpuPipeline;
            state.dirty = true;
        }
    }
}
