# Rocky渲染引擎解耦重构使用指南

## 1. 快速开始

### 1.1 基本使用

#### 初始化渲染引擎
```cpp
#include <rocky/render/RenderEngineManager.h>

// 获取渲染引擎管理器
auto& manager = rocky::render::getRenderManager();

// 设置渲染后端（自动选择最佳后端）
if (!manager.switchBackend(rocky::render::RenderBackend::Vulkan)) {
    // 如果Vulkan不可用，尝试其他后端
    manager.switchBackend(rocky::render::RenderBackend::WebGPU);
}

// 或者使用默认后端
auto engine = rocky::render::RenderEngineFactory::createDefault();
manager.setEngine(std::move(engine));
```

#### 创建窗口和场景
```cpp
// 创建窗口
rocky::render::WindowConfig windowConfig;
windowConfig.title = "Rocky Application";
windowConfig.width = 1920;
windowConfig.height = 1080;
windowConfig.vsync = true;

auto window = manager.createWindow(windowConfig);

// 创建场景图
auto sceneRoot = manager.createSceneRoot();

// 创建渲染图
auto renderGraph = manager.createRenderGraph(window);
```

#### 基本渲染循环
```cpp
// 渲染循环
while (window->isValid()) {
    // 处理事件
    window->pollEvents();
    
    // 更新场景
    updateScene(sceneRoot);
    
    // 渲染
    if (!manager.render(renderGraph)) {
        break;
    }
    
    // 呈现
    window->present();
}
```

### 1.2 便捷宏使用

#### 安全获取渲染引擎
```cpp
void myRenderFunction() {
    ROCKY_GET_RENDER_ENGINE(); // 自动获取并检查引擎
    
    // 使用engine指针
    auto texture = engine->createTexture(textureConfig);
}

// 带返回值的版本
std::shared_ptr<ITexture> createMyTexture() {
    ROCKY_GET_RENDER_ENGINE_OR_RETURN(nullptr);
    
    return engine->createTexture(config);
}
```

## 2. 场景图操作

### 2.1 创建场景节点

#### 基本节点类型
```cpp
// 创建组节点
auto group = manager.getEngine()->createGroup();

// 创建变换节点
auto transform = manager.getEngine()->createTransform();

// 创建几何节点
rocky::render::GeometryData geometry;
geometry.vertices = {
    0.0f, 0.5f, 0.0f,   // 顶点1
    -0.5f, -0.5f, 0.0f, // 顶点2
    0.5f, -0.5f, 0.0f   // 顶点3
};
geometry.indices = {0, 1, 2};
auto geometryNode = manager.getEngine()->createGeometry(geometry);

// 创建状态组节点
auto stateGroup = manager.getEngine()->createStateGroup();
```

#### 构建场景层次
```cpp
// 构建场景层次结构
auto root = manager.createSceneRoot();
auto transform = manager.getEngine()->createTransform();
auto geometry = manager.getEngine()->createGeometry(geometryData);

// 设置变换
rocky::render::Mat4 matrix = {
    {{1.0f, 0.0f, 0.0f, 0.0f},
     {0.0f, 1.0f, 0.0f, 0.0f},
     {0.0f, 0.0f, 1.0f, 0.0f},
     {0.0f, 0.0f, 0.0f, 1.0f}}
};
transform->setTransform(matrix);

// 建立父子关系
transform->addChild(geometry);
root->addChild(transform);
```

### 2.2 场景节点属性

#### 变换操作
```cpp
// 设置位置
transform->setPosition({1.0f, 2.0f, 3.0f});

// 设置旋转（四元数）
transform->setRotation({0.0f, 0.0f, 0.0f, 1.0f});

// 设置缩放
transform->setScale({2.0f, 2.0f, 2.0f});

// 获取世界变换矩阵
auto worldMatrix = transform->getWorldTransform();
```

#### 可见性控制
```cpp
// 设置可见性
node->setVisible(true);

// 设置启用状态
node->setEnabled(true);

// 检查状态
if (node->isVisible() && node->isEnabled()) {
    // 节点可见且启用
}
```

#### 用户数据
```cpp
// 设置用户数据
MyCustomData* data = new MyCustomData();
node->setUserData("custom_data", data);

// 获取用户数据
auto* retrievedData = static_cast<MyCustomData*>(
    node->getUserData("custom_data"));

// 移除用户数据
node->removeUserData("custom_data");
delete data;
```

## 3. 资源管理

### 3.1 纹理管理

#### 创建纹理
```cpp
// 纹理配置
rocky::render::TextureConfig textureConfig;
textureConfig.width = 512;
textureConfig.height = 512;
textureConfig.format = rocky::render::TextureFormat::RGBA8;
textureConfig.generateMipmaps = true;

// 创建纹理
auto texture = manager.getEngine()->createTexture(textureConfig);

// 带初始数据的纹理
std::vector<uint8_t> imageData(512 * 512 * 4, 255); // 白色纹理
textureConfig.initialData = imageData.data();
textureConfig.dataSize = imageData.size();
auto textureWithData = manager.getEngine()->createTexture(textureConfig);
```

#### 纹理绑定
```cpp
// 在状态组中绑定纹理
auto stateGroup = std::dynamic_pointer_cast<rocky::render::IStateGroupNode>(
    manager.getEngine()->createStateGroup());

stateGroup->setTexture(0, texture); // 绑定到槽位0
```

### 3.2 缓冲区管理

#### 创建缓冲区
```cpp
// 顶点缓冲区
rocky::render::BufferConfig vertexConfig;
vertexConfig.type = rocky::render::BufferType::Vertex;
vertexConfig.size = vertices.size() * sizeof(float);
vertexConfig.initialData = vertices.data();
auto vertexBuffer = manager.getEngine()->createBuffer(vertexConfig);

// 索引缓冲区
rocky::render::BufferConfig indexConfig;
indexConfig.type = rocky::render::BufferType::Index;
indexConfig.size = indices.size() * sizeof(uint32_t);
indexConfig.initialData = indices.data();
auto indexBuffer = manager.getEngine()->createBuffer(indexConfig);

// 统一缓冲区
rocky::render::BufferConfig uniformConfig;
uniformConfig.type = rocky::render::BufferType::Uniform;
uniformConfig.size = sizeof(UniformData);
uniformConfig.dynamic = true; // 动态更新
auto uniformBuffer = manager.getEngine()->createBuffer(uniformConfig);
```

### 3.3 着色器和管线

#### 创建着色器
```cpp
// 顶点着色器
rocky::render::ShaderConfig vertexShaderConfig;
vertexShaderConfig.type = rocky::render::ShaderType::Vertex;
vertexShaderConfig.source = R"(
    #version 450
    layout(location = 0) in vec3 position;
    layout(location = 1) in vec2 texCoord;
    
    layout(location = 0) out vec2 fragTexCoord;
    
    void main() {
        gl_Position = vec4(position, 1.0);
        fragTexCoord = texCoord;
    }
)";
auto vertexShader = manager.getEngine()->createShader(vertexShaderConfig);

// 片段着色器
rocky::render::ShaderConfig fragmentShaderConfig;
fragmentShaderConfig.type = rocky::render::ShaderType::Fragment;
fragmentShaderConfig.source = R"(
    #version 450
    layout(location = 0) in vec2 fragTexCoord;
    layout(location = 0) out vec4 fragColor;
    
    layout(binding = 0) uniform sampler2D texSampler;
    
    void main() {
        fragColor = texture(texSampler, fragTexCoord);
    }
)";
auto fragmentShader = manager.getEngine()->createShader(fragmentShaderConfig);
```

#### 创建渲染管线
```cpp
// 管线配置
rocky::render::PipelineConfig pipelineConfig;
pipelineConfig.shaders = {vertexShaderConfig, fragmentShaderConfig};
pipelineConfig.primitiveType = rocky::render::PrimitiveType::Triangles;
pipelineConfig.depthTest = true;
pipelineConfig.depthWrite = true;
pipelineConfig.blending = false;

auto pipeline = manager.getEngine()->createPipeline(pipelineConfig);

// 在状态组中设置管线
stateGroup->setPipeline(pipeline);
```

## 4. 多后端支持

### 4.1 后端切换

#### 运行时切换
```cpp
// 检查支持的后端
auto supportedBackends = rocky::render::RenderEngineFactory::getSupportedBackends();
for (auto backend : supportedBackends) {
    std::cout << "Supported: " << rocky::render::getRenderBackendName(backend) << std::endl;
}

// 获取推荐后端
auto recommended = rocky::render::RenderEngineFactory::getRecommendedBackend();

// 切换后端
if (manager.switchBackend(rocky::render::RenderBackend::WebGPU)) {
    std::cout << "Switched to WebGPU backend" << std::endl;
} else {
    std::cout << "Failed to switch to WebGPU backend" << std::endl;
}
```

#### 后端特定配置
```cpp
// Vulkan特定配置
struct VulkanConfig {
    bool enableValidationLayers = true;
    std::vector<const char*> requiredExtensions;
};

VulkanConfig vulkanConfig;
vulkanConfig.enableValidationLayers = true;

auto vulkanEngine = rocky::render::RenderEngineFactory::create(
    rocky::render::RenderBackend::Vulkan, &vulkanConfig);
```

### 4.2 平台适配

#### Web平台
```cpp
#ifdef ROCKY_PLATFORM_EMSCRIPTEN
// Web平台只支持WebGPU
manager.switchBackend(rocky::render::RenderBackend::WebGPU);
#else
// 桌面平台优先使用Vulkan
if (!manager.switchBackend(rocky::render::RenderBackend::Vulkan)) {
    manager.switchBackend(rocky::render::RenderBackend::WebGPU);
}
#endif
```

#### 移动平台
```cpp
#ifdef ROCKY_PLATFORM_ANDROID
// Android平台使用Vulkan或OpenGL
if (!manager.switchBackend(rocky::render::RenderBackend::Vulkan)) {
    manager.switchBackend(rocky::render::RenderBackend::OpenGL);
}
#elif defined(ROCKY_PLATFORM_IOS)
// iOS平台使用Metal（通过WebGPU）
manager.switchBackend(rocky::render::RenderBackend::WebGPU);
#endif
```

## 5. 调试和性能

### 5.1 调试功能

#### 调试回调
```cpp
// 设置调试回调
manager.setDebugCallback([](const std::string& message, int severity) {
    std::cout << "[DEBUG] " << message << std::endl;
});

// 设置错误回调
manager.setErrorCallback([](const std::string& error) {
    std::cerr << "[ERROR] " << error << std::endl;
});
```

#### 调试标记
```cpp
// 开始调试标记
manager.beginProfileMarker("Render Scene");

// 渲染操作
manager.render(renderGraph);

// 结束调试标记
manager.endProfileMarker();
```

### 5.2 性能监控

#### 渲染统计
```cpp
// 获取渲染统计信息
auto stats = manager.getRenderStatistics();
std::cout << "Frame Count: " << stats.frameCount << std::endl;
std::cout << "Draw Calls: " << stats.drawCalls << std::endl;
std::cout << "Triangles: " << stats.triangles << std::endl;
std::cout << "Frame Time: " << stats.frameTime << "ms" << std::endl;
std::cout << "Memory Used: " << stats.memoryUsed << " bytes" << std::endl;

// 重置统计信息
manager.resetRenderStatistics();
```

#### 性能分析
```cpp
// 性能分析示例
class PerformanceProfiler {
public:
    void beginFrame() {
        frameStart_ = std::chrono::high_resolution_clock::now();
    }
    
    void endFrame() {
        auto frameEnd = std::chrono::high_resolution_clock::now();
        auto duration = std::chrono::duration_cast<std::chrono::microseconds>(
            frameEnd - frameStart_);
        
        frameTime_ = duration.count() / 1000.0; // 转换为毫秒
        fps_ = 1000.0 / frameTime_;
    }
    
    double getFrameTime() const { return frameTime_; }
    double getFPS() const { return fps_; }
    
private:
    std::chrono::high_resolution_clock::time_point frameStart_;
    double frameTime_ = 0.0;
    double fps_ = 0.0;
};
```

## 6. 最佳实践

### 6.1 资源管理最佳实践

#### 资源生命周期
```cpp
// 好的做法：使用RAII管理资源
class TextureManager {
public:
    std::shared_ptr<rocky::render::ITexture> loadTexture(const std::string& path) {
        auto it = textures_.find(path);
        if (it != textures_.end()) {
            return it->second; // 返回缓存的纹理
        }
        
        // 加载新纹理
        auto texture = createTextureFromFile(path);
        textures_[path] = texture;
        return texture;
    }
    
private:
    std::unordered_map<std::string, std::shared_ptr<rocky::render::ITexture>> textures_;
};
```

#### 内存优化
```cpp
// 预分配资源池
class GeometryPool {
public:
    std::shared_ptr<rocky::render::ISceneNode> acquireGeometry() {
        if (!available_.empty()) {
            auto geometry = available_.back();
            available_.pop_back();
            return geometry;
        }
        
        return manager.getEngine()->createGeometry({});
    }
    
    void releaseGeometry(std::shared_ptr<rocky::render::ISceneNode> geometry) {
        // 重置几何体状态
        geometry->setVisible(false);
        available_.push_back(geometry);
    }
    
private:
    std::vector<std::shared_ptr<rocky::render::ISceneNode>> available_;
};
```

### 6.2 错误处理

#### 异常安全
```cpp
// 异常安全的资源管理
class SafeRenderer {
public:
    bool render() {
        try {
            ROCKY_GET_RENDER_ENGINE_OR_RETURN(false);
            
            // 渲染操作
            return engine->render(renderGraph_);
        }
        catch (const std::exception& e) {
            handleError("Render failed: " + std::string(e.what()));
            return false;
        }
    }
    
private:
    void handleError(const std::string& error) {
        // 记录错误
        std::cerr << error << std::endl;
        
        // 尝试恢复
        if (manager.getCurrentBackend() != rocky::render::RenderBackend::Mock) {
            manager.switchBackend(rocky::render::RenderBackend::Mock);
        }
    }
};
```

### 6.3 多线程使用

#### 线程安全
```cpp
// 线程安全的渲染器
class ThreadSafeRenderer {
public:
    void updateScene(std::shared_ptr<rocky::render::ISceneNode> scene) {
        std::lock_guard<std::mutex> lock(sceneMutex_);
        pendingScene_ = scene;
        sceneUpdated_ = true;
    }
    
    void render() {
        // 在渲染线程中
        std::unique_lock<std::mutex> lock(sceneMutex_);
        if (sceneUpdated_) {
            currentScene_ = pendingScene_;
            sceneUpdated_ = false;
        }
        lock.unlock();
        
        if (currentScene_) {
            manager.render(renderGraph_);
        }
    }
    
private:
    std::mutex sceneMutex_;
    std::shared_ptr<rocky::render::ISceneNode> currentScene_;
    std::shared_ptr<rocky::render::ISceneNode> pendingScene_;
    bool sceneUpdated_ = false;
};
```

## 7. 常见问题

### 7.1 初始化问题

**Q: 渲染引擎初始化失败怎么办？**
```cpp
A: 检查后端支持和降级策略
auto backends = rocky::render::RenderEngineFactory::getBackendPriority();
for (auto backend : backends) {
    if (manager.switchBackend(backend)) {
        break; // 成功初始化
    }
}
```

**Q: 如何检查特定后端是否可用？**
```cpp
A: 使用工厂类检查
if (rocky::render::RenderEngineFactory::isBackendSupported(
        rocky::render::RenderBackend::Vulkan)) {
    // Vulkan可用
}
```

### 7.2 性能问题

**Q: 渲染性能下降怎么办？**
```cpp
A: 检查统计信息和优化
auto stats = manager.getRenderStatistics();
if (stats.drawCalls > 1000) {
    // 考虑批处理优化
}
if (stats.frameTime > 16.67) {
    // 帧时间超过60fps，需要优化
}
```

### 7.3 兼容性问题

**Q: 如何处理不同后端的行为差异？**
```cpp
A: 使用抽象接口和适配器
class BackendAdapter {
public:
    static void normalizeTexture(std::shared_ptr<rocky::render::ITexture> texture,
                                rocky::render::RenderBackend backend) {
        switch (backend) {
            case rocky::render::RenderBackend::OpenGL:
                // OpenGL特定处理
                break;
            case rocky::render::RenderBackend::Vulkan:
                // Vulkan特定处理
                break;
            // ...
        }
    }
};
```

这个使用指南提供了完整的API使用方法和最佳实践，帮助开发者快速上手Rocky渲染引擎的解耦重构版本。
