# VSG Render Engine Abstraction CMake Configuration File

@PACKAGE_INIT@

# 版本信息
set(VSGAbstraction_VERSION "@PROJECT_VERSION@")
set(VSGAbstraction_VERSION_MAJOR "@PROJECT_VERSION_MAJOR@")
set(VSGAbstraction_VERSION_MINOR "@PROJECT_VERSION_MINOR@")
set(VSGAbstraction_VERSION_PATCH "@PROJECT_VERSION_PATCH@")

# 后端支持信息
set(VSGAbstraction_HAS_VSG @VSG_ABSTRACTION_HAS_VSG@)
set(VSGAbstraction_HAS_WEBGPU @VSG_ABSTRACTION_HAS_WEBGPU@)
set(VSGAbstraction_HAS_OPENGL @VSG_ABSTRACTION_HAS_OPENGL@)
set(VSGAbstraction_HAS_MOCK @VSG_ABSTRACTION_HAS_MOCK@)

# 构建配置
set(VSGAbstraction_SHARED_LIBS @VSG_ABSTRACTION_BUILD_SHARED_LIBS@)

# 检查依赖
include(CMakeFindDependencyMacro)

# 线程库依赖
find_dependency(Threads REQUIRED)

# VSG依赖（如果启用）
if(VSGAbstraction_HAS_VSG)
    find_dependency(vsg)
endif()

# OpenGL依赖（如果启用）
if(VSGAbstraction_HAS_OPENGL)
    find_dependency(OpenGL)
endif()

# 包含目标文件
include("${CMAKE_CURRENT_LIST_DIR}/VSGAbstractionTargets.cmake")

# 检查目标是否存在
check_required_components(VSGAbstraction)

# 设置变量
set(VSGAbstraction_LIBRARIES VSGAbstraction::VSGAbstraction)
set(VSGAbstraction_INCLUDE_DIRS "${PACKAGE_PREFIX_DIR}/include")

# 兼容性变量
set(VSGABSTRACTION_FOUND TRUE)
set(VSGABSTRACTION_VERSION ${VSGAbstraction_VERSION})
set(VSGABSTRACTION_LIBRARIES ${VSGAbstraction_LIBRARIES})
set(VSGABSTRACTION_INCLUDE_DIRS ${VSGAbstraction_INCLUDE_DIRS})

# 显示配置信息
if(NOT VSGAbstraction_FIND_QUIETLY)
    message(STATUS "Found VSG Render Engine Abstraction: ${VSGAbstraction_VERSION}")
    message(STATUS "  Include directories: ${VSGAbstraction_INCLUDE_DIRS}")
    message(STATUS "  Libraries: ${VSGAbstraction_LIBRARIES}")
    message(STATUS "  Backend support:")
    message(STATUS "    VSG (Vulkan): ${VSGAbstraction_HAS_VSG}")
    message(STATUS "    WebGPU: ${VSGAbstraction_HAS_WEBGPU}")
    message(STATUS "    OpenGL: ${VSGAbstraction_HAS_OPENGL}")
    message(STATUS "    Mock: ${VSGAbstraction_HAS_MOCK}")
endif()

# 辅助宏
macro(vsg_abstraction_check_backend backend)
    string(TOUPPER ${backend} backend_upper)
    if(NOT VSGAbstraction_HAS_${backend_upper})
        message(FATAL_ERROR "VSG Abstraction was not built with ${backend} backend support")
    endif()
endmacro()

# 使用示例：
# find_package(VSGAbstraction REQUIRED)
# vsg_abstraction_check_backend(VSG)
# target_link_libraries(my_target ${VSGAbstraction_LIBRARIES})
