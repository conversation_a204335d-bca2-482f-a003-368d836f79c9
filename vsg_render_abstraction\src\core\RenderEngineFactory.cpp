/* <editor-fold desc="MIT License">

Copyright(c) 2024 VSG Render Engine Abstraction

Permission is hereby granted, free of charge, to any person obtaining a copy of this software and associated documentation files (the "Software"), to deal in the Software without restriction, including without limitation the rights to use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of the Software, and to permit persons to whom the Software is furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.

</editor-fold> */

#include <vsg_abstraction/core/RenderEngineFactory.h>
#include <vsg_abstraction/core/Config.h>

// 包含各个后端的头文件
#ifdef VSG_ABSTRACTION_HAS_VSG
#include <vsg_abstraction/backends/VSGRenderEngine.h>
#endif

#ifdef VSG_ABSTRACTION_HAS_WEBGPU
#include <vsg_abstraction/backends/WebGPURenderEngine.h>
#endif

#ifdef VSG_ABSTRACTION_HAS_OPENGL
#include <vsg_abstraction/backends/OpenGLRenderEngine.h>
#endif

#ifdef VSG_ABSTRACTION_HAS_MOCK
#include <vsg_abstraction/backends/MockRenderEngine.h>
#endif

#include <iostream>
#include <algorithm>

namespace vsg_abstraction {

// ========== RenderEngineFactory Implementation ==========

std::unique_ptr<IRenderEngine> RenderEngineFactory::create(RenderBackend backend, const RenderEngineConfig* config) {
    try {
        switch (backend) {
        case RenderBackend::Vulkan:
            return createVulkanEngine(config);
            
        case RenderBackend::WebGPU:
            return createWebGPUEngine(config);
            
        case RenderBackend::OpenGL:
            return createOpenGLEngine(config);
            
        case RenderBackend::Mock:
            return createMockEngine(config);
            
        default:
            std::cerr << "[VSG_ABSTRACTION][ERROR] Unknown render backend: " 
                      << static_cast<int>(backend) << std::endl;
            return nullptr;
        }
    } catch (const std::exception& e) {
        std::cerr << "[VSG_ABSTRACTION][ERROR] Exception creating render engine: " 
                  << e.what() << std::endl;
        return nullptr;
    }
}

std::unique_ptr<IRenderEngine> RenderEngineFactory::createVulkanEngine(const RenderEngineConfig* config) {
#ifdef VSG_ABSTRACTION_HAS_VSG
    if (isBackendSupported(RenderBackend::Vulkan)) {
        try {
            return std::make_unique<VSGRenderEngine>(config);
        } catch (const std::exception& e) {
            std::cerr << "[VSG_ABSTRACTION][ERROR] Failed to create VSG render engine: " 
                      << e.what() << std::endl;
        }
    } else {
        std::cerr << "[VSG_ABSTRACTION][ERROR] VSG backend not available" << std::endl;
    }
#else
    (void)config; // 避免未使用参数警告
    std::cerr << "[VSG_ABSTRACTION][ERROR] VSG backend not compiled in" << std::endl;
#endif
    return nullptr;
}

std::unique_ptr<IRenderEngine> RenderEngineFactory::createWebGPUEngine(const RenderEngineConfig* config) {
#ifdef VSG_ABSTRACTION_HAS_WEBGPU
    if (isBackendSupported(RenderBackend::WebGPU)) {
        try {
            return std::make_unique<WebGPURenderEngine>(config);
        } catch (const std::exception& e) {
            std::cerr << "[VSG_ABSTRACTION][ERROR] Failed to create WebGPU render engine: " 
                      << e.what() << std::endl;
        }
    } else {
        std::cerr << "[VSG_ABSTRACTION][ERROR] WebGPU backend not available" << std::endl;
    }
#else
    (void)config; // 避免未使用参数警告
    std::cerr << "[VSG_ABSTRACTION][ERROR] WebGPU backend not compiled in" << std::endl;
#endif
    return nullptr;
}

std::unique_ptr<IRenderEngine> RenderEngineFactory::createOpenGLEngine(const RenderEngineConfig* config) {
#ifdef VSG_ABSTRACTION_HAS_OPENGL
    if (isBackendSupported(RenderBackend::OpenGL)) {
        try {
            return std::make_unique<OpenGLRenderEngine>(config);
        } catch (const std::exception& e) {
            std::cerr << "[VSG_ABSTRACTION][ERROR] Failed to create OpenGL render engine: " 
                      << e.what() << std::endl;
        }
    } else {
        std::cerr << "[VSG_ABSTRACTION][ERROR] OpenGL backend not available" << std::endl;
    }
#else
    (void)config; // 避免未使用参数警告
    std::cerr << "[VSG_ABSTRACTION][ERROR] OpenGL backend not compiled in" << std::endl;
#endif
    return nullptr;
}

std::unique_ptr<IRenderEngine> RenderEngineFactory::createMockEngine(const RenderEngineConfig* config) {
#ifdef VSG_ABSTRACTION_HAS_MOCK
    try {
        return std::make_unique<MockRenderEngine>(config);
    } catch (const std::exception& e) {
        std::cerr << "[VSG_ABSTRACTION][ERROR] Failed to create Mock render engine: " 
                  << e.what() << std::endl;
    }
#else
    (void)config; // 避免未使用参数警告
    std::cerr << "[VSG_ABSTRACTION][ERROR] Mock backend not compiled in" << std::endl;
#endif
    return nullptr;
}

bool RenderEngineFactory::isBackendSupported(RenderBackend backend) {
    switch (backend) {
    case RenderBackend::Vulkan:
#ifdef VSG_ABSTRACTION_HAS_VSG
        return true;
#else
        return false;
#endif
        
    case RenderBackend::WebGPU:
#ifdef VSG_ABSTRACTION_HAS_WEBGPU
        return true;
#else
        return false;
#endif
        
    case RenderBackend::OpenGL:
#ifdef VSG_ABSTRACTION_HAS_OPENGL
        return true;
#else
        return false;
#endif
        
    case RenderBackend::Mock:
#ifdef VSG_ABSTRACTION_HAS_MOCK
        return true;
#else
        return false;
#endif
        
    default:
        return false;
    }
}

std::vector<RenderBackend> RenderEngineFactory::getSupportedBackends() {
    std::vector<RenderBackend> backends;
    
    // 按优先级顺序添加支持的后端
#ifdef VSG_ABSTRACTION_HAS_VSG
    backends.push_back(RenderBackend::Vulkan);
#endif

#ifdef VSG_ABSTRACTION_HAS_WEBGPU
    backends.push_back(RenderBackend::WebGPU);
#endif

#ifdef VSG_ABSTRACTION_HAS_OPENGL
    backends.push_back(RenderBackend::OpenGL);
#endif

#ifdef VSG_ABSTRACTION_HAS_MOCK
    backends.push_back(RenderBackend::Mock);
#endif

    return backends;
}

RenderBackend RenderEngineFactory::getRecommendedBackend() {
    auto supportedBackends = getSupportedBackends();
    
    if (supportedBackends.empty()) {
        std::cerr << "[VSG_ABSTRACTION][ERROR] No supported backends available" << std::endl;
        return RenderBackend::Mock; // 默认返回Mock
    }
    
    // 优先级顺序：Vulkan > WebGPU > OpenGL > Mock
    std::vector<RenderBackend> preferenceOrder = {
        RenderBackend::Vulkan,
        RenderBackend::WebGPU,
        RenderBackend::OpenGL,
        RenderBackend::Mock
    };
    
    for (auto preferred : preferenceOrder) {
        auto it = std::find(supportedBackends.begin(), supportedBackends.end(), preferred);
        if (it != supportedBackends.end()) {
            return preferred;
        }
    }
    
    // 如果没有找到首选后端，返回第一个可用的
    return supportedBackends[0];
}

std::string RenderEngineFactory::getBackendDescription(RenderBackend backend) {
    switch (backend) {
    case RenderBackend::Vulkan:
        return "Vulkan/VSG - High-performance graphics API with VulkanSceneGraph";
        
    case RenderBackend::WebGPU:
        return "WebGPU - Modern cross-platform graphics API";
        
    case RenderBackend::OpenGL:
        return "OpenGL - Traditional cross-platform graphics API";
        
    case RenderBackend::Mock:
        return "Mock - Testing and debugging backend";
        
    default:
        return "Unknown backend";
    }
}

bool RenderEngineFactory::isBackendAvailable(RenderBackend backend) {
    if (!isBackendSupported(backend)) {
        return false;
    }
    
    // 对于某些后端，可能需要运行时检查
    switch (backend) {
    case RenderBackend::Vulkan:
#ifdef VSG_ABSTRACTION_HAS_VSG
        // 可以在这里添加Vulkan运行时可用性检查
        // 例如检查Vulkan驱动程序是否可用
        return true;
#else
        return false;
#endif
        
    case RenderBackend::WebGPU:
#ifdef VSG_ABSTRACTION_HAS_WEBGPU
        // 可以在这里添加WebGPU运行时可用性检查
        return true;
#else
        return false;
#endif
        
    case RenderBackend::OpenGL:
#ifdef VSG_ABSTRACTION_HAS_OPENGL
        // 可以在这里添加OpenGL运行时可用性检查
        return true;
#else
        return false;
#endif
        
    case RenderBackend::Mock:
#ifdef VSG_ABSTRACTION_HAS_MOCK
        return true;
#else
        return false;
#endif
        
    default:
        return false;
    }
}

std::vector<std::string> RenderEngineFactory::getBackendRequirements(RenderBackend backend) {
    std::vector<std::string> requirements;
    
    switch (backend) {
    case RenderBackend::Vulkan:
        requirements.push_back("Vulkan 1.0+ compatible graphics driver");
        requirements.push_back("VulkanSceneGraph library");
        requirements.push_back("Vulkan SDK (for development)");
        break;
        
    case RenderBackend::WebGPU:
        requirements.push_back("WebGPU compatible browser or native implementation");
        requirements.push_back("Dawn or wgpu-native library");
        break;
        
    case RenderBackend::OpenGL:
        requirements.push_back("OpenGL 3.3+ compatible graphics driver");
        requirements.push_back("OpenGL development libraries");
        break;
        
    case RenderBackend::Mock:
        requirements.push_back("No special requirements (testing only)");
        break;
        
    default:
        requirements.push_back("Unknown requirements");
        break;
    }
    
    return requirements;
}

void RenderEngineFactory::printBackendInfo() {
    std::cout << "VSG Render Engine Abstraction - Backend Information" << std::endl;
    std::cout << "====================================================" << std::endl;
    std::cout << std::endl;
    
    auto allBackends = {
        RenderBackend::Vulkan,
        RenderBackend::WebGPU,
        RenderBackend::OpenGL,
        RenderBackend::Mock
    };
    
    for (auto backend : allBackends) {
        std::cout << "Backend: " << getRenderBackendName(backend) << std::endl;
        std::cout << "  Description: " << getBackendDescription(backend) << std::endl;
        std::cout << "  Supported: " << (isBackendSupported(backend) ? "Yes" : "No") << std::endl;
        std::cout << "  Available: " << (isBackendAvailable(backend) ? "Yes" : "No") << std::endl;
        
        auto requirements = getBackendRequirements(backend);
        std::cout << "  Requirements:" << std::endl;
        for (const auto& req : requirements) {
            std::cout << "    - " << req << std::endl;
        }
        std::cout << std::endl;
    }
    
    auto supportedBackends = getSupportedBackends();
    std::cout << "Supported backends: ";
    for (size_t i = 0; i < supportedBackends.size(); ++i) {
        if (i > 0) std::cout << ", ";
        std::cout << getRenderBackendName(supportedBackends[i]);
    }
    std::cout << std::endl;
    
    auto recommended = getRecommendedBackend();
    std::cout << "Recommended backend: " << getRenderBackendName(recommended) << std::endl;
}

} // namespace vsg_abstraction
