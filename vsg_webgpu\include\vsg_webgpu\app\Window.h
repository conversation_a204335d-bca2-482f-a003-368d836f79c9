#pragma once

/* <editor-fold desc="MIT License">

Copyright(c) 2024 VSG WebGPU Extension

Permission is hereby granted, free of charge, to any person obtaining a copy of this software and associated documentation files (the "Software"), to deal in the Software without restriction, including without limitation the rights to use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of the Software, and to permit persons to whom the Software is furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.

</editor-fold> */

#include <vsg_webgpu/core/Export.h>
#include <vsg_webgpu/core/WebGPUHeaders.h>
#include <vsg_webgpu/vk/Device.h>
#include <vsg/app/Window.h>
#include <vsg/core/Inherit.h>
#include <vsg/core/Object.h>
#include <vsg/core/ref_ptr.h>
#include <string>

namespace vsg_webgpu
{
    /// WebGPU窗口，对应VSG的Window类
    /// 管理WebGPU表面和交换链
    class VSG_WEBGPU_DECLSPEC Window : public vsg::Inherit<vsg::Object, Window>
    {
    public:
        Window(const std::string& title = "VSG WebGPU Window", 
               uint32_t width = 1920, 
               uint32_t height = 1080);

        // 对应VSG Window的核心接口
        std::string title;
        uint32_t width = 1920;
        uint32_t height = 1080;
        bool fullscreen = false;
        bool resizable = true;
        bool visible = true;

        // WebGPU特定属性
        vsg::ref_ptr<Device> device;
        WGPUSurface surface;
        WGPUSwapChain swapChain;
        WGPUTextureFormat swapChainFormat = WGPUTextureFormat_BGRA8Unorm;

        // 窗口管理
        bool create();
        void destroy();
        bool valid() const { return _valid; }

        // 交换链管理
        bool createSwapChain();
        void resizeSwapChain(uint32_t newWidth, uint32_t newHeight);
        WGPUTextureView getCurrentTextureView();
        void present();

        // 事件处理
        bool pollEvents();
        void waitEvents();

        // 窗口属性
        void setTitle(const std::string& newTitle);
        void resize(uint32_t newWidth, uint32_t newHeight);
        void setFullscreen(bool enable);
        void setVisible(bool enable);

        // 获取窗口信息
        uint32_t getWidth() const { return width; }
        uint32_t getHeight() const { return height; }
        float getAspectRatio() const { return static_cast<float>(width) / static_cast<float>(height); }

        // 帧缓冲大小（可能与窗口大小不同，特别是在高DPI显示器上）
        uint32_t framebufferWidth = 1920;
        uint32_t framebufferHeight = 1080;

        // 回调函数
        using ResizeCallback = std::function<void(uint32_t width, uint32_t height)>;
        using CloseCallback = std::function<void()>;

        void setResizeCallback(ResizeCallback callback) { _resizeCallback = callback; }
        void setCloseCallback(CloseCallback callback) { _closeCallback = callback; }

        // 平台特定的窗口句柄
#ifdef _WIN32
        void* getHWND() const { return _hwnd; }
#elif defined(__linux__)
        void* getDisplay() const { return _display; }
        unsigned long getWindow() const { return _window; }
#elif defined(__APPLE__)
        void* getNSWindow() const { return _nsWindow; }
#endif

    protected:
        virtual ~Window();

    private:
        bool _valid = false;
        ResizeCallback _resizeCallback;
        CloseCallback _closeCallback;

        // 平台特定的实现
#ifdef _WIN32
        void* _hwnd = nullptr;
        bool _createWin32Window();
        void _destroyWin32Window();
#elif defined(__linux__)
        void* _display = nullptr;
        unsigned long _window = 0;
        bool _createX11Window();
        void _destroyX11Window();
#elif defined(__APPLE__)
        void* _nsWindow = nullptr;
        bool _createCocoaWindow();
        void _destroyCocoaWindow();
#endif

        // WebGPU表面创建
        bool _createWebGPUSurface();
        void _destroyWebGPUSurface();

        // 内部事件处理
        void _handleResize(uint32_t newWidth, uint32_t newHeight);
        void _handleClose();
    };

    VSG_type_name(vsg_webgpu::Window);

} // namespace vsg_webgpu
