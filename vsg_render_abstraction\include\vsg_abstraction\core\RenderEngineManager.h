#pragma once

/* <editor-fold desc="MIT License">

Copyright(c) 2024 VSG Render Engine Abstraction

Permission is hereby granted, free of charge, to any person obtaining a copy of this software and associated documentation files (the "Software"), to deal in the Software without restriction, including without limitation the rights to use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of the Software, and to permit persons to whom the Software is furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND <PERSON>NINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.

</editor-fold> */

#include <functional>
#include <memory>
#include <mutex>
#include <vsg_abstraction/core/Export.h>
#include <vsg_abstraction/core/IRenderEngine.h>
#include <vsg_abstraction/core/Types.h>

// 前向声明VSG类型
namespace vsg
{
    class Group;
    class MatrixTransform;
    class Geometry;
    class StateGroup;
    class Device;
    class Instance;
    class Window;
    class RenderGraph;
    class RecordTraversal;
    template<typename T>
    class ref_ptr;
} // namespace vsg

namespace vsg_abstraction
{

    /**
 * @brief 渲染引擎管理器
 * 
 * 全局单例类，负责管理渲染引擎的创建、切换和生命周期。
 * 这是整个VSG抽象层的入口点，所有渲染相关的操作都通过这个管理器进行。
 * 
 * 设计目标：
 * 1. 提供全局统一的渲染引擎访问点
 * 2. 支持运行时渲染后端切换
 * 3. 保持与VSG API的高度兼容性
 * 4. 提供便捷的创建方法
 * 5. 支持渐进式迁移
 */
    class VSG_ABSTRACTION_DECLSPEC RenderEngineManager
    {
    public:
        /**
     * @brief 获取单例实例
     * @return 渲染引擎管理器实例引用
     */
        static RenderEngineManager& instance();

        // 禁用拷贝构造和赋值
        RenderEngineManager(const RenderEngineManager&) = delete;
        RenderEngineManager& operator=(const RenderEngineManager&) = delete;

        // ========== 引擎管理 ==========

        /**
     * @brief 设置渲染引擎
     * @param engine 渲染引擎实例
     * @param autoInitialize 是否自动初始化引擎
     * @return 成功返回true
     */
        bool setEngine(std::unique_ptr<IRenderEngine> engine, bool autoInitialize = true);

        /**
     * @brief 获取当前渲染引擎
     * @return 渲染引擎指针，如果未设置则返回nullptr
     */
        IRenderEngine* getEngine() const;

        /**
     * @brief 检查是否有可用的渲染引擎
     * @return 有可用引擎返回true
     */
        bool hasEngine() const;

        /**
     * @brief 获取当前渲染后端类型
     * @return 渲染后端类型，如果未设置引擎则返回Mock
     */
        RenderBackend getCurrentBackend() const;

        /**
     * @brief 切换渲染后端
     * @param backend 目标渲染后端
     * @param config 引擎配置（可选）
     * @return 成功返回true
     */
        bool switchBackend(RenderBackend backend, const void* config = nullptr);

        // ========== 生命周期管理 ==========

        /**
     * @brief 初始化渲染引擎
     * @return 成功返回true
     */
        bool initialize();

        /**
     * @brief 关闭渲染引擎
     */
        void shutdown();

        /**
     * @brief 检查渲染引擎是否已初始化
     * @return 已初始化返回true
     */
        bool isInitialized() const;

        // ========== 便捷创建接口 ==========

        /**
     * @brief 创建组节点
     * @return 组节点指针
     */
        ref_ptr<IGroup> createGroup();

        /**
     * @brief 创建变换节点
     * @return 变换节点指针
     */
        ref_ptr<ITransform> createTransform();

        /**
     * @brief 创建矩阵变换节点
     * @return 矩阵变换节点指针
     */
        ref_ptr<ITransform> createMatrixTransform();

        /**
     * @brief 创建几何节点
     * @return 几何节点指针
     */
        ref_ptr<IGeometry> createGeometry();

        /**
     * @brief 创建状态组节点
     * @return 状态组节点指针
     */
        ref_ptr<IStateGroup> createStateGroup();

        /**
     * @brief 创建窗口
     * @param traits 窗口特性
     * @return 窗口指针
     */
        ref_ptr<IWindow> createWindow(const WindowTraits& traits = {});

        /**
     * @brief 创建渲染图
     * @param window 目标窗口
     * @param view 视图
     * @return 渲染图指针
     */
        ref_ptr<IRenderGraph> createRenderGraph(ref_ptr<IWindow> window, ref_ptr<INode> view = {});

        /**
     * @brief 创建记录遍历器
     * @return 记录遍历器指针
     */
        ref_ptr<IRecordTraversal> createRecordTraversal();

        // ========== VSG兼容性接口 ==========

#ifdef VSG_ABSTRACTION_HAS_VSG
        /**
     * @brief 创建VSG组节点（兼容性接口）
     * @return VSG组节点
     */
        vsg::ref_ptr<vsg::Group> createVSGGroup();

        /**
     * @brief 创建VSG变换节点（兼容性接口）
     * @return VSG变换节点
     */
        vsg::ref_ptr<vsg::MatrixTransform> createVSGTransform();

        /**
     * @brief 创建VSG几何节点（兼容性接口）
     * @return VSG几何节点
     */
        vsg::ref_ptr<vsg::Geometry> createVSGGeometry();

        /**
     * @brief 创建VSG状态组节点（兼容性接口）
     * @return VSG状态组节点
     */
        vsg::ref_ptr<vsg::StateGroup> createVSGStateGroup();
#endif

#ifdef VSG_ABSTRACTION_HAS_VSG
        /**
     * @brief 获取VSG设备（兼容性接口）
     * @return VSG设备
     */
        vsg::ref_ptr<vsg::Device> getVSGDevice();
#endif

#ifdef VSG_ABSTRACTION_HAS_VSG
        /**
     * @brief 获取VSG实例（兼容性接口）
     * @return VSG实例
     */
        vsg::ref_ptr<vsg::Instance> getVSGInstance();
#endif

        // ========== 回调和事件 ==========

        /**
     * @brief 引擎切换回调函数类型
     */
        using EngineChangeCallback = std::function<void(RenderBackend oldBackend, RenderBackend newBackend)>;

        /**
     * @brief 设置引擎切换回调
     * @param callback 回调函数
     */
        void setEngineChangeCallback(EngineChangeCallback callback);

        /**
     * @brief 设置错误回调
     * @param callback 错误回调函数
     */
        void setErrorCallback(ErrorCallback callback);

        /**
     * @brief 设置调试回调
     * @param callback 调试回调函数
     */
        void setDebugCallback(DebugCallback callback);

        // ========== 调试和诊断 ==========

        /**
     * @brief 获取渲染统计信息
     * @return 渲染统计信息
     */
        RenderStatistics getRenderStatistics() const;

        /**
     * @brief 重置渲染统计信息
     */
        void resetRenderStatistics();

        /**
     * @brief 获取引擎信息
     * @return 引擎信息，如果未设置引擎则返回空信息
     */
        EngineInfo getEngineInfo() const;

        /**
     * @brief 获取设备能力信息
     * @return 设备能力信息
     */
        DeviceCapabilities getDeviceCapabilities() const;

        /**
     * @brief 开始性能分析标记
     * @param name 标记名称
     */
        void beginProfileMarker(const std::string& name);

        /**
     * @brief 结束性能分析标记
     */
        void endProfileMarker();

        // ========== 高级功能 ==========

        /**
     * @brief 等待渲染完成
     */
        void waitIdle();

        /**
     * @brief 执行自定义渲染命令
     * @param command 自定义命令函数
     */
        void executeCustomCommand(std::function<void(IRenderEngine*)> command);

        /**
     * @brief 获取原生渲染引擎句柄
     * @return 原生句柄，类型取决于具体实现
     */
        void* getNativeEngineHandle() const;

        /**
     * @brief 检查功能支持
     * @param feature 功能名称
     * @return 支持返回true
     */
        bool supportsFeature(const std::string& feature) const;

        // ========== 配置管理 ==========

        /**
     * @brief 设置默认窗口特性
     * @param traits 窗口特性
     */
        void setDefaultWindowTraits(const WindowTraits& traits);

        /**
     * @brief 获取默认窗口特性
     */
        const WindowTraits& getDefaultWindowTraits() const;

        /**
     * @brief 启用/禁用自动错误检查
     * @param enable 是否启用
     */
        void setAutoErrorCheck(bool enable);

        /**
     * @brief 检查是否启用自动错误检查
     */
        bool isAutoErrorCheckEnabled() const;

    private:
        // 私有构造函数（单例模式）
        RenderEngineManager();
        ~RenderEngineManager();

        // 内部方法
        void _notifyEngineChange(RenderBackend oldBackend, RenderBackend newBackend);
        void _handleError(const std::string& error);
        void _checkEngine() const;

        // 成员变量
        mutable std::mutex mutex_;                           ///< 线程安全锁
        std::unique_ptr<IRenderEngine> engine_;              ///< 当前渲染引擎
        bool initialized_ = false;                           ///< 初始化状态
        RenderBackend currentBackend_ = RenderBackend::Mock; ///< 当前后端类型

        // 回调函数
        EngineChangeCallback engineChangeCallback_;
        ErrorCallback errorCallback_;
        DebugCallback debugCallback_;

        // 配置
        WindowTraits defaultWindowTraits_;
        bool autoErrorCheck_ = true;

        // 统计信息
        mutable RenderStatistics statistics_;
    };

    // ========== 全局便捷函数 ==========

    /**
 * @brief 获取全局渲染引擎管理器
 * @return 渲染引擎管理器引用
 */
    inline RenderEngineManager& getRenderManager()
    {
        return RenderEngineManager::instance();
    }

    /**
 * @brief 获取当前渲染引擎
 * @return 渲染引擎指针
 */
    inline IRenderEngine* getRenderEngine()
    {
        return RenderEngineManager::instance().getEngine();
    }

    /**
 * @brief 检查渲染引擎是否可用
 * @return 可用返回true
 */
    inline bool isRenderEngineAvailable()
    {
        return RenderEngineManager::instance().hasEngine() &&
               RenderEngineManager::instance().isInitialized();
    }

// ========== VSG兼容性宏 ==========

/**
 * @brief 安全获取渲染引擎的宏
 * 如果引擎不可用，会记录错误并返回
 */
#define VSG_ABSTRACTION_GET_ENGINE()                                                  \
    auto* engine = vsg_abstraction::getRenderEngine();                                \
    if (!engine)                                                                      \
    {                                                                                 \
        if (auto& manager = vsg_abstraction::getRenderManager(); manager.hasEngine()) \
        {                                                                             \
            if (!manager.initialize())                                                \
            {                                                                         \
                return;                                                               \
            }                                                                         \
            engine = manager.getEngine();                                             \
        }                                                                             \
        else                                                                          \
        {                                                                             \
            return;                                                                   \
        }                                                                             \
    }

/**
 * @brief 带返回值的安全获取渲染引擎的宏
 */
#define VSG_ABSTRACTION_GET_ENGINE_OR_RETURN(retval)                                  \
    auto* engine = vsg_abstraction::getRenderEngine();                                \
    if (!engine)                                                                      \
    {                                                                                 \
        if (auto& manager = vsg_abstraction::getRenderManager(); manager.hasEngine()) \
        {                                                                             \
            if (!manager.initialize())                                                \
            {                                                                         \
                return retval;                                                        \
            }                                                                         \
            engine = manager.getEngine();                                             \
        }                                                                             \
        else                                                                          \
        {                                                                             \
            return retval;                                                            \
        }                                                                             \
    }

#ifdef VSG_ABSTRACTION_HAS_VSG
/**
 * @brief VSG兼容性创建宏
 */
#    define VSG_CREATE_GROUP() \
        (vsg_abstraction::getRenderManager().hasEngine() ? vsg_abstraction::getRenderManager().createVSGGroup() : vsg::Group::create())

#    define VSG_CREATE_TRANSFORM() \
        (vsg_abstraction::getRenderManager().hasEngine() ? vsg_abstraction::getRenderManager().createVSGTransform() : vsg::MatrixTransform::create())

#    define VSG_CREATE_GEOMETRY() \
        (vsg_abstraction::getRenderManager().hasEngine() ? vsg_abstraction::getRenderManager().createVSGGeometry() : vsg::Geometry::create())

#    define VSG_CREATE_STATE_GROUP() \
        (vsg_abstraction::getRenderManager().hasEngine() ? vsg_abstraction::getRenderManager().createVSGStateGroup() : vsg::StateGroup::create())
#endif

} // namespace vsg_abstraction
