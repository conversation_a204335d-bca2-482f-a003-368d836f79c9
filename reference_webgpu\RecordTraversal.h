// include/rocky_webgpu/RecordTraversal.h
#pragma once
#include <rocky_webgpu/State.h>
#include <vsg/app/RecordTraversal.h>
#include <vsg/nodes/StateGroup.h>
#include <vsg/nodes/VertexDraw.h>

namespace rocky_webgpu
{

    // WebGPU的命令录制遍历器
    class RecordTraversal : public vsg::Inherit<vsg::Visitor, RecordTraversal>
    {
    public:
        RecordTraversal(State* state, wgpu::RenderPassEncoder encoder);

        // 节点访问
        void apply(vsg::Object& object) override;
        void apply(vsg::Node& node) override;
        void apply(vsg::Group& group) override;
        void apply(vsg::StateGroup& stateGroup) override;
        void apply(vsg::Transform& transform) override;
        void apply(vsg::VertexDraw& vertexDraw) override;
        void apply(vsg::VertexIndexDraw& vertexIndexDraw) override;

        // Rocky特定节点
        void apply(rocky::TerrainTileNode& tile);

    private:
        vsg::ref_ptr<State> _state;
        wgpu::RenderPassEncoder _encoder;
        std::unique_ptr<TerrainTileRenderer> _terrainRenderer;

        // 几何体渲染
        void renderGeometry(const vsg::BufferInfo& vertices,
                            const vsg::BufferInfo& indices,
                            uint32_t indexCount);
    };

    // 实现
    RecordTraversal::RecordTraversal(State* state, wgpu::RenderPassEncoder encoder) :
        _state(state), _encoder(encoder)
    {
        _terrainRenderer = std::make_unique<TerrainTileRenderer>(state->getDevice());
    }

    void RecordTraversal::apply(vsg::StateGroup& stateGroup)
    {
        if (stateGroup.stateCommands.empty())
        {
            traverse(stateGroup);
            return;
        }

        // 应用状态命令
        for (auto& command : stateGroup.stateCommands)
        {
            _state->apply(command);
        }

        // 遍历子节点
        traverse(stateGroup);
    }

    void RecordTraversal::apply(vsg::Transform& transform)
    {
        // 推入模型矩阵
        _state->pushModelMatrix(transform.matrix);

        traverse(transform);

        _state->popModelMatrix();
    }

    void RecordTraversal::apply(vsg::VertexIndexDraw& vertexIndexDraw)
    {
        // 获取顶点数据
        auto vertices = vertexIndexDraw.arrays[0];
        auto indices = vertexIndexDraw.indices;

        if (!vertices || !indices) return;

        // 创建或获取缓冲区
        auto device = _state->getDevice();
        auto& cache = device->getResourceCache();

        // 顶点缓冲区
        size_t vertexSize = vertices->dataSize();
        std::string vertexKey = "vertex_" + std::to_string(reinterpret_cast<uintptr_t>(vertices.get()));

        wgpu::BufferDescriptor vertexDesc{};
        vertexDesc.size = vertexSize;
        vertexDesc.usage = wgpu::BufferUsage::Vertex | wgpu::BufferUsage::CopyDst;

        auto vertexBuffer = cache.getOrCreateBuffer(vertexKey, vertexDesc,
                                                    [&](wgpu::Buffer buffer) {
                                                        device->getQueue().WriteBuffer(buffer, 0, vertices->dataPointer(), vertexSize);
                                                    });

        // 索引缓冲区
        size_t indexSize = indices->dataSize();
        std::string indexKey = "index_" + std::to_string(reinterpret_cast<uintptr_t>(indices.get()));

        wgpu::BufferDescriptor indexDesc{};
        indexDesc.size = indexSize;
        indexDesc.usage = wgpu::BufferUsage::Index | wgpu::BufferUsage::CopyDst;

        auto indexBuffer = cache.getOrCreateBuffer(indexKey, indexDesc,
                                                   [&](wgpu::Buffer buffer) {
                                                       device->getQueue().WriteBuffer(buffer, 0, indices->dataPointer(), indexSize);
                                                   });

        // 设置缓冲区并绘制
        _encoder.SetVertexBuffer(0, vertexBuffer);
        _encoder.SetIndexBuffer(indexBuffer, wgpu::IndexFormat::Uint32);
        _encoder.DrawIndexed(indices->valueCount());
    }

    void RecordTraversal::apply(rocky::TerrainTileNode& tile)
    {
        _terrainRenderer->render(&tile, *_state);
    }

} // namespace rocky_webgpu
