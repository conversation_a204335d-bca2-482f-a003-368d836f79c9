name: VSG Render Abstraction CI

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

env:
  BUILD_TYPE: Release

jobs:
  # Windows构建和测试
  windows-build:
    runs-on: windows-latest
    
    strategy:
      matrix:
        build_type: [Release, Debug]
        
    steps:
    - uses: actions/checkout@v3
    
    - name: Setup MSBuild
      uses: microsoft/setup-msbuild@v1
    
    - name: Setup vcpkg
      uses: lukka/run-vcpkg@v11
      with:
        vcpkgGitCommitId: '2023.04.15'
    
    - name: Install VSG
      run: |
        vcpkg install vsg:x64-windows
    
    - name: Configure CMake
      run: |
        cmake -B build -G "Visual Studio 17 2022" -A x64 `
          -DCMAKE_BUILD_TYPE=${{ matrix.build_type }} `
          -DCMAKE_TOOLCHAIN_FILE=$env:VCPKG_ROOT/scripts/buildsystems/vcpkg.cmake `
          -DVSG_ABSTRACTION_BUILD_VSG_BACKEND=ON `
          -DVSG_ABSTRACTION_BUILD_TESTS=ON `
          -DVSG_ABSTRACTION_BUILD_EXAMPLES=ON `
          -DVSG_ABSTRACTION_BUILD_SHARED_LIBS=ON
    
    - name: Build
      run: cmake --build build --config ${{ matrix.build_type }} --parallel
    
    - name: Test
      working-directory: build
      run: ctest --output-on-failure --build-config ${{ matrix.build_type }}
    
    - name: Run Examples
      working-directory: build/examples/${{ matrix.build_type }}
      run: |
        if (Test-Path "basic_example.exe") { ./basic_example.exe }
        if (Test-Path "vsg_rendering_example.exe") { ./vsg_rendering_example.exe }
    
    - name: Upload Artifacts
      uses: actions/upload-artifact@v3
      with:
        name: windows-${{ matrix.build_type }}-binaries
        path: |
          build/lib/${{ matrix.build_type }}/
          build/bin/${{ matrix.build_type }}/
          build/examples/${{ matrix.build_type }}/

  # Linux构建和测试
  linux-build:
    runs-on: ubuntu-latest
    
    strategy:
      matrix:
        build_type: [Release, Debug]
        compiler: [gcc, clang]
        
    steps:
    - uses: actions/checkout@v3
    
    - name: Install Dependencies
      run: |
        sudo apt-get update
        sudo apt-get install -y cmake ninja-build
        
        # 安装编译器
        if [ "${{ matrix.compiler }}" = "clang" ]; then
          sudo apt-get install -y clang-14 libc++-14-dev libc++abi-14-dev
          echo "CC=clang-14" >> $GITHUB_ENV
          echo "CXX=clang++-14" >> $GITHUB_ENV
        else
          sudo apt-get install -y gcc-11 g++-11
          echo "CC=gcc-11" >> $GITHUB_ENV
          echo "CXX=g++-11" >> $GITHUB_ENV
        fi
        
        # 尝试安装VSG（如果可用）
        sudo apt-get install -y libvulkan-dev || true
    
    - name: Build VSG from source
      run: |
        git clone https://github.com/vsg-dev/VulkanSceneGraph.git
        cd VulkanSceneGraph
        cmake -B build -G Ninja -DCMAKE_BUILD_TYPE=Release
        cmake --build build --parallel
        sudo cmake --install build
        cd ..
    
    - name: Configure CMake
      run: |
        cmake -B build -G Ninja \
          -DCMAKE_BUILD_TYPE=${{ matrix.build_type }} \
          -DVSG_ABSTRACTION_BUILD_VSG_BACKEND=ON \
          -DVSG_ABSTRACTION_BUILD_TESTS=ON \
          -DVSG_ABSTRACTION_BUILD_EXAMPLES=ON \
          -DVSG_ABSTRACTION_BUILD_SHARED_LIBS=ON
    
    - name: Build
      run: cmake --build build --parallel
    
    - name: Test
      working-directory: build
      run: ctest --output-on-failure
    
    - name: Run Examples
      working-directory: build/examples
      run: |
        if [ -x "basic_example" ]; then ./basic_example; fi
        if [ -x "vsg_rendering_example" ]; then ./vsg_rendering_example; fi
    
    - name: Upload Artifacts
      uses: actions/upload-artifact@v3
      with:
        name: linux-${{ matrix.compiler }}-${{ matrix.build_type }}-binaries
        path: |
          build/lib/
          build/bin/
          build/examples/

  # macOS构建和测试
  macos-build:
    runs-on: macos-latest
    
    strategy:
      matrix:
        build_type: [Release, Debug]
        
    steps:
    - uses: actions/checkout@v3
    
    - name: Install Dependencies
      run: |
        brew install cmake ninja vulkan-headers vulkan-loader
    
    - name: Build VSG from source
      run: |
        git clone https://github.com/vsg-dev/VulkanSceneGraph.git
        cd VulkanSceneGraph
        cmake -B build -G Ninja -DCMAKE_BUILD_TYPE=Release
        cmake --build build --parallel
        sudo cmake --install build
        cd ..
    
    - name: Configure CMake
      run: |
        cmake -B build -G Ninja \
          -DCMAKE_BUILD_TYPE=${{ matrix.build_type }} \
          -DVSG_ABSTRACTION_BUILD_VSG_BACKEND=ON \
          -DVSG_ABSTRACTION_BUILD_TESTS=ON \
          -DVSG_ABSTRACTION_BUILD_EXAMPLES=ON \
          -DVSG_ABSTRACTION_BUILD_SHARED_LIBS=ON
    
    - name: Build
      run: cmake --build build --parallel
    
    - name: Test
      working-directory: build
      run: ctest --output-on-failure
    
    - name: Run Examples
      working-directory: build/examples
      run: |
        if [ -x "basic_example" ]; then ./basic_example; fi
        if [ -x "vsg_rendering_example" ]; then ./vsg_rendering_example; fi
    
    - name: Upload Artifacts
      uses: actions/upload-artifact@v3
      with:
        name: macos-${{ matrix.build_type }}-binaries
        path: |
          build/lib/
          build/bin/
          build/examples/

  # 代码质量检查
  code-quality:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Install Dependencies
      run: |
        sudo apt-get update
        sudo apt-get install -y clang-format-14 cppcheck
    
    - name: Check Code Format
      run: |
        find . -name "*.cpp" -o -name "*.h" -o -name "*.hpp" | \
        xargs clang-format-14 --dry-run --Werror
    
    - name: Static Analysis
      run: |
        cppcheck --enable=all --error-exitcode=1 \
          --suppress=missingIncludeSystem \
          --suppress=unusedFunction \
          src/ include/

  # 文档生成
  documentation:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Install Doxygen
      run: sudo apt-get install -y doxygen graphviz
    
    - name: Generate Documentation
      run: |
        doxygen Doxyfile || echo "Doxygen config not found, skipping"
    
    - name: Upload Documentation
      uses: actions/upload-artifact@v3
      with:
        name: documentation
        path: docs/

  # 性能基准测试
  benchmark:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Install Dependencies
      run: |
        sudo apt-get update
        sudo apt-get install -y cmake ninja-build gcc-11 g++-11
    
    - name: Configure CMake
      run: |
        cmake -B build -G Ninja \
          -DCMAKE_BUILD_TYPE=Release \
          -DVSG_ABSTRACTION_BUILD_VSG_BACKEND=OFF \
          -DVSG_ABSTRACTION_BUILD_TESTS=ON \
          -DVSG_ABSTRACTION_BUILD_EXAMPLES=ON
    
    - name: Build
      run: cmake --build build --parallel
    
    - name: Run Benchmarks
      working-directory: build
      run: |
        if [ -x "tests/test_performance" ]; then
          ./tests/test_performance > benchmark_results.txt
        fi
    
    - name: Upload Benchmark Results
      uses: actions/upload-artifact@v3
      with:
        name: benchmark-results
        path: build/benchmark_results.txt
