@echo off
REM VSG WebGPU Windows Build Script

echo ========================================
echo VSG WebGPU Windows Build Script
echo ========================================

REM 设置变量
set BUILD_TYPE=Release
set BUILD_DIR=build_desk
set INSTALL_DIR=redist_desk
set CMAKE_GENERATOR="Visual Studio 17 2022"
set CMAKE_ARCH=x64

REM 解析命令行参数
:parse_args
if "%1"=="debug" (
    set BUILD_TYPE=Debug
    shift
    goto parse_args
)
if "%1"=="release" (
    set BUILD_TYPE=Release
    shift
    goto parse_args
)
if "%1"=="clean" (
    echo Cleaning build directory...
    if exist %BUILD_DIR% rmdir /s /q %BUILD_DIR%
    if exist %INSTALL_DIR% rmdir /s /q %INSTALL_DIR%
    echo Clean completed.
    goto end
)
if "%1"=="help" (
    goto show_help
)
if "%1"=="/?" (
    goto show_help
)

echo Build Type: %BUILD_TYPE%
echo Build Directory: %BUILD_DIR%
echo Install Directory: %INSTALL_DIR%
echo.

REM 检查必要的工具
echo Checking required tools...

where cmake >nul 2>&1
if %ERRORLEVEL% neq 0 (
    echo ERROR: CMake not found in PATH
    echo Please install CMake and add it to your PATH
    goto error
)

where git >nul 2>&1
if %ERRORLEVEL% neq 0 (
    echo WARNING: Git not found in PATH
    echo Some features may not work properly
)

echo Tools check completed.
echo.

REM 检查VSG
echo Checking VSG installation...
if not exist "C:\dev\vcpkg\installed\x64-windows\include\vsg" (
    echo WARNING: VSG not found in vcpkg
    echo Please install VSG using: vcpkg install vsg
)

REM 创建构建目录
echo Creating build directory...
if not exist %BUILD_DIR% mkdir %BUILD_DIR%
if not exist %INSTALL_DIR% mkdir %INSTALL_DIR%

REM 配置CMake
echo.
echo ========================================
echo Configuring CMake...
echo ========================================

cd %BUILD_DIR%

cmake .. ^
    -G %CMAKE_GENERATOR% ^
    -A %CMAKE_ARCH% ^
    -DCMAKE_BUILD_TYPE=%BUILD_TYPE% ^
    -DCMAKE_INSTALL_PREFIX=../%INSTALL_DIR% ^
    -DCMAKE_TOOLCHAIN_FILE=C:/dev/vcpkg/scripts/buildsystems/vcpkg.cmake ^
    -DVSSG_WEBGPU_BUILD_TESTS=ON ^
    -DVSSG_WEBGPU_BUILD_EXAMPLES=ON ^
    -DVSSG_WEBGPU_USE_DAWN=ON

if %ERRORLEVEL% neq 0 (
    echo ERROR: CMake configuration failed
    cd ..
    goto error
)

echo CMake configuration completed successfully.
echo.

REM 构建项目
echo ========================================
echo Building project...
echo ========================================

cmake --build . --config %BUILD_TYPE% --parallel

if %ERRORLEVEL% neq 0 (
    echo ERROR: Build failed
    cd ..
    goto error
)

echo Build completed successfully.
echo.

REM 安装项目
echo ========================================
echo Installing project...
echo ========================================

cmake --install . --config %BUILD_TYPE%

if %ERRORLEVEL% neq 0 (
    echo ERROR: Installation failed
    cd ..
    goto error
)

echo Installation completed successfully.
echo.

REM 运行测试
echo ========================================
echo Running tests...
echo ========================================

ctest --build-config %BUILD_TYPE% --output-on-failure

if %ERRORLEVEL% neq 0 (
    echo WARNING: Some tests failed
    echo Check the test output above for details
) else (
    echo All tests passed successfully.
)

cd ..

echo.
echo ========================================
echo Build Summary
echo ========================================
echo Build Type: %BUILD_TYPE%
echo Build Directory: %BUILD_DIR%
echo Install Directory: %INSTALL_DIR%
echo.
echo Build artifacts:
if exist %INSTALL_DIR%\bin\*.exe (
    echo   Executables: %INSTALL_DIR%\bin\
)
if exist %INSTALL_DIR%\lib\*.lib (
    echo   Libraries: %INSTALL_DIR%\lib\
)
if exist %INSTALL_DIR%\include\vsg_webgpu (
    echo   Headers: %INSTALL_DIR%\include\vsg_webgpu\
)
echo.
echo Build completed successfully!
goto end

:show_help
echo Usage: build_windows.bat [options]
echo.
echo Options:
echo   debug     - Build in Debug mode
echo   release   - Build in Release mode (default)
echo   clean     - Clean build and install directories
echo   help      - Show this help message
echo.
echo Examples:
echo   build_windows.bat          - Build in Release mode
echo   build_windows.bat debug    - Build in Debug mode
echo   build_windows.bat clean    - Clean all build artifacts
echo.
goto end

:error
echo.
echo ========================================
echo Build FAILED!
echo ========================================
echo Check the error messages above for details.
echo.
pause
exit /b 1

:end
echo.
pause
