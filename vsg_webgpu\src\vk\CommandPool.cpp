/* <editor-fold desc="MIT License">

Copyright(c) 2024 VSG WebGPU Extension

Permission is hereby granted, free of charge, to any person obtaining a copy of this software and associated documentation files (the "Software"), to deal in the Software without restriction, including without limitation the rights to use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of the Software, and to permit persons to whom the Software is furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.

</editor-fold> */

#include <vsg_webgpu/vk/CommandPool.h>
#include <vsg_webgpu/vk/CommandBuffer.h>
#include <vsg_webgpu/vk/Device.h>

using namespace vsg_webgpu;

CommandPool::CommandPool(Device* device, uint32_t queueFamilyIndex) :
    queueFamilyIndex(queueFamilyIndex),
    _device(device)
{
    VSG_WEBGPU_LOG_DEBUG("CommandPool::CommandPool()");
}

CommandPool::~CommandPool()
{
    VSG_WEBGPU_LOG_DEBUG("CommandPool::~CommandPool()");
    
    // 清理所有命令缓冲区
    std::lock_guard<std::mutex> lock(_mutex);
    _activeCommandBuffers.clear();
    _availableCommandBuffers.clear();
}

vsg::ref_ptr<CommandBuffer> CommandPool::allocate()
{
    std::lock_guard<std::mutex> lock(_mutex);
    
    // 尝试重用可用的命令缓冲区
    if (!_availableCommandBuffers.empty())
    {
        auto commandBuffer = _availableCommandBuffers.back();
        _availableCommandBuffers.pop_back();
        _activeCommandBuffers.push_back(commandBuffer);
        
        // 重置命令缓冲区
        commandBuffer->reset();
        
        VSG_WEBGPU_LOG_DEBUG("CommandPool::allocate() - reused command buffer");
        return commandBuffer;
    }
    
    // 创建新的命令缓冲区
    auto commandBuffer = _createCommandBuffer();
    if (commandBuffer)
    {
        _activeCommandBuffers.push_back(commandBuffer);
        VSG_WEBGPU_LOG_DEBUG("CommandPool::allocate() - created new command buffer");
    }
    
    return commandBuffer;
}

void CommandPool::free(CommandBuffer* commandBuffer)
{
    if (!commandBuffer) return;
    
    std::lock_guard<std::mutex> lock(_mutex);
    
    // 从活动列表中移除
    auto it = std::find_if(_activeCommandBuffers.begin(), _activeCommandBuffers.end(),
                          [commandBuffer](const vsg::ref_ptr<CommandBuffer>& cb) {
                              return cb.get() == commandBuffer;
                          });
    
    if (it != _activeCommandBuffers.end())
    {
        auto cb = *it;
        _activeCommandBuffers.erase(it);
        
        // 重置并添加到可用列表
        cb->reset();
        _availableCommandBuffers.push_back(cb);
        
        VSG_WEBGPU_LOG_DEBUG("CommandPool::free() - command buffer returned to pool");
    }
}

void CommandPool::reset()
{
    std::lock_guard<std::mutex> lock(_mutex);
    
    // 重置所有命令缓冲区
    for (auto& cb : _activeCommandBuffers)
    {
        cb->reset();
    }
    
    for (auto& cb : _availableCommandBuffers)
    {
        cb->reset();
    }
    
    // 将所有活动的命令缓冲区移到可用列表
    _availableCommandBuffers.insert(_availableCommandBuffers.end(),
                                   _activeCommandBuffers.begin(),
                                   _activeCommandBuffers.end());
    _activeCommandBuffers.clear();
    
    VSG_WEBGPU_LOG_DEBUG("CommandPool::reset() - all command buffers reset");
}

std::vector<vsg::ref_ptr<CommandBuffer>> CommandPool::allocate(uint32_t count)
{
    std::vector<vsg::ref_ptr<CommandBuffer>> commandBuffers;
    commandBuffers.reserve(count);
    
    for (uint32_t i = 0; i < count; ++i)
    {
        auto cb = allocate();
        if (cb)
        {
            commandBuffers.push_back(cb);
        }
        else
        {
            VSG_WEBGPU_LOG_ERROR("CommandPool::allocate() - failed to allocate command buffer ", i);
            break;
        }
    }
    
    return commandBuffers;
}

void CommandPool::free(const std::vector<vsg::ref_ptr<CommandBuffer>>& commandBuffers)
{
    for (auto& cb : commandBuffers)
    {
        free(cb.get());
    }
}

size_t CommandPool::getActiveCommandBufferCount() const
{
    std::lock_guard<std::mutex> lock(_mutex);
    return _activeCommandBuffers.size();
}

size_t CommandPool::getTotalCommandBufferCount() const
{
    std::lock_guard<std::mutex> lock(_mutex);
    return _activeCommandBuffers.size() + _availableCommandBuffers.size();
}

vsg::ref_ptr<CommandBuffer> CommandPool::_createCommandBuffer()
{
    // 创建WebGPU命令编码器
    auto encoder = _device->createCommandEncoder();
    if (!encoder)
    {
        VSG_WEBGPU_LOG_ERROR("CommandPool::_createCommandBuffer() - failed to create command encoder");
        return {};
    }
    
    // 创建命令缓冲区包装器
    return CommandBuffer::create(this, encoder);
}
