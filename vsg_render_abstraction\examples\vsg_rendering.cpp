/* <editor-fold desc="MIT License">

Copyright(c) 2024 VSG Render Engine Abstraction

Permission is hereby granted, free of charge, to any person obtaining a copy of this software and associated documentation files (the "Software"), to deal in the Software without restriction, including without limitation the rights to use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of the Software, and to permit persons to whom the Software is furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.

</editor-fold> */

/**
 * @file vsg_rendering.cpp
 * @brief VSG渲染引擎抽象层渲染示例
 * 
 * 这个示例展示了如何使用VSG抽象层进行实际的3D渲染：
 * 1. 创建窗口和渲染上下文
 * 2. 构建3D场景图
 * 3. 设置相机和视图
 * 4. 执行渲染循环
 * 5. 处理用户输入
 */

#include <vsg_abstraction/core/RenderEngineManager.h>
#include <vsg_abstraction/core/RenderEngineFactory.h>
#include <vsg_abstraction/core/Config.h>

#include <iostream>
#include <chrono>
#include <thread>
#include <cmath>

#ifdef VSG_ABSTRACTION_HAS_VSG
#include <vsg/all.h>
#endif

using namespace vsg_abstraction;

class VSGRenderingExample {
public:
    VSGRenderingExample() = default;
    ~VSGRenderingExample() = default;

    bool initialize() {
        std::cout << "=== VSG渲染引擎抽象层渲染示例 ===" << std::endl;
        std::cout << "版本: " << VERSION_STRING << std::endl;
        std::cout << "平台: " << PLATFORM_NAME << std::endl;
        std::cout << std::endl;

        // 获取渲染引擎管理器
        auto& manager = getRenderManager();

        // 设置错误和调试回调
        manager.setErrorCallback([](const std::string& error) {
            std::cerr << "渲染错误: " << error << std::endl;
        });

        manager.setDebugCallback([](const std::string& message, int severity) {
            if (severity > 0) {
                std::cout << "调试信息 [" << severity << "]: " << message << std::endl;
            }
        });

        // 尝试切换到VSG后端
        if (manager.switchBackend(RenderBackend::Vulkan)) {
            std::cout << "✓ 成功切换到VSG/Vulkan后端" << std::endl;
        } else {
            std::cout << "✗ VSG后端不可用，切换到Mock后端" << std::endl;
            if (!manager.switchBackend(RenderBackend::Mock)) {
                std::cerr << "错误: 无法初始化任何渲染后端" << std::endl;
                return false;
            }
        }

        // 获取引擎信息
        auto engineInfo = manager.getEngineInfo();
        std::cout << "引擎: " << engineInfo.name << " v" << engineInfo.version << std::endl;
        std::cout << "供应商: " << engineInfo.vendor << std::endl;

        // 获取设备能力
        auto capabilities = manager.getDeviceCapabilities();
        std::cout << "设备能力:" << std::endl;
        std::cout << "  最大纹理尺寸: " << capabilities.maxTextureSize << std::endl;
        std::cout << "  支持几何着色器: " << (capabilities.supportsGeometryShader ? "是" : "否") << std::endl;
        std::cout << "  支持计算着色器: " << (capabilities.supportsComputeShader ? "是" : "否") << std::endl;
        std::cout << std::endl;

        return true;
    }

    bool createWindow() {
        auto& manager = getRenderManager();

        // 创建窗口特性
        WindowTraits traits;
        traits.windowTitle = "VSG抽象层渲染示例";
        traits.width = 1024;
        traits.height = 768;
        traits.decoration = true;
        traits.resizable = true;
        traits.vsync = true;
        traits.samples = 4; // 4x MSAA

        // 创建窗口
        window_ = manager.createWindow(traits);
        if (!window_) {
            std::cerr << "错误: 无法创建窗口" << std::endl;
            return false;
        }

        std::cout << "✓ 窗口创建成功: " << traits.width << "x" << traits.height << std::endl;
        return true;
    }

    bool createScene() {
        auto& manager = getRenderManager();

        std::cout << "创建3D场景..." << std::endl;

        // 创建根节点
        sceneRoot_ = manager.createGroup();
        sceneRoot_->setName("SceneRoot");

        // 创建一个旋转的立方体
        createRotatingCube();

        // 创建一个三角形
        createTriangle();

        // 创建地面网格
        createGroundGrid();

        std::cout << "✓ 场景创建完成，包含 " << sceneRoot_->getNumChildren() << " 个子节点" << std::endl;
        return true;
    }

    void createRotatingCube() {
        auto& manager = getRenderManager();

        // 创建立方体变换节点
        cubeTransform_ = manager.createMatrixTransform();
        cubeTransform_->setName("CubeTransform");
        cubeTransform_->setPosition({2.0f, 0.0f, 0.0f});

        // 创建立方体几何体
        auto cubeGeometry = manager.createGeometry();
        cubeGeometry->setName("CubeGeometry");

        // 立方体顶点（简化的立方体）
        std::vector<vec3> cubeVertices = {
            // 前面
            {-1.0f, -1.0f,  1.0f}, { 1.0f, -1.0f,  1.0f}, { 1.0f,  1.0f,  1.0f}, {-1.0f,  1.0f,  1.0f},
            // 后面
            {-1.0f, -1.0f, -1.0f}, {-1.0f,  1.0f, -1.0f}, { 1.0f,  1.0f, -1.0f}, { 1.0f, -1.0f, -1.0f},
        };

        std::vector<uint32_t> cubeIndices = {
            // 前面
            0, 1, 2, 2, 3, 0,
            // 右面
            1, 7, 6, 6, 2, 1,
            // 后面
            7, 4, 5, 5, 6, 7,
            // 左面
            4, 0, 3, 3, 5, 4,
            // 底面
            4, 7, 1, 1, 0, 4,
            // 顶面
            3, 2, 6, 6, 5, 3
        };

        cubeGeometry->setVertices(cubeVertices);
        cubeGeometry->setIndices(cubeIndices);
        cubeGeometry->setPrimitiveTopology(PrimitiveTopology::TriangleList);

        // 添加到变换节点
        cubeTransform_->addChild(cubeGeometry);
        sceneRoot_->addChild(cubeTransform_);

        std::cout << "  ✓ 立方体创建完成 (顶点: " << cubeVertices.size() 
                  << ", 三角形: " << cubeIndices.size() / 3 << ")" << std::endl;
    }

    void createTriangle() {
        auto& manager = getRenderManager();

        // 创建三角形变换节点
        auto triangleTransform = manager.createMatrixTransform();
        triangleTransform->setName("TriangleTransform");
        triangleTransform->setPosition({-2.0f, 0.0f, 0.0f});

        // 创建三角形几何体
        auto triangleGeometry = manager.createGeometry();
        triangleGeometry->setName("TriangleGeometry");

        // 三角形顶点
        std::vector<vec3> triangleVertices = {
            { 0.0f,  1.0f, 0.0f},  // 顶点
            {-1.0f, -1.0f, 0.0f},  // 左下
            { 1.0f, -1.0f, 0.0f}   // 右下
        };

        std::vector<uint32_t> triangleIndices = {0, 1, 2};

        // 设置颜色
        std::vector<vec4> triangleColors = {
            {1.0f, 0.0f, 0.0f, 1.0f},  // 红色
            {0.0f, 1.0f, 0.0f, 1.0f},  // 绿色
            {0.0f, 0.0f, 1.0f, 1.0f}   // 蓝色
        };

        triangleGeometry->setVertices(triangleVertices);
        triangleGeometry->setIndices(triangleIndices);
        triangleGeometry->setColors(triangleColors);
        triangleGeometry->setPrimitiveTopology(PrimitiveTopology::TriangleList);

        triangleTransform->addChild(triangleGeometry);
        sceneRoot_->addChild(triangleTransform);

        std::cout << "  ✓ 彩色三角形创建完成" << std::endl;
    }

    void createGroundGrid() {
        auto& manager = getRenderManager();

        // 创建地面网格变换节点
        auto gridTransform = manager.createMatrixTransform();
        gridTransform->setName("GridTransform");
        gridTransform->setPosition({0.0f, -2.0f, 0.0f});

        // 创建网格几何体
        auto gridGeometry = manager.createGeometry();
        gridGeometry->setName("GridGeometry");

        // 创建网格线
        std::vector<vec3> gridVertices;
        std::vector<uint32_t> gridIndices;

        const int gridSize = 10;
        const float gridSpacing = 1.0f;
        const float gridExtent = gridSize * gridSpacing;

        uint32_t vertexIndex = 0;

        // 水平线
        for (int i = -gridSize; i <= gridSize; ++i) {
            float z = i * gridSpacing;
            gridVertices.push_back({-gridExtent, 0.0f, z});
            gridVertices.push_back({ gridExtent, 0.0f, z});
            gridIndices.push_back(vertexIndex++);
            gridIndices.push_back(vertexIndex++);
        }

        // 垂直线
        for (int i = -gridSize; i <= gridSize; ++i) {
            float x = i * gridSpacing;
            gridVertices.push_back({x, 0.0f, -gridExtent});
            gridVertices.push_back({x, 0.0f,  gridExtent});
            gridIndices.push_back(vertexIndex++);
            gridIndices.push_back(vertexIndex++);
        }

        gridGeometry->setVertices(gridVertices);
        gridGeometry->setIndices(gridIndices);
        gridGeometry->setPrimitiveTopology(PrimitiveTopology::LineList);

        gridTransform->addChild(gridGeometry);
        sceneRoot_->addChild(gridTransform);

        std::cout << "  ✓ 地面网格创建完成 (线段: " << gridIndices.size() / 2 << ")" << std::endl;
    }

    bool createRenderGraph() {
        auto& manager = getRenderManager();

        // 创建渲染图
        renderGraph_ = manager.createRenderGraph(window_, sceneRoot_);
        if (!renderGraph_) {
            std::cerr << "错误: 无法创建渲染图" << std::endl;
            return false;
        }

        std::cout << "✓ 渲染图创建成功" << std::endl;
        return true;
    }

    void runRenderLoop() {
        auto& manager = getRenderManager();

        std::cout << std::endl << "开始渲染循环..." << std::endl;
        std::cout << "按 Ctrl+C 退出" << std::endl;

        auto startTime = std::chrono::high_resolution_clock::now();
        int frameCount = 0;
        const int maxFrames = 300; // 运行5秒（假设60FPS）

        while (frameCount < maxFrames) {
            auto currentTime = std::chrono::high_resolution_clock::now();
            auto elapsed = std::chrono::duration<float>(currentTime - startTime).count();

            // 更新立方体旋转
            if (cubeTransform_) {
                float rotationAngle = elapsed * 45.0f; // 每秒45度
                vec4 rotation = {0.0f, std::sin(rotationAngle * 0.5f * 3.14159f / 180.0f), 0.0f, std::cos(rotationAngle * 0.5f * 3.14159f / 180.0f)};
                cubeTransform_->setRotation(rotation);
            }

            // 开始帧标记
            manager.beginProfileMarker("Frame_" + std::to_string(frameCount));

            // 模拟渲染工作
            std::this_thread::sleep_for(std::chrono::milliseconds(16)); // ~60 FPS

            // 结束帧标记
            manager.endProfileMarker();

            frameCount++;

            // 每60帧输出一次统计信息
            if (frameCount % 60 == 0) {
                auto stats = manager.getRenderStatistics();
                std::cout << "帧 " << frameCount << ": "
                          << "绘制调用=" << stats.drawCalls 
                          << ", 三角形=" << stats.triangles
                          << ", 帧时间=" << stats.frameTime << "ms"
                          << ", 内存=" << (stats.memoryUsed / 1024) << "KB" << std::endl;
            }
        }

        // 等待渲染完成
        manager.waitIdle();

        // 输出最终统计信息
        auto finalStats = manager.getRenderStatistics();
        std::cout << std::endl << "渲染完成!" << std::endl;
        std::cout << "最终统计信息:" << std::endl;
        std::cout << "  总帧数: " << finalStats.frameCount << std::endl;
        std::cout << "  总绘制调用: " << finalStats.drawCalls << std::endl;
        std::cout << "  总三角形数: " << finalStats.triangles << std::endl;
        std::cout << "  平均帧时间: " << finalStats.frameTime << "ms" << std::endl;
        std::cout << "  总内存使用: " << (finalStats.memoryUsed / 1024) << "KB" << std::endl;
    }

    void cleanup() {
        auto& manager = getRenderManager();
        
        std::cout << std::endl << "清理资源..." << std::endl;
        
        // 清理场景图
        if (sceneRoot_) {
            sceneRoot_->removeAllChildren();
            sceneRoot_.reset();
        }
        
        // 清理渲染对象
        renderGraph_.reset();
        window_.reset();
        cubeTransform_.reset();
        
        // 关闭渲染引擎
        manager.shutdown();
        
        std::cout << "✓ 清理完成" << std::endl;
    }

private:
    ref_ptr<IWindow> window_;
    ref_ptr<IGroup> sceneRoot_;
    ref_ptr<IRenderGraph> renderGraph_;
    ref_ptr<ITransform> cubeTransform_;
};

int main() {
    VSGRenderingExample example;

    try {
        // 初始化
        if (!example.initialize()) {
            std::cerr << "初始化失败" << std::endl;
            return 1;
        }

        // 创建窗口
        if (!example.createWindow()) {
            std::cerr << "窗口创建失败" << std::endl;
            return 1;
        }

        // 创建场景
        if (!example.createScene()) {
            std::cerr << "场景创建失败" << std::endl;
            return 1;
        }

        // 创建渲染图
        if (!example.createRenderGraph()) {
            std::cerr << "渲染图创建失败" << std::endl;
            return 1;
        }

        // 运行渲染循环
        example.runRenderLoop();

        // 清理
        example.cleanup();

        std::cout << std::endl << "🎉 VSG渲染示例运行成功！" << std::endl;
        return 0;

    } catch (const std::exception& e) {
        std::cerr << "异常: " << e.what() << std::endl;
        example.cleanup();
        return 1;
    } catch (...) {
        std::cerr << "未知异常" << std::endl;
        example.cleanup();
        return 1;
    }
}
