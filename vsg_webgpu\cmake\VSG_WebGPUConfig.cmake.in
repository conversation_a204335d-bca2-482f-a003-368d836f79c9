# VSG WebGPU Extension CMake Configuration File

@PACKAGE_INIT@

include(CMakeFindDependencyMacro)

# 查找依赖项
if(NOT @VSG_WEBGPU_USE_MOCK_VSG@)
    find_dependency(vsg REQUIRED)
endif()

# 包含目标文件
if(NOT @VSG_WEBGPU_USE_MOCK_VSG@)
    include("${CMAKE_CURRENT_LIST_DIR}/VSG_WebGPUTargets.cmake")
endif()

# 设置变量
set(VSG_WebGPU_VERSION @PROJECT_VERSION@)
set(VSG_WebGPU_USE_MOCK_VSG @VSG_WEBGPU_USE_MOCK_VSG@)
set(VSG_WebGPU_USE_DAWN @VSG_WEBGPU_USE_DAWN@)
set(VSG_WebGPU_USE_EMSCRIPTEN @VSG_WEBGPU_USE_EMSCRIPTEN@)

check_required_components(VSG_WebGPU)
