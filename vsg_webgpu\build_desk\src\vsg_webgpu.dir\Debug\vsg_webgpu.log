﻿  The vcpkg manifest was disabled, but we found a manifest file in F:\cmo-dev\my_osgearth_web\. You may want to enable vcpkg manifests in your properties page or pass /p:VcpkgEnableManifest=true to the msbuild invocation.
  WebGPUHeaders.cpp
  Device.cpp
F:\cmo-dev\my_osgearth_web\osgearth_origin\VulkanSceneGraph\vsg_webgpu\include\vsg_webgpu\vk\Queue.h(85,27): error C4430: 缺少类型说明符 - 假定为 int。注意: C++ 不支持默认 int
  (编译源文件“../../src/vk/Device.cpp”)
  
F:\cmo-dev\my_osgearth_web\osgearth_origin\VulkanSceneGraph\vsg_webgpu\include\vsg_webgpu\vk\Queue.h(85,53): error C2143: 语法错误: 缺少“,”(在“&”的前面)
  (编译源文件“../../src/vk/Device.cpp”)
  
F:\cmo-dev\my_osgearth_web\osgearth_origin\VulkanSceneGraph\vsg_webgpu\src\vk\Device.cpp(73,46): error C2039: "GetQueue": 不是 "wgpu::Device" 的成员
      F:\cmo-dev\my_osgearth_web\osgearth_origin\VulkanSceneGraph\vsg_webgpu\include\vsg_webgpu\core\MockWebGPU.h(121,7):
      参见“wgpu::Device”的声明
  
F:\cmo-dev\my_osgearth_web\osgearth_origin\VulkanSceneGraph\vsg_webgpu\src\vk\Device.cpp(338,9): error C2451: 类型为“_Ret”的条件表达式无效
F:\cmo-dev\my_osgearth_web\osgearth_origin\VulkanSceneGraph\vsg_webgpu\src\vk\Device.cpp(338,9): error C2451:         with
F:\cmo-dev\my_osgearth_web\osgearth_origin\VulkanSceneGraph\vsg_webgpu\src\vk\Device.cpp(338,9): error C2451:         [
F:\cmo-dev\my_osgearth_web\osgearth_origin\VulkanSceneGraph\vsg_webgpu\src\vk\Device.cpp(338,9): error C2451:             _Ret=vsg_webgpu::WGPURenderPipeline
F:\cmo-dev\my_osgearth_web\osgearth_origin\VulkanSceneGraph\vsg_webgpu\src\vk\Device.cpp(338,9): error C2451:         ]
      F:\cmo-dev\my_osgearth_web\osgearth_origin\VulkanSceneGraph\vsg_webgpu\src\vk\Device.cpp(338,9):
      没有可用于执行该转换的用户定义的转换运算符，或者无法调用该运算符
  
F:\cmo-dev\my_osgearth_web\osgearth_origin\VulkanSceneGraph\vsg_webgpu\src\vk\Device.cpp(357,9): error C2451: 类型为“_Ret”的条件表达式无效
F:\cmo-dev\my_osgearth_web\osgearth_origin\VulkanSceneGraph\vsg_webgpu\src\vk\Device.cpp(357,9): error C2451:         with
F:\cmo-dev\my_osgearth_web\osgearth_origin\VulkanSceneGraph\vsg_webgpu\src\vk\Device.cpp(357,9): error C2451:         [
F:\cmo-dev\my_osgearth_web\osgearth_origin\VulkanSceneGraph\vsg_webgpu\src\vk\Device.cpp(357,9): error C2451:             _Ret=vsg_webgpu::WGPUComputePipeline
F:\cmo-dev\my_osgearth_web\osgearth_origin\VulkanSceneGraph\vsg_webgpu\src\vk\Device.cpp(357,9): error C2451:         ]
      F:\cmo-dev\my_osgearth_web\osgearth_origin\VulkanSceneGraph\vsg_webgpu\src\vk\Device.cpp(357,9):
      没有可用于执行该转换的用户定义的转换运算符，或者无法调用该运算符
  
F:\cmo-dev\my_osgearth_web\osgearth_origin\VulkanSceneGraph\vsg_webgpu\src\vk\Device.cpp(376,9): error C2451: 类型为“_Ret”的条件表达式无效
F:\cmo-dev\my_osgearth_web\osgearth_origin\VulkanSceneGraph\vsg_webgpu\src\vk\Device.cpp(376,9): error C2451:         with
F:\cmo-dev\my_osgearth_web\osgearth_origin\VulkanSceneGraph\vsg_webgpu\src\vk\Device.cpp(376,9): error C2451:         [
F:\cmo-dev\my_osgearth_web\osgearth_origin\VulkanSceneGraph\vsg_webgpu\src\vk\Device.cpp(376,9): error C2451:             _Ret=vsg_webgpu::WGPUBindGroup
F:\cmo-dev\my_osgearth_web\osgearth_origin\VulkanSceneGraph\vsg_webgpu\src\vk\Device.cpp(376,9): error C2451:         ]
      F:\cmo-dev\my_osgearth_web\osgearth_origin\VulkanSceneGraph\vsg_webgpu\src\vk\Device.cpp(376,9):
      没有可用于执行该转换的用户定义的转换运算符，或者无法调用该运算符
  
  Queue.cpp
F:\cmo-dev\my_osgearth_web\osgearth_origin\VulkanSceneGraph\vsg_webgpu\include\vsg_webgpu\vk\Queue.h(85,27): error C4430: 缺少类型说明符 - 假定为 int。注意: C++ 不支持默认 int
  (编译源文件“../../src/vk/Queue.cpp”)
  
F:\cmo-dev\my_osgearth_web\osgearth_origin\VulkanSceneGraph\vsg_webgpu\include\vsg_webgpu\vk\Queue.h(85,53): error C2143: 语法错误: 缺少“,”(在“&”的前面)
  (编译源文件“../../src/vk/Queue.cpp”)
  
F:\cmo-dev\my_osgearth_web\osgearth_origin\VulkanSceneGraph\vsg_webgpu\include\vsg_webgpu\vk\CommandBuffer.h(79,34): error C4430: 缺少类型说明符 - 假定为 int。注意: C++ 不支持默认 int
  (编译源文件“../../src/vk/Queue.cpp”)
  
F:\cmo-dev\my_osgearth_web\osgearth_origin\VulkanSceneGraph\vsg_webgpu\include\vsg_webgpu\vk\CommandBuffer.h(79,59): error C2143: 语法错误: 缺少“,”(在“&”的前面)
  (编译源文件“../../src/vk/Queue.cpp”)
  
F:\cmo-dev\my_osgearth_web\osgearth_origin\VulkanSceneGraph\vsg_webgpu\include\vsg_webgpu\vk\CommandBuffer.h(83,34): error C4430: 缺少类型说明符 - 假定为 int。注意: C++ 不支持默认 int
  (编译源文件“../../src/vk/Queue.cpp”)
  
F:\cmo-dev\my_osgearth_web\osgearth_origin\VulkanSceneGraph\vsg_webgpu\include\vsg_webgpu\vk\CommandBuffer.h(83,60): error C2143: 语法错误: 缺少“,”(在“&”的前面)
  (编译源文件“../../src/vk/Queue.cpp”)
  
F:\cmo-dev\my_osgearth_web\osgearth_origin\VulkanSceneGraph\vsg_webgpu\include\vsg_webgpu\vk\CommandBuffer.h(87,35): error C4430: 缺少类型说明符 - 假定为 int。注意: C++ 不支持默认 int
  (编译源文件“../../src/vk/Queue.cpp”)
  
F:\cmo-dev\my_osgearth_web\osgearth_origin\VulkanSceneGraph\vsg_webgpu\include\vsg_webgpu\vk\CommandBuffer.h(87,61): error C2143: 语法错误: 缺少“,”(在“&”的前面)
  (编译源文件“../../src/vk/Queue.cpp”)
  
F:\cmo-dev\my_osgearth_web\osgearth_origin\VulkanSceneGraph\vsg_webgpu\include\vsg_webgpu\vk\CommandBuffer.h(97,29): error C2061: 语法错误: 标识符“WGPUQuerySet”
  (编译源文件“../../src/vk/Queue.cpp”)
  
F:\cmo-dev\my_osgearth_web\osgearth_origin\VulkanSceneGraph\vsg_webgpu\src\vk\Queue.cpp(109,26): error C4430: 缺少类型说明符 - 假定为 int。注意: C++ 不支持默认 int
F:\cmo-dev\my_osgearth_web\osgearth_origin\VulkanSceneGraph\vsg_webgpu\src\vk\Queue.cpp(109,52): error C2143: 语法错误: 缺少“,”(在“&”的前面)
F:\cmo-dev\my_osgearth_web\osgearth_origin\VulkanSceneGraph\vsg_webgpu\src\vk\Queue.cpp(156,9): warning C4002: 类函数宏的调用“VSG_WEBGPU_LOG_DEBUG”参数过多
F:\cmo-dev\my_osgearth_web\osgearth_origin\VulkanSceneGraph\vsg_webgpu\src\vk\Queue.cpp(161,9): warning C4002: 类函数宏的调用“VSG_WEBGPU_LOG_DEBUG”参数过多
  CommandBuffer.cpp
F:\cmo-dev\my_osgearth_web\osgearth_origin\VulkanSceneGraph\vsg_webgpu\include\vsg_webgpu\vk\CommandBuffer.h(79,34): error C4430: 缺少类型说明符 - 假定为 int。注意: C++ 不支持默认 int
  (编译源文件“../../src/vk/CommandBuffer.cpp”)
  
F:\cmo-dev\my_osgearth_web\osgearth_origin\VulkanSceneGraph\vsg_webgpu\include\vsg_webgpu\vk\CommandBuffer.h(79,59): error C2143: 语法错误: 缺少“,”(在“&”的前面)
  (编译源文件“../../src/vk/CommandBuffer.cpp”)
  
F:\cmo-dev\my_osgearth_web\osgearth_origin\VulkanSceneGraph\vsg_webgpu\include\vsg_webgpu\vk\CommandBuffer.h(83,34): error C4430: 缺少类型说明符 - 假定为 int。注意: C++ 不支持默认 int
  (编译源文件“../../src/vk/CommandBuffer.cpp”)
  
F:\cmo-dev\my_osgearth_web\osgearth_origin\VulkanSceneGraph\vsg_webgpu\include\vsg_webgpu\vk\CommandBuffer.h(83,60): error C2143: 语法错误: 缺少“,”(在“&”的前面)
  (编译源文件“../../src/vk/CommandBuffer.cpp”)
  
F:\cmo-dev\my_osgearth_web\osgearth_origin\VulkanSceneGraph\vsg_webgpu\include\vsg_webgpu\vk\CommandBuffer.h(87,35): error C4430: 缺少类型说明符 - 假定为 int。注意: C++ 不支持默认 int
  (编译源文件“../../src/vk/CommandBuffer.cpp”)
  
F:\cmo-dev\my_osgearth_web\osgearth_origin\VulkanSceneGraph\vsg_webgpu\include\vsg_webgpu\vk\CommandBuffer.h(87,61): error C2143: 语法错误: 缺少“,”(在“&”的前面)
  (编译源文件“../../src/vk/CommandBuffer.cpp”)
  
F:\cmo-dev\my_osgearth_web\osgearth_origin\VulkanSceneGraph\vsg_webgpu\include\vsg_webgpu\vk\CommandBuffer.h(97,29): error C2061: 语法错误: 标识符“WGPUQuerySet”
  (编译源文件“../../src/vk/CommandBuffer.cpp”)
  
F:\cmo-dev\my_osgearth_web\osgearth_origin\VulkanSceneGraph\vsg_webgpu\src\vk\CommandBuffer.cpp(131,24): error C2039: "reset": 不是 "vsg::ScratchMemory" 的成员
      F:\cmo-dev\my_osgearth_web\osgearth_origin\VulkanSceneGraph\include\vsg\core\ScratchMemory.h(22,12):
      参见“vsg::ScratchMemory”的声明
  
F:\cmo-dev\my_osgearth_web\osgearth_origin\VulkanSceneGraph\vsg_webgpu\src\vk\CommandBuffer.cpp(232,41): error C4430: 缺少类型说明符 - 假定为 int。注意: C++ 不支持默认 int
F:\cmo-dev\my_osgearth_web\osgearth_origin\VulkanSceneGraph\vsg_webgpu\src\vk\CommandBuffer.cpp(232,66): error C2143: 语法错误: 缺少“,”(在“&”的前面)
F:\cmo-dev\my_osgearth_web\osgearth_origin\VulkanSceneGraph\vsg_webgpu\src\vk\CommandBuffer.cpp(241,41): error C4430: 缺少类型说明符 - 假定为 int。注意: C++ 不支持默认 int
F:\cmo-dev\my_osgearth_web\osgearth_origin\VulkanSceneGraph\vsg_webgpu\src\vk\CommandBuffer.cpp(241,67): error C2143: 语法错误: 缺少“,”(在“&”的前面)
F:\cmo-dev\my_osgearth_web\osgearth_origin\VulkanSceneGraph\vsg_webgpu\src\vk\CommandBuffer.cpp(250,42): error C4430: 缺少类型说明符 - 假定为 int。注意: C++ 不支持默认 int
F:\cmo-dev\my_osgearth_web\osgearth_origin\VulkanSceneGraph\vsg_webgpu\src\vk\CommandBuffer.cpp(250,68): error C2143: 语法错误: 缺少“,”(在“&”的前面)
F:\cmo-dev\my_osgearth_web\osgearth_origin\VulkanSceneGraph\vsg_webgpu\src\vk\CommandBuffer.cpp(280,36): error C2065: “WGPUQuerySet”: 未声明的标识符
F:\cmo-dev\my_osgearth_web\osgearth_origin\VulkanSceneGraph\vsg_webgpu\src\vk\CommandBuffer.cpp(280,49): error C2146: 语法错误: 缺少“)”(在标识符“querySet”的前面)
F:\cmo-dev\my_osgearth_web\osgearth_origin\VulkanSceneGraph\vsg_webgpu\src\vk\CommandBuffer.cpp(281,1): error C2143: 语法错误: 缺少“;”(在“{”的前面)
F:\cmo-dev\my_osgearth_web\osgearth_origin\VulkanSceneGraph\vsg_webgpu\src\vk\CommandBuffer.cpp(281,1): error C2447: “{”: 缺少函数标题(是否是老式的形式表?)
  CommandPool.cpp
F:\cmo-dev\my_osgearth_web\osgearth_origin\VulkanSceneGraph\vsg_webgpu\include\vsg_webgpu\vk\CommandBuffer.h(79,34): error C4430: 缺少类型说明符 - 假定为 int。注意: C++ 不支持默认 int
  (编译源文件“../../src/vk/CommandPool.cpp”)
  
F:\cmo-dev\my_osgearth_web\osgearth_origin\VulkanSceneGraph\vsg_webgpu\include\vsg_webgpu\vk\CommandBuffer.h(79,59): error C2143: 语法错误: 缺少“,”(在“&”的前面)
  (编译源文件“../../src/vk/CommandPool.cpp”)
  
F:\cmo-dev\my_osgearth_web\osgearth_origin\VulkanSceneGraph\vsg_webgpu\include\vsg_webgpu\vk\CommandBuffer.h(83,34): error C4430: 缺少类型说明符 - 假定为 int。注意: C++ 不支持默认 int
  (编译源文件“../../src/vk/CommandPool.cpp”)
  
F:\cmo-dev\my_osgearth_web\osgearth_origin\VulkanSceneGraph\vsg_webgpu\include\vsg_webgpu\vk\CommandBuffer.h(83,60): error C2143: 语法错误: 缺少“,”(在“&”的前面)
  (编译源文件“../../src/vk/CommandPool.cpp”)
  
F:\cmo-dev\my_osgearth_web\osgearth_origin\VulkanSceneGraph\vsg_webgpu\include\vsg_webgpu\vk\CommandBuffer.h(87,35): error C4430: 缺少类型说明符 - 假定为 int。注意: C++ 不支持默认 int
  (编译源文件“../../src/vk/CommandPool.cpp”)
  
F:\cmo-dev\my_osgearth_web\osgearth_origin\VulkanSceneGraph\vsg_webgpu\include\vsg_webgpu\vk\CommandBuffer.h(87,61): error C2143: 语法错误: 缺少“,”(在“&”的前面)
  (编译源文件“../../src/vk/CommandPool.cpp”)
  
F:\cmo-dev\my_osgearth_web\osgearth_origin\VulkanSceneGraph\vsg_webgpu\include\vsg_webgpu\vk\CommandBuffer.h(97,29): error C2061: 语法错误: 标识符“WGPUQuerySet”
  (编译源文件“../../src/vk/CommandPool.cpp”)
  
F:\cmo-dev\my_osgearth_web\osgearth_origin\VulkanSceneGraph\vsg_webgpu\src\vk\CommandPool.cpp(128,13): warning C4002: 类函数宏的调用“VSG_WEBGPU_LOG_ERROR”参数过多
F:\cmo-dev\my_osgearth_web\osgearth_origin\VulkanSceneGraph\vsg_webgpu\src\vk\CommandPool.cpp(160,10): error C2678: 二进制“!”: 没有找到接受“vsg_webgpu::WGPUCommandEncoder”类型的左操作数的运算符(或没有可接受的转换)
      F:\cmo-dev\my_osgearth_web\osgearth_origin\VulkanSceneGraph\vsg_webgpu\src\vk\CommandPool.cpp(160,10):
      可以是“内置 C++ operator!(bool)”
          F:\cmo-dev\my_osgearth_web\osgearth_origin\VulkanSceneGraph\vsg_webgpu\src\vk\CommandPool.cpp(160,10):
          “!”: 无法将参数 1 从“vsg_webgpu::WGPUCommandEncoder”转换为“bool”
      F:\cmo-dev\my_osgearth_web\osgearth_origin\VulkanSceneGraph\vsg_webgpu\src\vk\CommandPool.cpp(160,10):
      尝试匹配参数列表“(vsg_webgpu::WGPUCommandEncoder)”时
  
F:\cmo-dev\my_osgearth_web\osgearth_origin\VulkanSceneGraph\vsg_webgpu\src\vk\CommandPool.cpp(160,9): error C2088: 内置运算符“!”无法应用于类型为“vsg_webgpu::WGPUCommandEncoder”的操作数
  Context.cpp
F:\cmo-dev\my_osgearth_web\osgearth_origin\VulkanSceneGraph\vsg_webgpu\include\vsg_webgpu\vk\Queue.h(85,27): error C4430: 缺少类型说明符 - 假定为 int。注意: C++ 不支持默认 int
  (编译源文件“../../src/vk/Context.cpp”)
  
F:\cmo-dev\my_osgearth_web\osgearth_origin\VulkanSceneGraph\vsg_webgpu\include\vsg_webgpu\vk\Queue.h(85,53): error C2143: 语法错误: 缺少“,”(在“&”的前面)
  (编译源文件“../../src/vk/Context.cpp”)
  
F:\cmo-dev\my_osgearth_web\osgearth_origin\VulkanSceneGraph\vsg_webgpu\src\vk\Context.cpp(77,44): error C2039: "size": 不是 "wgpu::BufferDescriptor" 的成员
      F:\cmo-dev\my_osgearth_web\osgearth_origin\VulkanSceneGraph\vsg_webgpu\include\vsg_webgpu\core\MockWebGPU.h(216,8):
      参见“wgpu::BufferDescriptor”的声明
  
  State.cpp
F:\cmo-dev\my_osgearth_web\osgearth_origin\VulkanSceneGraph\vsg_webgpu\include\vsg_webgpu\vk\State.h(20,10): error C1083: 无法打开包括文件: “vsg/state/StateGroup.h”: No such file or directory
  (编译源文件“../../src/vk/State.cpp”)
  
  GraphicsPipeline.cpp
F:\cmo-dev\my_osgearth_web\osgearth_origin\VulkanSceneGraph\vsg_webgpu\include\vsg_webgpu\state\GraphicsPipeline.h(72,21): error C2065: “WGPUVertexAttribute”: 未声明的标识符
  (编译源文件“../../src/state/GraphicsPipeline.cpp”)
  
F:\cmo-dev\my_osgearth_web\osgearth_origin\VulkanSceneGraph\vsg_webgpu\include\vsg_webgpu\state\GraphicsPipeline.h(72,14): error C2923: "std::vector": "WGPUVertexAttribute" 不是参数 "_Ty" 的有效 模板 类型参数
  (编译源文件“../../src/state/GraphicsPipeline.cpp”)
      F:\cmo-dev\my_osgearth_web\osgearth_origin\VulkanSceneGraph\vsg_webgpu\include\vsg_webgpu\state\GraphicsPipeline.h(72,21):
      参见“WGPUVertexAttribute”的声明
  
F:\cmo-dev\my_osgearth_web\osgearth_origin\VulkanSceneGraph\vsg_webgpu\include\vsg_webgpu\state\GraphicsPipeline.h(72,14): error C2976: “std::vector'”: 模板 参数太少
  (编译源文件“../../src/state/GraphicsPipeline.cpp”)
      C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\include\vector(442,7):
      参见“std::vector”的声明
  
F:\cmo-dev\my_osgearth_web\osgearth_origin\VulkanSceneGraph\vsg_webgpu\include\vsg_webgpu\state\GraphicsPipeline.h(72,42): error C2955: “std::vector”: 使用 类 模板 需要 模板 参数列表
  (编译源文件“../../src/state/GraphicsPipeline.cpp”)
      C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\include\vector(442,7):
      参见“std::vector”的声明
  
F:\cmo-dev\my_osgearth_web\osgearth_origin\VulkanSceneGraph\vsg_webgpu\include\vsg_webgpu\state\GraphicsPipeline.h(73,21): error C2065: “WGPUVertexBufferLayout”: 未声明的标识符
  (编译源文件“../../src/state/GraphicsPipeline.cpp”)
  
F:\cmo-dev\my_osgearth_web\osgearth_origin\VulkanSceneGraph\vsg_webgpu\include\vsg_webgpu\state\GraphicsPipeline.h(73,14): error C2923: "std::vector": "WGPUVertexBufferLayout" 不是参数 "_Ty" 的有效 模板 类型参数
  (编译源文件“../../src/state/GraphicsPipeline.cpp”)
      F:\cmo-dev\my_osgearth_web\osgearth_origin\VulkanSceneGraph\vsg_webgpu\include\vsg_webgpu\state\GraphicsPipeline.h(73,21):
      参见“WGPUVertexBufferLayout”的声明
  
F:\cmo-dev\my_osgearth_web\osgearth_origin\VulkanSceneGraph\vsg_webgpu\include\vsg_webgpu\state\GraphicsPipeline.h(73,14): error C2976: “std::vector'”: 模板 参数太少
  (编译源文件“../../src/state/GraphicsPipeline.cpp”)
      C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\include\vector(442,7):
      参见“std::vector”的声明
  
F:\cmo-dev\my_osgearth_web\osgearth_origin\VulkanSceneGraph\vsg_webgpu\include\vsg_webgpu\state\GraphicsPipeline.h(73,45): error C2955: “std::vector”: 使用 类 模板 需要 模板 参数列表
  (编译源文件“../../src/state/GraphicsPipeline.cpp”)
      C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\include\vector(442,7):
      参见“std::vector”的声明
  
F:\cmo-dev\my_osgearth_web\osgearth_origin\VulkanSceneGraph\vsg_webgpu\include\vsg_webgpu\state\GraphicsPipeline.h(86,42): error C2065: “WGPUPrimitiveTopology_TriangleList”: 未声明的标识符
  (编译源文件“../../src/state/GraphicsPipeline.cpp”)
  
F:\cmo-dev\my_osgearth_web\osgearth_origin\VulkanSceneGraph\vsg_webgpu\include\vsg_webgpu\state\GraphicsPipeline.h(87,44): error C2065: “WGPUIndexFormat_Undefined”: 未声明的标识符
  (编译源文件“../../src/state/GraphicsPipeline.cpp”)
  
F:\cmo-dev\my_osgearth_web\osgearth_origin\VulkanSceneGraph\vsg_webgpu\include\vsg_webgpu\state\GraphicsPipeline.h(88,35): error C2065: “WGPUFrontFace_CCW”: 未声明的标识符
  (编译源文件“../../src/state/GraphicsPipeline.cpp”)
  
F:\cmo-dev\my_osgearth_web\osgearth_origin\VulkanSceneGraph\vsg_webgpu\include\vsg_webgpu\state\GraphicsPipeline.h(89,33): error C2065: “WGPUCullMode_Back”: 未声明的标识符
  (编译源文件“../../src/state/GraphicsPipeline.cpp”)
  
F:\cmo-dev\my_osgearth_web\osgearth_origin\VulkanSceneGraph\vsg_webgpu\include\vsg_webgpu\state\GraphicsPipeline.h(107,30): error C3646: “stencilFront”: 未知重写说明符
  (编译源文件“../../src/state/GraphicsPipeline.cpp”)
  
F:\cmo-dev\my_osgearth_web\osgearth_origin\VulkanSceneGraph\vsg_webgpu\include\vsg_webgpu\state\GraphicsPipeline.h(107,43): error C2059: 语法错误:“=”
  (编译源文件“../../src/state/GraphicsPipeline.cpp”)
  
F:\cmo-dev\my_osgearth_web\osgearth_origin\VulkanSceneGraph\vsg_webgpu\include\vsg_webgpu\state\GraphicsPipeline.h(107,45): error C2334: “{”的前面有意外标记；跳过明显的函数体
  (编译源文件“../../src/state/GraphicsPipeline.cpp”)
  
F:\cmo-dev\my_osgearth_web\osgearth_origin\VulkanSceneGraph\vsg_webgpu\include\vsg_webgpu\state\GraphicsPipeline.h(108,30): error C3646: “stencilBack”: 未知重写说明符
  (编译源文件“../../src/state/GraphicsPipeline.cpp”)
  
F:\cmo-dev\my_osgearth_web\osgearth_origin\VulkanSceneGraph\vsg_webgpu\include\vsg_webgpu\state\GraphicsPipeline.h(108,42): error C2059: 语法错误:“=”
  (编译源文件“../../src/state/GraphicsPipeline.cpp”)
  
F:\cmo-dev\my_osgearth_web\osgearth_origin\VulkanSceneGraph\vsg_webgpu\include\vsg_webgpu\state\GraphicsPipeline.h(108,44): error C2334: “{”的前面有意外标记；跳过明显的函数体
  (编译源文件“../../src/state/GraphicsPipeline.cpp”)
  
F:\cmo-dev\my_osgearth_web\osgearth_origin\VulkanSceneGraph\vsg_webgpu\include\vsg_webgpu\state\GraphicsPipeline.h(138,21): error C2065: “WGPUColorTargetState”: 未声明的标识符
  (编译源文件“../../src/state/GraphicsPipeline.cpp”)
  
F:\cmo-dev\my_osgearth_web\osgearth_origin\VulkanSceneGraph\vsg_webgpu\include\vsg_webgpu\state\GraphicsPipeline.h(138,14): error C2923: "std::vector": "WGPUColorTargetState" 不是参数 "_Ty" 的有效 模板 类型参数
  (编译源文件“../../src/state/GraphicsPipeline.cpp”)
      F:\cmo-dev\my_osgearth_web\osgearth_origin\VulkanSceneGraph\vsg_webgpu\include\vsg_webgpu\state\GraphicsPipeline.h(138,21):
      参见“WGPUColorTargetState”的声明
  
F:\cmo-dev\my_osgearth_web\osgearth_origin\VulkanSceneGraph\vsg_webgpu\include\vsg_webgpu\state\GraphicsPipeline.h(138,14): error C2976: “std::vector'”: 模板 参数太少
  (编译源文件“../../src/state/GraphicsPipeline.cpp”)
      C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\include\vector(442,7):
      参见“std::vector”的声明
  
F:\cmo-dev\my_osgearth_web\osgearth_origin\VulkanSceneGraph\vsg_webgpu\include\vsg_webgpu\state\GraphicsPipeline.h(138,43): error C2955: “std::vector”: 使用 类 模板 需要 模板 参数列表
  (编译源文件“../../src/state/GraphicsPipeline.cpp”)
      C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\include\vector(442,7):
      参见“std::vector”的声明
  
F:\cmo-dev\my_osgearth_web\osgearth_origin\VulkanSceneGraph\vsg_webgpu\include\vsg_webgpu\vk\State.h(20,10): error C1083: 无法打开包括文件: “vsg/state/StateGroup.h”: No such file or directory
  (编译源文件“../../src/state/GraphicsPipeline.cpp”)
  
  ComputePipeline.cpp
F:\cmo-dev\my_osgearth_web\osgearth_origin\VulkanSceneGraph\vsg_webgpu\src\state\ComputePipeline.cpp(13,10): error C1083: 无法打开包括文件: “vsg_webgpu/state/ComputePipeline.h”: No such file or directory
  ShaderModule.cpp
F:\cmo-dev\my_osgearth_web\osgearth_origin\VulkanSceneGraph\vsg_webgpu\src\state\ShaderModule.cpp(13,10): error C1083: 无法打开包括文件: “vsg_webgpu/state/ShaderModule.h”: No such file or directory
  Buffer.cpp
F:\cmo-dev\my_osgearth_web\osgearth_origin\VulkanSceneGraph\vsg_webgpu\src\state\Buffer.cpp(13,10): error C1083: 无法打开包括文件: “vsg_webgpu/state/Buffer.h”: No such file or directory
  Image.cpp
F:\cmo-dev\my_osgearth_web\osgearth_origin\VulkanSceneGraph\vsg_webgpu\src\state\Image.cpp(13,10): error C1083: 无法打开包括文件: “vsg_webgpu/state/Image.h”: No such file or directory
  Sampler.cpp
F:\cmo-dev\my_osgearth_web\osgearth_origin\VulkanSceneGraph\vsg_webgpu\src\state\Sampler.cpp(13,10): error C1083: 无法打开包括文件: “vsg_webgpu/state/Sampler.h”: No such file or directory
  DescriptorSet.cpp
F:\cmo-dev\my_osgearth_web\osgearth_origin\VulkanSceneGraph\vsg_webgpu\src\state\DescriptorSet.cpp(13,10): error C1083: 无法打开包括文件: “vsg_webgpu/state/DescriptorSet.h”: No such file or directory
  RenderGraph.cpp
F:\cmo-dev\my_osgearth_web\osgearth_origin\VulkanSceneGraph\vsg_webgpu\include\vsg_webgpu\vk\CommandBuffer.h(79,34): error C4430: 缺少类型说明符 - 假定为 int。注意: C++ 不支持默认 int
  (编译源文件“../../src/app/RenderGraph.cpp”)
  
F:\cmo-dev\my_osgearth_web\osgearth_origin\VulkanSceneGraph\vsg_webgpu\include\vsg_webgpu\vk\CommandBuffer.h(79,59): error C2143: 语法错误: 缺少“,”(在“&”的前面)
  (编译源文件“../../src/app/RenderGraph.cpp”)
  
F:\cmo-dev\my_osgearth_web\osgearth_origin\VulkanSceneGraph\vsg_webgpu\include\vsg_webgpu\vk\CommandBuffer.h(83,34): error C4430: 缺少类型说明符 - 假定为 int。注意: C++ 不支持默认 int
  (编译源文件“../../src/app/RenderGraph.cpp”)
  
F:\cmo-dev\my_osgearth_web\osgearth_origin\VulkanSceneGraph\vsg_webgpu\include\vsg_webgpu\vk\CommandBuffer.h(83,60): error C2143: 语法错误: 缺少“,”(在“&”的前面)
  (编译源文件“../../src/app/RenderGraph.cpp”)
  
F:\cmo-dev\my_osgearth_web\osgearth_origin\VulkanSceneGraph\vsg_webgpu\include\vsg_webgpu\vk\CommandBuffer.h(87,35): error C4430: 缺少类型说明符 - 假定为 int。注意: C++ 不支持默认 int
  (编译源文件“../../src/app/RenderGraph.cpp”)
  
F:\cmo-dev\my_osgearth_web\osgearth_origin\VulkanSceneGraph\vsg_webgpu\include\vsg_webgpu\vk\CommandBuffer.h(87,61): error C2143: 语法错误: 缺少“,”(在“&”的前面)
  (编译源文件“../../src/app/RenderGraph.cpp”)
  
F:\cmo-dev\my_osgearth_web\osgearth_origin\VulkanSceneGraph\vsg_webgpu\include\vsg_webgpu\vk\CommandBuffer.h(97,29): error C2061: 语法错误: 标识符“WGPUQuerySet”
  (编译源文件“../../src/app/RenderGraph.cpp”)
  
F:\cmo-dev\my_osgearth_web\osgearth_origin\VulkanSceneGraph\vsg_webgpu\include\vsg_webgpu\vk\State.h(20,10): error C1083: 无法打开包括文件: “vsg/state/StateGroup.h”: No such file or directory
  (编译源文件“../../src/app/RenderGraph.cpp”)
  
  RecordTraversal.cpp
F:\cmo-dev\my_osgearth_web\osgearth_origin\VulkanSceneGraph\vsg_webgpu\include\vsg_webgpu\vk\CommandBuffer.h(79,34): error C4430: 缺少类型说明符 - 假定为 int。注意: C++ 不支持默认 int
  (编译源文件“../../src/app/RecordTraversal.cpp”)
  
F:\cmo-dev\my_osgearth_web\osgearth_origin\VulkanSceneGraph\vsg_webgpu\include\vsg_webgpu\vk\CommandBuffer.h(79,59): error C2143: 语法错误: 缺少“,”(在“&”的前面)
  (编译源文件“../../src/app/RecordTraversal.cpp”)
  
F:\cmo-dev\my_osgearth_web\osgearth_origin\VulkanSceneGraph\vsg_webgpu\include\vsg_webgpu\vk\CommandBuffer.h(83,34): error C4430: 缺少类型说明符 - 假定为 int。注意: C++ 不支持默认 int
  (编译源文件“../../src/app/RecordTraversal.cpp”)
  
F:\cmo-dev\my_osgearth_web\osgearth_origin\VulkanSceneGraph\vsg_webgpu\include\vsg_webgpu\vk\CommandBuffer.h(83,60): error C2143: 语法错误: 缺少“,”(在“&”的前面)
  (编译源文件“../../src/app/RecordTraversal.cpp”)
  
F:\cmo-dev\my_osgearth_web\osgearth_origin\VulkanSceneGraph\vsg_webgpu\include\vsg_webgpu\vk\CommandBuffer.h(87,35): error C4430: 缺少类型说明符 - 假定为 int。注意: C++ 不支持默认 int
  (编译源文件“../../src/app/RecordTraversal.cpp”)
  
F:\cmo-dev\my_osgearth_web\osgearth_origin\VulkanSceneGraph\vsg_webgpu\include\vsg_webgpu\vk\CommandBuffer.h(87,61): error C2143: 语法错误: 缺少“,”(在“&”的前面)
  (编译源文件“../../src/app/RecordTraversal.cpp”)
  
F:\cmo-dev\my_osgearth_web\osgearth_origin\VulkanSceneGraph\vsg_webgpu\include\vsg_webgpu\vk\CommandBuffer.h(97,29): error C2061: 语法错误: 标识符“WGPUQuerySet”
  (编译源文件“../../src/app/RecordTraversal.cpp”)
  
F:\cmo-dev\my_osgearth_web\osgearth_origin\VulkanSceneGraph\vsg_webgpu\include\vsg_webgpu\vk\State.h(20,10): error C1083: 无法打开包括文件: “vsg/state/StateGroup.h”: No such file or directory
  (编译源文件“../../src/app/RecordTraversal.cpp”)
  
  Window.cpp
F:\cmo-dev\my_osgearth_web\osgearth_origin\VulkanSceneGraph\vsg_webgpu\include\vsg_webgpu\app\Window.h(47,45): error C2065: “WGPUTextureFormat_BGRA8Unorm”: 未声明的标识符
  (编译源文件“../../src/app/Window.cpp”)
  
F:\cmo-dev\my_osgearth_web\osgearth_origin\VulkanSceneGraph\vsg_webgpu\src\app\Window.cpp(22,5): warning C4002: 类函数宏的调用“VSG_WEBGPU_LOG_DEBUG”参数过多
F:\cmo-dev\my_osgearth_web\osgearth_origin\VulkanSceneGraph\vsg_webgpu\src\app\Window.cpp(33,5): warning C4002: 类函数宏的调用“VSG_WEBGPU_LOG_INFO”参数过多
F:\cmo-dev\my_osgearth_web\osgearth_origin\VulkanSceneGraph\vsg_webgpu\src\app\Window.cpp(111,21): error C2678: 二进制“!”: 没有找到接受“vsg_webgpu::WGPUSurface”类型的左操作数的运算符(或没有可接受的转换)
      F:\cmo-dev\my_osgearth_web\osgearth_origin\VulkanSceneGraph\vsg_webgpu\src\app\Window.cpp(111,21):
      可以是“内置 C++ operator!(bool)”
          F:\cmo-dev\my_osgearth_web\osgearth_origin\VulkanSceneGraph\vsg_webgpu\src\app\Window.cpp(111,21):
          “!”: 无法将参数 1 从“vsg_webgpu::WGPUSurface”转换为“bool”
      F:\cmo-dev\my_osgearth_web\osgearth_origin\VulkanSceneGraph\vsg_webgpu\src\app\Window.cpp(111,21):
      尝试匹配参数列表“(vsg_webgpu::WGPUSurface)”时
  
F:\cmo-dev\my_osgearth_web\osgearth_origin\VulkanSceneGraph\vsg_webgpu\src\app\Window.cpp(111,20): error C2088: 内置运算符“!”无法应用于类型为“vsg_webgpu::WGPUSurface”的操作数
F:\cmo-dev\my_osgearth_web\osgearth_origin\VulkanSceneGraph\vsg_webgpu\src\app\Window.cpp(135,5): warning C4002: 类函数宏的调用“VSG_WEBGPU_LOG_DEBUG”参数过多
F:\cmo-dev\my_osgearth_web\osgearth_origin\VulkanSceneGraph\vsg_webgpu\src\app\Window.cpp(162,5): warning C4002: 类函数宏的调用“VSG_WEBGPU_LOG_DEBUG”参数过多
F:\cmo-dev\my_osgearth_web\osgearth_origin\VulkanSceneGraph\vsg_webgpu\src\app\Window.cpp(189,5): error C2065: “MSG”: 未声明的标识符
F:\cmo-dev\my_osgearth_web\osgearth_origin\VulkanSceneGraph\vsg_webgpu\src\app\Window.cpp(189,9): error C2146: 语法错误: 缺少“;”(在标识符“msg”的前面)
F:\cmo-dev\my_osgearth_web\osgearth_origin\VulkanSceneGraph\vsg_webgpu\src\app\Window.cpp(189,9): error C2065: “msg”: 未声明的标识符
F:\cmo-dev\my_osgearth_web\osgearth_origin\VulkanSceneGraph\vsg_webgpu\src\app\Window.cpp(190,25): error C2065: “msg”: 未声明的标识符
F:\cmo-dev\my_osgearth_web\osgearth_origin\VulkanSceneGraph\vsg_webgpu\src\app\Window.cpp(190,45): error C2065: “PM_REMOVE”: 未声明的标识符
F:\cmo-dev\my_osgearth_web\osgearth_origin\VulkanSceneGraph\vsg_webgpu\src\app\Window.cpp(190,12): error C3861: “PeekMessage”: 找不到标识符
F:\cmo-dev\my_osgearth_web\osgearth_origin\VulkanSceneGraph\vsg_webgpu\src\app\Window.cpp(192,13): error C2065: “msg”: 未声明的标识符
F:\cmo-dev\my_osgearth_web\osgearth_origin\VulkanSceneGraph\vsg_webgpu\src\app\Window.cpp(192,28): error C2065: “WM_QUIT”: 未声明的标识符
F:\cmo-dev\my_osgearth_web\osgearth_origin\VulkanSceneGraph\vsg_webgpu\src\app\Window.cpp(198,13): error C2065: “msg”: 未声明的标识符
F:\cmo-dev\my_osgearth_web\osgearth_origin\VulkanSceneGraph\vsg_webgpu\src\app\Window.cpp(198,28): error C2065: “WM_SIZE”: 未声明的标识符
F:\cmo-dev\my_osgearth_web\osgearth_origin\VulkanSceneGraph\vsg_webgpu\src\app\Window.cpp(200,40): error C2065: “msg”: 未声明的标识符
F:\cmo-dev\my_osgearth_web\osgearth_origin\VulkanSceneGraph\vsg_webgpu\src\app\Window.cpp(200,33): error C3861: “LOWORD”: 找不到标识符
F:\cmo-dev\my_osgearth_web\osgearth_origin\VulkanSceneGraph\vsg_webgpu\src\app\Window.cpp(201,41): error C2065: “msg”: 未声明的标识符
F:\cmo-dev\my_osgearth_web\osgearth_origin\VulkanSceneGraph\vsg_webgpu\src\app\Window.cpp(201,34): error C3861: “HIWORD”: 找不到标识符
F:\cmo-dev\my_osgearth_web\osgearth_origin\VulkanSceneGraph\vsg_webgpu\src\app\Window.cpp(205,27): error C2065: “msg”: 未声明的标识符
F:\cmo-dev\my_osgearth_web\osgearth_origin\VulkanSceneGraph\vsg_webgpu\src\app\Window.cpp(205,9): error C3861: “TranslateMessage”: 找不到标识符
F:\cmo-dev\my_osgearth_web\osgearth_origin\VulkanSceneGraph\vsg_webgpu\src\app\Window.cpp(206,26): error C2065: “msg”: 未声明的标识符
F:\cmo-dev\my_osgearth_web\osgearth_origin\VulkanSceneGraph\vsg_webgpu\src\app\Window.cpp(206,9): error C3861: “DispatchMessage”: 找不到标识符
F:\cmo-dev\my_osgearth_web\osgearth_origin\VulkanSceneGraph\vsg_webgpu\src\app\Window.cpp(233,36): error C2061: 语法错误: 标识符“HWND”
F:\cmo-dev\my_osgearth_web\osgearth_origin\VulkanSceneGraph\vsg_webgpu\src\app\Window.cpp(279,32): error C2061: 语法错误: 标识符“HWND”
F:\cmo-dev\my_osgearth_web\osgearth_origin\VulkanSceneGraph\vsg_webgpu\src\app\Window.cpp(336,5): error C2065: “WGPUSurfaceDescriptorFromWindowsHWND”: 未声明的标识符
F:\cmo-dev\my_osgearth_web\osgearth_origin\VulkanSceneGraph\vsg_webgpu\src\app\Window.cpp(336,42): error C2146: 语法错误: 缺少“;”(在标识符“surfaceDesc”的前面)
F:\cmo-dev\my_osgearth_web\osgearth_origin\VulkanSceneGraph\vsg_webgpu\src\app\Window.cpp(336,42): error C2065: “surfaceDesc”: 未声明的标识符
F:\cmo-dev\my_osgearth_web\osgearth_origin\VulkanSceneGraph\vsg_webgpu\src\app\Window.cpp(336,54): error C3079: 初始值设定项列表不能用作此赋值运算符的正确操作数
F:\cmo-dev\my_osgearth_web\osgearth_origin\VulkanSceneGraph\vsg_webgpu\src\app\Window.cpp(337,5): error C2065: “surfaceDesc”: 未声明的标识符
F:\cmo-dev\my_osgearth_web\osgearth_origin\VulkanSceneGraph\vsg_webgpu\src\app\Window.cpp(337,31): error C2065: “WGPUSType_SurfaceDescriptorFromWindowsHWND”: 未声明的标识符
F:\cmo-dev\my_osgearth_web\osgearth_origin\VulkanSceneGraph\vsg_webgpu\src\app\Window.cpp(338,5): error C2065: “surfaceDesc”: 未声明的标识符
F:\cmo-dev\my_osgearth_web\osgearth_origin\VulkanSceneGraph\vsg_webgpu\src\app\Window.cpp(339,5): error C2065: “surfaceDesc”: 未声明的标识符
F:\cmo-dev\my_osgearth_web\osgearth_origin\VulkanSceneGraph\vsg_webgpu\src\app\Window.cpp(339,29): error C3861: “GetModuleHandle”: 找不到标识符
F:\cmo-dev\my_osgearth_web\osgearth_origin\VulkanSceneGraph\vsg_webgpu\src\app\Window.cpp(341,5): error C2065: “WGPUSurfaceDescriptor”: 未声明的标识符
F:\cmo-dev\my_osgearth_web\osgearth_origin\VulkanSceneGraph\vsg_webgpu\src\app\Window.cpp(341,27): error C2146: 语法错误: 缺少“;”(在标识符“desc”的前面)
F:\cmo-dev\my_osgearth_web\osgearth_origin\VulkanSceneGraph\vsg_webgpu\src\app\Window.cpp(341,27): error C2065: “desc”: 未声明的标识符
F:\cmo-dev\my_osgearth_web\osgearth_origin\VulkanSceneGraph\vsg_webgpu\src\app\Window.cpp(341,32): error C3079: 初始值设定项列表不能用作此赋值运算符的正确操作数
F:\cmo-dev\my_osgearth_web\osgearth_origin\VulkanSceneGraph\vsg_webgpu\src\app\Window.cpp(342,5): error C2065: “desc”: 未声明的标识符
F:\cmo-dev\my_osgearth_web\osgearth_origin\VulkanSceneGraph\vsg_webgpu\src\app\Window.cpp(342,25): error C2065: “surfaceDesc”: 未声明的标识符
F:\cmo-dev\my_osgearth_web\osgearth_origin\VulkanSceneGraph\vsg_webgpu\src\app\Window.cpp(344,37): error C2039: "CreateSurface": 不是 "wgpu::Instance" 的成员
      F:\cmo-dev\my_osgearth_web\osgearth_origin\VulkanSceneGraph\vsg_webgpu\include\vsg_webgpu\core\MockWebGPU.h(110,7):
      参见“wgpu::Instance”的声明
  
F:\cmo-dev\my_osgearth_web\osgearth_origin\VulkanSceneGraph\vsg_webgpu\src\app\Window.cpp(344,52): error C2065: “desc”: 未声明的标识符
F:\cmo-dev\my_osgearth_web\osgearth_origin\VulkanSceneGraph\vsg_webgpu\src\app\Window.cpp(351,20): error C2676: 二进制“!=”:“vsg_webgpu::WGPUSurface”不定义该运算符或到预定义运算符可接收的类型的转换
  Draw.cpp
F:\cmo-dev\my_osgearth_web\osgearth_origin\VulkanSceneGraph\vsg_webgpu\src\commands\Draw.cpp(13,10): error C1083: 无法打开包括文件: “vsg_webgpu/commands/Draw.h”: No such file or directory
  DrawIndexed.cpp
F:\cmo-dev\my_osgearth_web\osgearth_origin\VulkanSceneGraph\vsg_webgpu\src\commands\DrawIndexed.cpp(13,10): error C1083: 无法打开包括文件: “vsg_webgpu/commands/DrawIndexed.h”: No such file or directory
  Dispatch.cpp
F:\cmo-dev\my_osgearth_web\osgearth_origin\VulkanSceneGraph\vsg_webgpu\src\commands\Dispatch.cpp(13,10): error C1083: 无法打开包括文件: “vsg_webgpu/commands/Dispatch.h”: No such file or directory
  正在生成代码...
  正在编译...
  BindDescriptorSet.cpp
F:\cmo-dev\my_osgearth_web\osgearth_origin\VulkanSceneGraph\vsg_webgpu\src\commands\BindDescriptorSet.cpp(13,10): error C1083: 无法打开包括文件: “vsg_webgpu/commands/BindDescriptorSet.h”: No such file or directory
  BindVertexBuffers.cpp
F:\cmo-dev\my_osgearth_web\osgearth_origin\VulkanSceneGraph\vsg_webgpu\src\commands\BindVertexBuffers.cpp(13,10): error C1083: 无法打开包括文件: “vsg_webgpu/commands/BindVertexBuffers.h”: No such file or directory
  BindIndexBuffer.cpp
F:\cmo-dev\my_osgearth_web\osgearth_origin\VulkanSceneGraph\vsg_webgpu\src\commands\BindIndexBuffer.cpp(13,10): error C1083: 无法打开包括文件: “vsg_webgpu/commands/BindIndexBuffer.h”: No such file or directory
  Builder.cpp
F:\cmo-dev\my_osgearth_web\osgearth_origin\VulkanSceneGraph\vsg_webgpu\src\utils\Builder.cpp(13,10): error C1083: 无法打开包括文件: “vsg_webgpu/utils/Builder.h”: No such file or directory
  ShaderCompiler.cpp
F:\cmo-dev\my_osgearth_web\osgearth_origin\VulkanSceneGraph\vsg_webgpu\src\utils\ShaderCompiler.cpp(13,10): error C1083: 无法打开包括文件: “vsg_webgpu/utils/ShaderCompiler.h”: No such file or directory
  正在生成代码...
