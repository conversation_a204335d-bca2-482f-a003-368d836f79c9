#pragma once

/* <editor-fold desc="MIT License">

Copyright(c) 2025 Robert <PERSON>

Permission is hereby granted, free of charge, to any person obtaining a copy of this software and associated documentation files (the "Software"), to deal in the Software without restriction, including without limitation the rights to use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of the Software, and to permit persons to whom the Software is furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.

</editor-fold> */

#include <cstdint>

namespace vsg
{

    /// max slot values used for general state and view state related State::stateStacks
    struct Slots
    {
        uint32_t state = 0;
        uint32_t view = 0;

        /// return maximum of the state and view slot numbers
        uint32_t max() const
        {
            return view > state ? view : state;
        }

        void merge(const Slots& rhs)
        {
            if (rhs.state > state) state = rhs.state;
            if (rhs.view > view) view = rhs.view;
        }
    };

} // namespace vsg
