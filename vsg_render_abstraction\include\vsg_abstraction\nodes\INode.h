#pragma once

/* <editor-fold desc="MIT License">

Copyright(c) 2024 VSG Render Engine Abstraction

Permission is hereby granted, free of charge, to any person obtaining a copy of this software and associated documentation files (the "Software"), to deal in the Software without restriction, including without limitation the rights to use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of the Software, and to permit persons to whom the Software is furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.

</editor-fold> */

#include <vsg_abstraction/core/Types.h>
#include <vsg_abstraction/core/Export.h>
#include <string>
#include <vector>
#include <unordered_map>

// 前向声明VSG类型
namespace vsg {
    class Node;
    class Group;
    class Transform;
    class MatrixTransform;
    class Geometry;
    class StateGroup;
    template<typename T> class ref_ptr;
}

namespace vsg_abstraction {

// 前向声明
class IVisitor;
class IConstVisitor;

/**
 * @brief 节点类型枚举
 */
enum class NodeType {
    Node,           ///< 基础节点
    Group,          ///< 组节点
    Transform,      ///< 变换节点
    MatrixTransform,///< 矩阵变换节点
    Geometry,       ///< 几何节点
    StateGroup,     ///< 状态组节点
    LOD,            ///< 细节层次节点
    Switch,         ///< 开关节点
    Billboard,      ///< 广告牌节点
    Light,          ///< 光源节点
    Camera          ///< 相机节点
};

/**
 * @brief 场景节点抽象接口
 * 
 * 这是场景图中所有节点的基类，定义了场景图的基本操作。
 * 设计目标：
 * 1. 与VSG Node接口保持高度兼容
 * 2. 支持访问者模式
 * 3. 提供VSG对象的直接访问
 * 4. 支持用户数据和元数据
 */
class VSG_ABSTRACTION_DECLSPEC INode {
public:
    virtual ~INode() = default;

    // ========== 基本属性 ==========
    
    /**
     * @brief 获取节点类型
     */
    virtual NodeType getNodeType() const = 0;
    
    /**
     * @brief 获取节点名称
     */
    virtual const std::string& getName() const = 0;
    
    /**
     * @brief 设置节点名称
     */
    virtual void setName(const std::string& name) = 0;
    
    /**
     * @brief 获取节点ID
     */
    virtual uint64_t getNodeId() const = 0;

    // ========== 访问者模式 ==========
    
    /**
     * @brief 接受访问者
     * @param visitor 访问者对象
     */
    virtual void accept(IVisitor& visitor) = 0;
    
    /**
     * @brief 接受常量访问者
     * @param visitor 常量访问者对象
     */
    virtual void accept(IConstVisitor& visitor) const = 0;
    
    /**
     * @brief 遍历子节点
     * @param visitor 访问者对象
     */
    virtual void traverse(IVisitor& visitor) = 0;
    
    /**
     * @brief 遍历子节点（常量版本）
     * @param visitor 常量访问者对象
     */
    virtual void traverse(IConstVisitor& visitor) const = 0;

    // ========== 包围盒 ==========
    
    /**
     * @brief 获取局部包围盒
     * @return 包围盒 [min_x, min_y, min_z, max_x, max_y, max_z]
     */
    virtual std::array<double, 6> getLocalBounds() const = 0;
    
    /**
     * @brief 获取世界包围盒
     */
    virtual std::array<double, 6> getWorldBounds() const = 0;
    
    /**
     * @brief 计算包围盒（递归计算所有子节点）
     */
    virtual void computeBounds() = 0;

    // ========== 用户数据 ==========
    
    /**
     * @brief 设置用户数据
     * @param key 数据键
     * @param data 数据指针
     */
    virtual void setUserData(const std::string& key, void* data) = 0;
    
    /**
     * @brief 获取用户数据
     * @param key 数据键
     */
    virtual void* getUserData(const std::string& key) const = 0;
    
    /**
     * @brief 移除用户数据
     * @param key 数据键
     */
    virtual void removeUserData(const std::string& key) = 0;
    
    /**
     * @brief 检查是否有用户数据
     * @param key 数据键
     */
    virtual bool hasUserData(const std::string& key) const = 0;

    // ========== VSG兼容性接口 ==========
    
    /**
     * @brief 获取VSG节点对象
     * @return VSG节点指针
     */
    virtual vsg::ref_ptr<vsg::Node> getVSGNode() const = 0;
    
    /**
     * @brief 从VSG节点创建抽象节点
     * @param vsgNode VSG节点
     * @return 抽象节点指针
     */
    static ref_ptr<INode> createFromVSG(vsg::ref_ptr<vsg::Node> vsgNode);

    // ========== 原生对象访问 ==========
    
    /**
     * @brief 获取原生节点对象（用于高级用法）
     * @return 原生对象指针，类型取决于具体实现
     */
    virtual void* getNativeHandle() const = 0;
};

/**
 * @brief 组节点抽象接口
 * 
 * 扩展基础节点，添加子节点管理功能
 */
class VSG_ABSTRACTION_DECLSPEC IGroup : public INode {
public:
    // ========== 子节点管理 ==========
    
    /**
     * @brief 添加子节点
     * @param child 要添加的子节点
     */
    virtual void addChild(ref_ptr<INode> child) = 0;
    
    /**
     * @brief 移除子节点
     * @param child 要移除的子节点
     */
    virtual void removeChild(ref_ptr<INode> child) = 0;
    
    /**
     * @brief 移除指定索引的子节点
     * @param index 子节点索引
     */
    virtual void removeChild(size_t index) = 0;
    
    /**
     * @brief 移除所有子节点
     */
    virtual void removeAllChildren() = 0;
    
    /**
     * @brief 获取子节点数量
     */
    virtual size_t getNumChildren() const = 0;
    
    /**
     * @brief 获取指定索引的子节点
     * @param index 子节点索引
     */
    virtual ref_ptr<INode> getChild(size_t index) const = 0;
    
    /**
     * @brief 获取所有子节点
     */
    virtual const std::vector<ref_ptr<INode>>& getChildren() const = 0;
    
    /**
     * @brief 查找子节点
     * @param name 节点名称
     * @return 找到的节点，未找到返回nullptr
     */
    virtual ref_ptr<INode> findChild(const std::string& name) const = 0;

    // ========== VSG兼容性接口 ==========
    
    /**
     * @brief 获取VSG组节点对象
     * @return VSG组节点指针
     */
    virtual vsg::ref_ptr<vsg::Group> getVSGGroup() const = 0;
};

/**
 * @brief 变换节点抽象接口
 * 
 * 扩展组节点，添加变换功能
 */
class VSG_ABSTRACTION_DECLSPEC ITransform : public IGroup {
public:
    // ========== 变换操作 ==========
    
    /**
     * @brief 设置变换矩阵
     * @param matrix 4x4变换矩阵
     */
    virtual void setMatrix(const mat4& matrix) = 0;
    
    /**
     * @brief 获取变换矩阵
     */
    virtual const mat4& getMatrix() const = 0;
    
    /**
     * @brief 获取世界变换矩阵
     */
    virtual mat4 getWorldMatrix() const = 0;
    
    /**
     * @brief 设置位置
     * @param position 位置向量
     */
    virtual void setPosition(const vec3& position) = 0;
    
    /**
     * @brief 获取位置
     */
    virtual vec3 getPosition() const = 0;
    
    /**
     * @brief 设置旋转（四元数）
     * @param rotation 旋转四元数 [x, y, z, w]
     */
    virtual void setRotation(const vec4& rotation) = 0;
    
    /**
     * @brief 获取旋转
     */
    virtual vec4 getRotation() const = 0;
    
    /**
     * @brief 设置缩放
     * @param scale 缩放向量
     */
    virtual void setScale(const vec3& scale) = 0;
    
    /**
     * @brief 获取缩放
     */
    virtual vec3 getScale() const = 0;

    // ========== VSG兼容性接口 ==========
    
    /**
     * @brief 获取VSG变换节点对象
     * @return VSG变换节点指针
     */
    virtual vsg::ref_ptr<vsg::MatrixTransform> getVSGTransform() const = 0;
};

/**
 * @brief 几何节点抽象接口
 * 
 * 扩展基础节点，添加几何体相关的功能
 */
class VSG_ABSTRACTION_DECLSPEC IGeometry : public INode {
public:
    // ========== 几何数据 ==========
    
    /**
     * @brief 设置顶点数据
     * @param vertices 顶点数组
     */
    virtual void setVertices(const std::vector<vec3>& vertices) = 0;
    
    /**
     * @brief 获取顶点数据
     */
    virtual const std::vector<vec3>& getVertices() const = 0;
    
    /**
     * @brief 设置法线数据
     * @param normals 法线数组
     */
    virtual void setNormals(const std::vector<vec3>& normals) = 0;
    
    /**
     * @brief 获取法线数据
     */
    virtual const std::vector<vec3>& getNormals() const = 0;
    
    /**
     * @brief 设置纹理坐标
     * @param texCoords 纹理坐标数组
     */
    virtual void setTexCoords(const std::vector<vec2>& texCoords) = 0;
    
    /**
     * @brief 获取纹理坐标
     */
    virtual const std::vector<vec2>& getTexCoords() const = 0;
    
    /**
     * @brief 设置索引数据
     * @param indices 索引数组
     */
    virtual void setIndices(const std::vector<uint32_t>& indices) = 0;
    
    /**
     * @brief 获取索引数据
     */
    virtual const std::vector<uint32_t>& getIndices() const = 0;
    
    /**
     * @brief 设置图元类型
     * @param topology 图元类型
     */
    virtual void setPrimitiveTopology(PrimitiveTopology topology) = 0;
    
    /**
     * @brief 获取图元类型
     */
    virtual PrimitiveTopology getPrimitiveTopology() const = 0;

    // ========== VSG兼容性接口 ==========
    
    /**
     * @brief 获取VSG几何节点对象
     * @return VSG几何节点指针
     */
    virtual vsg::ref_ptr<vsg::Geometry> getVSGGeometry() const = 0;
};

/**
 * @brief 状态组节点抽象接口
 * 
 * 扩展组节点，添加渲染状态管理功能
 */
class VSG_ABSTRACTION_DECLSPEC IStateGroup : public IGroup {
public:
    // ========== 状态管理 ==========
    
    /**
     * @brief 添加状态命令
     * @param stateCommand 状态命令
     */
    virtual void addStateCommand(ref_ptr<INode> stateCommand) = 0;
    
    /**
     * @brief 移除状态命令
     * @param stateCommand 状态命令
     */
    virtual void removeStateCommand(ref_ptr<INode> stateCommand) = 0;
    
    /**
     * @brief 获取所有状态命令
     */
    virtual const std::vector<ref_ptr<INode>>& getStateCommands() const = 0;

    // ========== VSG兼容性接口 ==========
    
    /**
     * @brief 获取VSG状态组节点对象
     * @return VSG状态组节点指针
     */
    virtual vsg::ref_ptr<vsg::StateGroup> getVSGStateGroup() const = 0;
};

} // namespace vsg_abstraction
