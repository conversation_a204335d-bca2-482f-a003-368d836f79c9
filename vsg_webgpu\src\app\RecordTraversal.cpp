/* <editor-fold desc="MIT License">

Copyright(c) 2024 VSG WebGPU Extension

Permission is hereby granted, free of charge, to any person obtaining a copy of this software and associated documentation files (the "Software"), to deal in the Software without restriction, including without limitation the rights to use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of the Software, and to permit persons to whom the Software is furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.

</editor-fold> */

#include <vsg_webgpu/app/RecordTraversal.h>
#include <vsg_webgpu/vk/Device.h>
#include <vsg_webgpu/state/GraphicsPipeline.h>

using namespace vsg_webgpu;

RecordTraversal::RecordTraversal(vsg::ref_ptr<CommandBuffer> commandBuffer, 
                                uint32_t maxSlot, 
                                vsg::ref_ptr<State> state) :
    maxSlot(maxSlot),
    _commandBuffer(commandBuffer),
    _state(state)
{
    VSG_WEBGPU_LOG_DEBUG("RecordTraversal::RecordTraversal()");
}

RecordTraversal::~RecordTraversal()
{
    VSG_WEBGPU_LOG_DEBUG("RecordTraversal::~RecordTraversal()");
}

void RecordTraversal::setCommandBuffer(vsg::ref_ptr<CommandBuffer> commandBuffer)
{
    _commandBuffer = commandBuffer;
    if (_state)
    {
        _state->connect(commandBuffer);
    }
}

void RecordTraversal::setState(vsg::ref_ptr<State> state)
{
    _state = state;
    if (_commandBuffer)
    {
        _state->connect(_commandBuffer);
    }
}

void RecordTraversal::apply(vsg::Object& object)
{
    // 默认处理
    object.traverse(*this);
}

void RecordTraversal::apply(vsg::Group& group)
{
    // 遍历所有子节点
    for (auto& child : group.children)
    {
        if (child)
        {
            child->traverse(*this);
        }
    }
}

void RecordTraversal::apply(vsg::StateGroup& stateGroup)
{
    // 推入状态
    pushState();
    
    // 应用状态组的状态命令
    if (_state)
    {
        _state->apply(&stateGroup);
    }
    
    _updateStatistics("state_change");
    
    // 遍历子节点
    for (auto& child : stateGroup.children)
    {
        if (child)
        {
            child->traverse(*this);
        }
    }
    
    // 弹出状态
    popState();
}

void RecordTraversal::apply(vsg::Transform& transform)
{
    // 推入变换
    pushState();
    
    // 遍历子节点
    for (auto& child : transform.children)
    {
        if (child)
        {
            child->traverse(*this);
        }
    }
    
    // 弹出变换
    popState();
}

void RecordTraversal::apply(vsg::MatrixTransform& matrixTransform)
{
    // 推入矩阵变换
    pushState();
    
    // 在实际实现中，这里会推入变换矩阵到矩阵栈
    if (_state)
    {
        // 这里应该推入matrixTransform的矩阵，但在mock实现中我们简化处理
        _state->pushModelMatrix(vsg::mat4{}); // 使用单位矩阵
    }
    
    // 遍历子节点
    for (auto& child : matrixTransform.children)
    {
        if (child)
        {
            child->traverse(*this);
        }
    }
    
    // 弹出矩阵变换
    if (_state)
    {
        _state->popModelMatrix();
    }
    
    popState();
}

void RecordTraversal::apply(vsg::Geometry& geometry)
{
    // 录制几何体绘制命令
    for (auto& command : geometry.commands)
    {
        if (command)
        {
            command->accept(*this);
        }
    }
}

void RecordTraversal::apply(vsg::VertexIndexDraw& vid)
{
    // 录制顶点索引绘制命令
    _updateStatistics("draw_call");
    _updateStatistics("triangle", 1); // 假设每个绘制调用有1个三角形
}

void RecordTraversal::apply(vsg::BindVertexBuffers& bvb)
{
    // 绑定顶点缓冲区
    if (_state && _commandBuffer && _commandBuffer->inRenderPass())
    {
        // 在实际实现中，这里会绑定顶点缓冲区到WebGPU渲染通道
        _updateStatistics("vertex_buffer_bind");
    }
}

void RecordTraversal::apply(vsg::BindIndexBuffer& bib)
{
    // 绑定索引缓冲区
    if (_state && _commandBuffer && _commandBuffer->inRenderPass())
    {
        // 在实际实现中，这里会绑定索引缓冲区到WebGPU渲染通道
        _updateStatistics("index_buffer_bind");
    }
}

void RecordTraversal::apply(vsg::Draw& draw)
{
    // 录制绘制命令
    if (_state && _commandBuffer && _commandBuffer->inRenderPass())
    {
        auto renderPass = _commandBuffer->getCurrentRenderPass();
        if (renderPass)
        {
            // 在实际实现中，这里会调用WebGPU的绘制命令
            // renderPass.Draw(vertexCount, instanceCount, firstVertex, firstInstance);
        }
    }
    
    _updateStatistics("draw_call");
    _updateStatistics("vertex", 3); // 假设绘制3个顶点
}

void RecordTraversal::apply(vsg::DrawIndexed& drawIndexed)
{
    // 录制索引绘制命令
    if (_state && _commandBuffer && _commandBuffer->inRenderPass())
    {
        auto renderPass = _commandBuffer->getCurrentRenderPass();
        if (renderPass)
        {
            // 在实际实现中，这里会调用WebGPU的索引绘制命令
            // renderPass.DrawIndexed(indexCount, instanceCount, firstIndex, baseVertex, firstInstance);
        }
    }
    
    _updateStatistics("draw_call");
    _updateStatistics("triangle", 1); // 假设每个索引绘制调用有1个三角形
}

void RecordTraversal::apply(vsg::StateCommand& stateCommand)
{
    // 应用状态命令
    if (_state)
    {
        _state->apply(&stateCommand);
        
        // 特殊处理管线绑定
        if (auto bindPipeline = dynamic_cast<BindGraphicsPipeline*>(&stateCommand))
        {
            bindPipeline->record(*_state);
            _updateStatistics("pipeline_bind");
        }
    }
}

void RecordTraversal::beginRenderPass(const WGPURenderPassDescriptor& descriptor)
{
    if (_commandBuffer)
    {
        auto renderPass = _commandBuffer->beginRenderPass(descriptor);
        if (_state)
        {
            _state->setRenderPassEncoder(renderPass);
        }
    }
}

void RecordTraversal::endRenderPass()
{
    if (_commandBuffer)
    {
        _commandBuffer->endRenderPass();
        if (_state)
        {
            _state->setRenderPassEncoder({});
        }
    }
}

void RecordTraversal::beginComputePass(const WGPUComputePassDescriptor& descriptor)
{
    if (_commandBuffer)
    {
        auto computePass = _commandBuffer->beginComputePass(descriptor);
        if (_state)
        {
            _state->setComputePassEncoder(computePass);
        }
    }
}

void RecordTraversal::endComputePass()
{
    if (_commandBuffer)
    {
        _commandBuffer->endComputePass();
        if (_state)
        {
            _state->setComputePassEncoder({});
        }
    }
}

void RecordTraversal::pushState()
{
    // 推入状态帧
    StateFrame frame;
    if (_state)
    {
        frame.projectionMatrix = _state->getProjectionMatrix();
        frame.modelviewMatrix = _state->getViewMatrix();
    }
    _stateStack.push_back(frame);
}

void RecordTraversal::popState()
{
    // 弹出状态帧
    if (!_stateStack.empty())
    {
        _stateStack.pop_back();
    }
}

void RecordTraversal::_recordStateCommands()
{
    if (_state)
    {
        _state->record();
    }
}

void RecordTraversal::_updateStatistics(const std::string& operation, uint32_t count)
{
    if (operation == "draw_call")
    {
        _statistics.numDrawCalls += count;
    }
    else if (operation == "triangle")
    {
        _statistics.numTriangles += count;
    }
    else if (operation == "vertex")
    {
        _statistics.numVertices += count;
    }
    else if (operation == "state_change")
    {
        _statistics.numStateChanges += count;
    }
    else if (operation == "pipeline_bind")
    {
        _statistics.numPipelineBinds += count;
    }
}
