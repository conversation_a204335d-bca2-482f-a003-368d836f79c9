#pragma once

// Mock VSG headers for building without VSG installed
// This is a minimal implementation for testing purposes only

#include <memory>
#include <vector>
#include <functional>
#include <string>
#include <array>
#include <stack>
#include <iostream>

// Mock VSG namespace
namespace vsg {

// Basic types
using ref_ptr_count = int;

// Math types
struct vec3 {
    float x, y, z;
    vec3() : x(0), y(0), z(0) {}
    vec3(float x_, float y_, float z_) : x(x_), y(y_), z(z_) {}
};

struct vec4 {
    float x, y, z, w;
    vec4() : x(0), y(0), z(0), w(0) {}
    vec4(float x_, float y_, float z_, float w_) : x(x_), y(y_), z(z_), w(w_) {}
};

struct mat4 {
    std::array<std::array<float, 4>, 4> data;
    mat4() {
        for (int i = 0; i < 4; ++i) {
            for (int j = 0; j < 4; ++j) {
                data[i][j] = (i == j) ? 1.0f : 0.0f;
            }
        }
    }
    
    std::array<float, 4>& operator[](int i) { return data[i]; }
    const std::array<float, 4>& operator[](int i) const { return data[i]; }
    
    bool operator==(const mat4& other) const {
        for (int i = 0; i < 4; ++i) {
            for (int j = 0; j < 4; ++j) {
                if (data[i][j] != other.data[i][j]) return false;
            }
        }
        return true;
    }
    
    bool operator!=(const mat4& other) const { return !(*this == other); }
};

using dmat4 = mat4;

// Array types
template<typename T>
class Array : public std::vector<T> {
public:
    static std::shared_ptr<Array<T>> create(std::initializer_list<T> init = {}) {
        auto array = std::make_shared<Array<T>>();
        array->assign(init);
        return array;
    }
};

using vec3Array = Array<vec3>;
using ushortArray = Array<unsigned short>;

// Math functions
inline mat4 perspective(float fovy, float aspect, float near, float far) {
    mat4 result;
    // Simplified perspective matrix
    float f = 1.0f / std::tan(fovy * 0.5f);
    result[0][0] = f / aspect;
    result[1][1] = f;
    result[2][2] = (far + near) / (near - far);
    result[2][3] = (2.0f * far * near) / (near - far);
    result[3][2] = -1.0f;
    result[3][3] = 0.0f;
    return result;
}

inline mat4 lookAt(const vec3& eye, const vec3& center, const vec3& up) {
    mat4 result;
    // Simplified lookAt matrix
    return result;
}

inline mat4 translate(float x, float y, float z) {
    mat4 result;
    result[3][0] = x;
    result[3][1] = y;
    result[3][2] = z;
    return result;
}

// Core object system
template<typename T>
using ref_ptr = std::shared_ptr<T>;

class Object {
public:
    virtual ~Object() = default;
    
    template<typename T, typename... Args>
    static ref_ptr<T> create(Args&&... args) {
        return std::make_shared<T>(std::forward<Args>(args)...);
    }
    
    virtual void accept(class Visitor&) {}
    virtual int compare(const Object&) const { return 0; }
};

template<typename Base, typename Derived>
class Inherit : public Base {
public:
    template<typename... Args>
    Inherit(Args&&... args) : Base(std::forward<Args>(args)...) {}
    
    template<typename T = Derived, typename... Args>
    static ref_ptr<T> create(Args&&... args) {
        return std::make_shared<T>(std::forward<Args>(args)...);
    }
};

// Visitor pattern
class Visitor {
public:
    virtual ~Visitor() = default;
    virtual void apply(Object&) {}
    virtual void apply(class Group&) {}
    virtual void apply(class StateGroup&) {}
    virtual void apply(class Transform&) {}
    virtual void apply(class MatrixTransform&) {}
    virtual void apply(class Geometry&) {}
    virtual void apply(class VertexIndexDraw&) {}
    virtual void apply(class BindVertexBuffers&) {}
    virtual void apply(class BindIndexBuffer&) {}
    virtual void apply(class Draw&) {}
    virtual void apply(class DrawIndexed&) {}
};

// Scene graph nodes
class Node : public Object {
public:
    virtual void traverse(Visitor& visitor) { visitor.apply(*this); }
};

class Group : public Inherit<Node, Group> {
public:
    std::vector<ref_ptr<Node>> children;
    
    void addChild(ref_ptr<Node> child) {
        if (child) children.push_back(child);
    }
    
    void traverse(Visitor& visitor) override {
        visitor.apply(*this);
        for (auto& child : children) {
            if (child) child->traverse(visitor);
        }
    }
};

class Transform : public Inherit<Group, Transform> {};
class MatrixTransform : public Inherit<Transform, MatrixTransform> {};

// State management
class StateCommand : public Object {
public:
    uint32_t slot = 0;
};

class StateGroup : public Inherit<Group, StateGroup> {
public:
    std::vector<ref_ptr<StateCommand>> stateCommands;
};

// Geometry
class Geometry : public Inherit<Node, Geometry> {
public:
    std::vector<ref_ptr<Object>> arrays;
    ref_ptr<Object> indices;
    std::vector<ref_ptr<Object>> commands;
};

class Draw : public Object {
public:
    static ref_ptr<Draw> create(uint32_t vertexCount, uint32_t instanceCount = 1, uint32_t firstVertex = 0, uint32_t firstInstance = 0) {
        auto draw = std::make_shared<Draw>();
        return draw;
    }
};

class DrawIndexed : public Object {
public:
    static ref_ptr<DrawIndexed> create(uint32_t indexCount, uint32_t instanceCount = 1, uint32_t firstIndex = 0, int32_t vertexOffset = 0, uint32_t firstInstance = 0) {
        auto draw = std::make_shared<DrawIndexed>();
        return draw;
    }
};

class VertexIndexDraw : public Object {};
class BindVertexBuffers : public Object {};
class BindIndexBuffer : public Object {};

// Utility classes
class ScratchMemory : public Object {
public:
    static ref_ptr<ScratchMemory> create(size_t size) {
        return std::make_shared<ScratchMemory>();
    }
    void reset() {}
};

// Mask and slots
using Mask = uint32_t;
const Mask MASK_ALL = 0xFFFFFFFF;
const Mask MASK_OFF = 0x00000000;

struct Slots {
    uint32_t slot0 = 0, slot1 = 0, slot2 = 0, slot3 = 0;
    Slots() = default;
    Slots(uint32_t s0, uint32_t s1, uint32_t s2, uint32_t s3) : slot0(s0), slot1(s1), slot2(s2), slot3(s3) {}
    uint32_t max() const { return std::max({slot0, slot1, slot2, slot3}); }
};

// Matrix stack
template<typename T>
class StateStack {
public:
    void push(const T& value) { stack.push(value); }
    void pop() { if (!stack.empty()) stack.pop(); }
    const T& top() const { return stack.top(); }
    bool empty() const { return stack.empty(); }
private:
    std::stack<T> stack;
};

struct MatrixStack {
    using value_type = mat4;
    uint32_t maxDepth;
    
    MatrixStack(uint32_t depth) : maxDepth(depth) {}
};

// Frustum
struct Frustum {
    // Mock frustum implementation
};

// Device features
class DeviceFeatures : public Object {};

// Logger
inline void info(const std::string& msg) { std::cout << "[INFO] " << msg << std::endl; }
inline void warn(const std::string& msg) { std::cout << "[WARN] " << msg << std::endl; }
inline void debug(const std::string& msg) { std::cout << "[DEBUG] " << msg << std::endl; }

// Type name macro
#define VSG_type_name(T) // Mock implementation

} // namespace vsg
