/* <editor-fold desc="MIT License">

Copyright(c) 2024 VSG Render Engine Abstraction

Permission is hereby granted, free of charge, to any person obtaining a copy of this software and associated documentation files (the "Software"), to deal in the Software without restriction, including without limitation the rights to use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of the Software, and to permit persons to whom the Software is furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.

</editor-fold> */

/**
 * @file test_vsg_integration.cpp
 * @brief VSG集成测试程序
 * 
 * 这个测试程序验证VSG抽象层与原生VSG的集成是否正确工作。
 */

#include <vsg_abstraction/core/RenderEngineManager.h>
#include <vsg_abstraction/core/RenderEngineFactory.h>
#include <vsg_abstraction/backends/VSGRenderEngine.h>

#include <iostream>
#include <chrono>
#include <thread>

// 如果有VSG库，包含VSG头文件进行对比测试
#ifdef VSG_ABSTRACTION_SUPPORT_VSG
#include <vsg/all.h>
#endif

using namespace vsg_abstraction;

// 测试结果结构
struct TestResult {
    std::string testName;
    bool passed;
    std::string errorMessage;
    double executionTime;
};

std::vector<TestResult> testResults;

// 测试辅助宏
#define TEST_ASSERT(condition, message) \
    if (!(condition)) { \
        result.passed = false; \
        result.errorMessage = message; \
        return result; \
    }

#define RUN_TEST(testFunc) \
    do { \
        auto start = std::chrono::high_resolution_clock::now(); \
        auto result = testFunc(); \
        auto end = std::chrono::high_resolution_clock::now(); \
        result.executionTime = std::chrono::duration<double, std::milli>(end - start).count(); \
        testResults.push_back(result); \
        std::cout << "[" << (result.passed ? "PASS" : "FAIL") << "] " \
                  << result.testName << " (" << result.executionTime << "ms)"; \
        if (!result.passed) { \
            std::cout << " - " << result.errorMessage; \
        } \
        std::cout << std::endl; \
    } while(0)

// ========== 基础功能测试 ==========

TestResult testRenderEngineCreation() {
    TestResult result;
    result.testName = "Render Engine Creation";
    result.passed = true;
    
    try {
        // 测试创建VSG渲染引擎
        auto engine = RenderEngineFactory::create(RenderBackend::Vulkan);
        TEST_ASSERT(engine != nullptr, "Failed to create VSG render engine");
        
        // 测试引擎类型
        TEST_ASSERT(engine->getBackendType() == RenderBackend::Vulkan, 
                   "Engine backend type mismatch");
        
        // 测试初始化
        bool initialized = engine->initialize();
        TEST_ASSERT(initialized, "Failed to initialize render engine");
        
        // 测试引擎信息
        auto info = engine->getEngineInfo();
        TEST_ASSERT(!info.name.empty(), "Engine name is empty");
        TEST_ASSERT(!info.version.empty(), "Engine version is empty");
        
        std::cout << "  Engine: " << info.name << " v" << info.version << std::endl;
        
    } catch (const std::exception& e) {
        result.passed = false;
        result.errorMessage = std::string("Exception: ") + e.what();
    }
    
    return result;
}

TestResult testSceneGraphCreation() {
    TestResult result;
    result.testName = "Scene Graph Creation";
    result.passed = true;
    
    try {
        auto& manager = getRenderManager();
        
        // 确保有可用的引擎
        if (!manager.hasEngine()) {
            bool switched = manager.switchBackend(RenderBackend::Vulkan);
            if (!switched) {
                switched = manager.switchBackend(RenderBackend::Mock);
            }
            TEST_ASSERT(switched, "Failed to switch to any backend");
        }
        
        // 测试创建场景图节点
        auto group = manager.createGroup();
        TEST_ASSERT(group != nullptr, "Failed to create group node");
        
        auto transform = manager.createTransform();
        TEST_ASSERT(transform != nullptr, "Failed to create transform node");
        
        auto geometry = manager.createGeometry();
        TEST_ASSERT(geometry != nullptr, "Failed to create geometry node");
        
        // 测试场景图层次结构
        transform->addChild(geometry);
        group->addChild(transform);
        
        TEST_ASSERT(group->getNumChildren() == 1, "Group should have 1 child");
        TEST_ASSERT(transform->getNumChildren() == 1, "Transform should have 1 child");
        
        // 测试节点属性
        group->setName("TestGroup");
        TEST_ASSERT(group->getName() == "TestGroup", "Group name mismatch");
        
        transform->setName("TestTransform");
        TEST_ASSERT(transform->getName() == "TestTransform", "Transform name mismatch");
        
    } catch (const std::exception& e) {
        result.passed = false;
        result.errorMessage = std::string("Exception: ") + e.what();
    }
    
    return result;
}

TestResult testVSGCompatibility() {
    TestResult result;
    result.testName = "VSG Compatibility";
    result.passed = true;
    
    try {
        auto& manager = getRenderManager();
        
        // 尝试切换到VSG后端
        bool switched = manager.switchBackend(RenderBackend::Vulkan);
        if (!switched) {
            result.passed = false;
            result.errorMessage = "VSG backend not available, skipping compatibility test";
            return result;
        }
        
        // 测试VSG兼容性接口
        auto vsgGroup = manager.createVSGGroup();
        TEST_ASSERT(vsgGroup != nullptr, "Failed to create VSG group");
        
        auto vsgTransform = manager.createVSGTransform();
        TEST_ASSERT(vsgTransform != nullptr, "Failed to create VSG transform");
        
        auto vsgGeometry = manager.createVSGGeometry();
        TEST_ASSERT(vsgGeometry != nullptr, "Failed to create VSG geometry");
        
        // 测试VSG对象的使用
        vsgTransform->addChild(vsgGeometry);
        vsgGroup->addChild(vsgTransform);
        
        TEST_ASSERT(vsgGroup->children.size() == 1, "VSG group should have 1 child");
        TEST_ASSERT(vsgTransform->children.size() == 1, "VSG transform should have 1 child");
        
#ifdef VSG_ABSTRACTION_SUPPORT_VSG
        // 如果有VSG库，测试与原生VSG的互操作性
        auto nativeGroup = vsg::Group::create();
        vsgGroup->addChild(nativeGroup);
        
        TEST_ASSERT(vsgGroup->children.size() == 2, "VSG group should have 2 children after adding native VSG node");
#endif
        
    } catch (const std::exception& e) {
        result.passed = false;
        result.errorMessage = std::string("Exception: ") + e.what();
    }
    
    return result;
}

TestResult testMultiBackendSupport() {
    TestResult result;
    result.testName = "Multi-Backend Support";
    result.passed = true;
    
    try {
        auto& manager = getRenderManager();
        
        // 获取支持的后端
        auto supportedBackends = RenderEngineFactory::getSupportedBackends();
        TEST_ASSERT(!supportedBackends.empty(), "No supported backends found");
        
        std::cout << "  Supported backends: ";
        for (auto backend : supportedBackends) {
            std::cout << getRenderBackendName(backend) << " ";
        }
        std::cout << std::endl;
        
        // 测试后端切换
        std::vector<RenderBackend> backendsToTest = {
            RenderBackend::Vulkan,
            RenderBackend::Mock
        };
        
        int successfulSwitches = 0;
        for (auto backend : backendsToTest) {
            if (RenderEngineFactory::isBackendSupported(backend)) {
                bool switched = manager.switchBackend(backend);
                if (switched) {
                    successfulSwitches++;
                    
                    // 测试基本功能
                    auto group = manager.createGroup();
                    TEST_ASSERT(group != nullptr, 
                               std::string("Failed to create group with ") + getRenderBackendName(backend) + " backend");
                    
                    auto engineInfo = manager.getEngineInfo();
                    std::cout << "    " << getRenderBackendName(backend) << ": " 
                              << engineInfo.name << " v" << engineInfo.version << std::endl;
                }
            }
        }
        
        TEST_ASSERT(successfulSwitches > 0, "No backend switches were successful");
        
    } catch (const std::exception& e) {
        result.passed = false;
        result.errorMessage = std::string("Exception: ") + e.what();
    }
    
    return result;
}

TestResult testPerformanceAndStatistics() {
    TestResult result;
    result.testName = "Performance and Statistics";
    result.passed = true;
    
    try {
        auto& manager = getRenderManager();
        
        // 确保有可用的引擎
        if (!manager.hasEngine()) {
            bool switched = manager.switchBackend(RenderBackend::Mock);
            TEST_ASSERT(switched, "Failed to switch to Mock backend");
        }
        
        // 重置统计信息
        manager.resetRenderStatistics();
        
        // 创建一些对象来生成统计数据
        for (int i = 0; i < 10; ++i) {
            auto group = manager.createGroup();
            auto transform = manager.createTransform();
            auto geometry = manager.createGeometry();
            
            transform->addChild(geometry);
            group->addChild(transform);
        }
        
        // 获取统计信息
        auto stats = manager.getRenderStatistics();
        
        // 验证统计信息
        TEST_ASSERT(stats.frameCount >= 0, "Frame count should be non-negative");
        TEST_ASSERT(stats.memoryUsed >= 0, "Memory used should be non-negative");
        
        std::cout << "  Statistics: " << stats.frameCount << " frames, " 
                  << (stats.memoryUsed / 1024) << "KB memory" << std::endl;
        
    } catch (const std::exception& e) {
        result.passed = false;
        result.errorMessage = std::string("Exception: ") + e.what();
    }
    
    return result;
}

// ========== 主测试函数 ==========

int main() {
    std::cout << "=== VSG渲染引擎抽象层集成测试 ===" << std::endl;
    std::cout << "版本: " << VERSION_STRING << std::endl;
    std::cout << "平台: " << PLATFORM_NAME << std::endl;
    std::cout << "编译器: " << COMPILER_NAME << std::endl;
    std::cout << std::endl;
    
    std::cout << "后端支持情况：" << std::endl;
    std::cout << "  VSG (Vulkan): " << (SUPPORT_VSG ? "是" : "否") << std::endl;
    std::cout << "  WebGPU: " << (SUPPORT_WEBGPU ? "是" : "否") << std::endl;
    std::cout << "  OpenGL: " << (SUPPORT_OPENGL ? "是" : "否") << std::endl;
    std::cout << "  Mock: " << (SUPPORT_MOCK ? "是" : "否") << std::endl;
    std::cout << std::endl;
    
    // 运行测试
    std::cout << "运行测试..." << std::endl;
    
    RUN_TEST(testRenderEngineCreation);
    RUN_TEST(testSceneGraphCreation);
    RUN_TEST(testVSGCompatibility);
    RUN_TEST(testMultiBackendSupport);
    RUN_TEST(testPerformanceAndStatistics);
    
    // 统计测试结果
    int passedTests = 0;
    int totalTests = testResults.size();
    double totalTime = 0.0;
    
    for (const auto& result : testResults) {
        if (result.passed) {
            passedTests++;
        }
        totalTime += result.executionTime;
    }
    
    std::cout << std::endl;
    std::cout << "=== 测试结果 ===" << std::endl;
    std::cout << "通过: " << passedTests << "/" << totalTests << " 测试" << std::endl;
    std::cout << "总时间: " << totalTime << "ms" << std::endl;
    
    if (passedTests == totalTests) {
        std::cout << "🎉 所有测试通过！" << std::endl;
        return 0;
    } else {
        std::cout << "❌ " << (totalTests - passedTests) << " 个测试失败" << std::endl;
        
        std::cout << std::endl << "失败的测试:" << std::endl;
        for (const auto& result : testResults) {
            if (!result.passed) {
                std::cout << "  - " << result.testName << ": " << result.errorMessage << std::endl;
            }
        }
        
        return 1;
    }
}
