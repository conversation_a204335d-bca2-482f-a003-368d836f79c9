#pragma once

/* <editor-fold desc="MIT License">

Copyright(c) 2024 Rocky Render Engine Refactor

Permission is hereby granted, free of charge, to any person obtaining a copy of this software and associated documentation files (the "Software"), to deal in the Software without restriction, including without limitation the rights to use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of the Software, and to permit persons to whom the Software is furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.

</editor-fold> */

/**
 * @file Config.h
 * @brief Rocky Render Engine Refactor 编译时配置
 * 
 * 这个文件由CMake自动生成，包含了编译时的配置信息。
 */

// ========== 版本信息 ==========

#define ROCKY_RENDER_VERSION_MAJOR @PROJECT_VERSION_MAJOR@
#define ROCKY_RENDER_VERSION_MINOR @PROJECT_VERSION_MINOR@
#define ROCKY_RENDER_VERSION_PATCH @PROJECT_VERSION_PATCH@
#define ROCKY_RENDER_VERSION "@PROJECT_VERSION@"

// ========== 后端支持 ==========

// Vulkan后端支持（通过VSG）
#cmakedefine ROCKY_HAS_VSG
#ifdef ROCKY_HAS_VSG
    #define ROCKY_SUPPORT_VULKAN 1
#else
    #define ROCKY_SUPPORT_VULKAN 0
#endif

// WebGPU后端支持
#cmakedefine ROCKY_HAS_WEBGPU
#ifdef ROCKY_HAS_WEBGPU
    #define ROCKY_SUPPORT_WEBGPU 1
#else
    #define ROCKY_SUPPORT_WEBGPU 0
#endif

// OpenGL后端支持
#cmakedefine ROCKY_HAS_OPENGL
#ifdef ROCKY_HAS_OPENGL
    #define ROCKY_SUPPORT_OPENGL 1
#else
    #define ROCKY_SUPPORT_OPENGL 0
#endif

// Mock后端支持（总是启用）
#define ROCKY_SUPPORT_MOCK 1

// ========== 平台检测 ==========

// 操作系统检测
#if defined(_WIN32) || defined(_WIN64)
    #define ROCKY_PLATFORM_WINDOWS 1
    #define ROCKY_PLATFORM_NAME "Windows"
#elif defined(__linux__)
    #define ROCKY_PLATFORM_LINUX 1
    #define ROCKY_PLATFORM_NAME "Linux"
#elif defined(__APPLE__)
    #define ROCKY_PLATFORM_MACOS 1
    #define ROCKY_PLATFORM_NAME "macOS"
#elif defined(__EMSCRIPTEN__)
    #define ROCKY_PLATFORM_EMSCRIPTEN 1
    #define ROCKY_PLATFORM_NAME "Emscripten"
#else
    #define ROCKY_PLATFORM_UNKNOWN 1
    #define ROCKY_PLATFORM_NAME "Unknown"
#endif

// 编译器检测
#if defined(_MSC_VER)
    #define ROCKY_COMPILER_MSVC 1
    #define ROCKY_COMPILER_NAME "MSVC"
    #define ROCKY_COMPILER_VERSION _MSC_VER
#elif defined(__clang__)
    #define ROCKY_COMPILER_CLANG 1
    #define ROCKY_COMPILER_NAME "Clang"
    #define ROCKY_COMPILER_VERSION (__clang_major__ * 10000 + __clang_minor__ * 100 + __clang_patchlevel__)
#elif defined(__GNUC__)
    #define ROCKY_COMPILER_GCC 1
    #define ROCKY_COMPILER_NAME "GCC"
    #define ROCKY_COMPILER_VERSION (__GNUC__ * 10000 + __GNUC_MINOR__ * 100 + __GNUC_PATCHLEVEL__)
#else
    #define ROCKY_COMPILER_UNKNOWN 1
    #define ROCKY_COMPILER_NAME "Unknown"
    #define ROCKY_COMPILER_VERSION 0
#endif

// ========== 导出宏 ==========

#ifdef ROCKY_PLATFORM_WINDOWS
    #ifdef ROCKY_RENDER_EXPORTS
        #define ROCKY_RENDER_API __declspec(dllexport)
    #else
        #define ROCKY_RENDER_API __declspec(dllimport)
    #endif
    #define ROCKY_RENDER_LOCAL
#else
    #if defined(__GNUC__) && __GNUC__ >= 4
        #define ROCKY_RENDER_API __attribute__((visibility("default")))
        #define ROCKY_RENDER_LOCAL __attribute__((visibility("hidden")))
    #else
        #define ROCKY_RENDER_API
        #define ROCKY_RENDER_LOCAL
    #endif
#endif

// ========== 调试宏 ==========

#ifdef _DEBUG
    #define ROCKY_DEBUG 1
#else
    #define ROCKY_DEBUG 0
#endif

// 断言宏
#if ROCKY_DEBUG
    #include <cassert>
    #define ROCKY_ASSERT(condition) assert(condition)
    #define ROCKY_ASSERT_MSG(condition, message) assert((condition) && (message))
#else
    #define ROCKY_ASSERT(condition) ((void)0)
    #define ROCKY_ASSERT_MSG(condition, message) ((void)0)
#endif

// ========== 日志宏 ==========

#include <iostream>

#define ROCKY_LOG_INFO(msg) std::cout << "[INFO] " << msg << std::endl
#define ROCKY_LOG_WARN(msg) std::cout << "[WARN] " << msg << std::endl
#define ROCKY_LOG_ERROR(msg) std::cerr << "[ERROR] " << msg << std::endl

#if ROCKY_DEBUG
    #define ROCKY_LOG_DEBUG(msg) std::cout << "[DEBUG] " << msg << std::endl
#else
    #define ROCKY_LOG_DEBUG(msg) ((void)0)
#endif

// ========== 性能宏 ==========

// 强制内联
#ifdef ROCKY_COMPILER_MSVC
    #define ROCKY_FORCE_INLINE __forceinline
#elif defined(ROCKY_COMPILER_GCC) || defined(ROCKY_COMPILER_CLANG)
    #define ROCKY_FORCE_INLINE __attribute__((always_inline)) inline
#else
    #define ROCKY_FORCE_INLINE inline
#endif

// 禁用内联
#ifdef ROCKY_COMPILER_MSVC
    #define ROCKY_NO_INLINE __declspec(noinline)
#elif defined(ROCKY_COMPILER_GCC) || defined(ROCKY_COMPILER_CLANG)
    #define ROCKY_NO_INLINE __attribute__((noinline))
#else
    #define ROCKY_NO_INLINE
#endif

// 分支预测提示
#if defined(ROCKY_COMPILER_GCC) || defined(ROCKY_COMPILER_CLANG)
    #define ROCKY_LIKELY(x) __builtin_expect(!!(x), 1)
    #define ROCKY_UNLIKELY(x) __builtin_expect(!!(x), 0)
#else
    #define ROCKY_LIKELY(x) (x)
    #define ROCKY_UNLIKELY(x) (x)
#endif

// ========== 功能检测 ==========

// C++20特性检测
#if __cplusplus >= 202002L
    #define ROCKY_HAS_CPP20 1
    #define ROCKY_HAS_CONCEPTS 1
    #define ROCKY_HAS_MODULES 0  // 模块支持还不完善
#else
    #define ROCKY_HAS_CPP20 0
    #define ROCKY_HAS_CONCEPTS 0
    #define ROCKY_HAS_MODULES 0
#endif

// 线程支持
#include <thread>
#define ROCKY_HAS_THREADS 1

// ========== 常量定义 ==========

namespace rocky {
namespace render {

// 版本信息
constexpr int VERSION_MAJOR = ROCKY_RENDER_VERSION_MAJOR;
constexpr int VERSION_MINOR = ROCKY_RENDER_VERSION_MINOR;
constexpr int VERSION_PATCH = ROCKY_RENDER_VERSION_PATCH;
constexpr const char* VERSION_STRING = ROCKY_RENDER_VERSION;

// 平台信息
constexpr const char* PLATFORM_NAME = ROCKY_PLATFORM_NAME;
constexpr const char* COMPILER_NAME = ROCKY_COMPILER_NAME;
constexpr int COMPILER_VERSION = ROCKY_COMPILER_VERSION;

// 后端支持
constexpr bool SUPPORT_VULKAN = ROCKY_SUPPORT_VULKAN;
constexpr bool SUPPORT_WEBGPU = ROCKY_SUPPORT_WEBGPU;
constexpr bool SUPPORT_OPENGL = ROCKY_SUPPORT_OPENGL;
constexpr bool SUPPORT_MOCK = ROCKY_SUPPORT_MOCK;

} // namespace render
} // namespace rocky
