/* <editor-fold desc="MIT License">

Copyright(c) 2024 VSG WebGPU Extension

Permission is hereby granted, free of charge, to any person obtaining a copy of this software and associated documentation files (the "Software"), to deal in the Software without restriction, including without limitation the rights to use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of the Software, and to permit persons to whom the Software is furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.

</editor-fold> */

#include <vsg_webgpu/all.h>
#include <vsg/all.h>
#include <iostream>
#include <chrono>

using namespace vsg_webgpu;

int main()
{
    try
    {
        std::cout << "VSG WebGPU Performance Example" << std::endl;
        
        // 初始化WebGPU
        initializeWebGPU();
        
        // 创建设备
        auto device = Device::create();
        if (!device || !device->initialize())
        {
            std::cerr << "Failed to initialize WebGPU device" << std::endl;
            return 1;
        }
        
        // 创建窗口
        auto window = Window::create("VSG WebGPU Performance Test", 1920, 1080);
        window->device = device;
        
#if !VSG_WEBGPU_USE_MOCK
        if (!window->create())
        {
            std::cerr << "Failed to create window" << std::endl;
            return 1;
        }
#endif
        
        // 创建大量几何体的场景图（压力测试）
        auto root = vsg::Group::create();
        
        const int gridSize = 20; // 20x20 = 400个对象
        const int totalObjects = gridSize * gridSize;
        
        std::cout << "Creating " << totalObjects << " objects..." << std::endl;
        
        for (int x = 0; x < gridSize; ++x)
        {
            for (int y = 0; y < gridSize; ++y)
            {
                // 创建变换节点
                auto transform = vsg::MatrixTransform::create();
                
                // 设置位置
                float posX = (x - gridSize/2) * 2.0f;
                float posY = (y - gridSize/2) * 2.0f;
                float posZ = 0.0f;
                
                // 创建几何体
                auto vertices = vsg::vec3Array::create({
                    {0.0f, 0.5f, 0.0f},
                    {-0.5f, -0.5f, 0.0f},
                    {0.5f, -0.5f, 0.0f}
                });
                
                // 随机颜色
                float r = (x % 3) / 2.0f;
                float g = (y % 3) / 2.0f;
                float b = ((x + y) % 3) / 2.0f;
                
                auto colors = vsg::vec3Array::create({
                    {r, g, b},
                    {r, g, b},
                    {r, g, b}
                });
                
                auto indices = vsg::ushortArray::create({0, 1, 2});
                
                auto geometry = vsg::Geometry::create();
                geometry->arrays = {vertices, colors};
                geometry->indices = indices;
                geometry->commands = {vsg::DrawIndexed::create(3, 1, 0, 0, 0)};
                
                transform->addChild(geometry);
                root->addChild(transform);
            }
        }
        
        std::cout << "Scene graph created with " << totalObjects << " objects" << std::endl;
        
        // 创建渲染图
        auto renderGraph = RenderGraph::create(device.get(), window.get());
        renderGraph->addRenderPass("main", root);
        
        // 创建记录遍历器
        auto recordTraversal = RecordTraversal::create();
        
        std::cout << "Starting performance test..." << std::endl;
        
        // 性能测试
        auto startTime = std::chrono::high_resolution_clock::now();
        int frameCount = 0;
        const int testFrames = 300; // 5秒 @ 60fps
        
        double totalFrameTime = 0.0;
        double minFrameTime = std::numeric_limits<double>::max();
        double maxFrameTime = 0.0;
        
#if VSG_WEBGPU_USE_MOCK
        // Mock模式：模拟渲染
        while (frameCount < testFrames)
        {
            auto frameStart = std::chrono::high_resolution_clock::now();
            
            renderGraph->render(recordTraversal);
            
            auto frameEnd = std::chrono::high_resolution_clock::now();
            auto frameDuration = std::chrono::duration_cast<std::chrono::microseconds>(frameEnd - frameStart);
            double frameTime = frameDuration.count() / 1000.0; // ms
            
            totalFrameTime += frameTime;
            minFrameTime = std::min(minFrameTime, frameTime);
            maxFrameTime = std::max(maxFrameTime, frameTime);
            
            frameCount++;
            
            if (frameCount % 60 == 0)
            {
                auto stats = recordTraversal->getStatistics();
                std::cout << "Frame " << frameCount 
                         << " - Mock performance test"
                         << " - Objects: " << totalObjects << std::endl;
            }
            
            std::this_thread::sleep_for(std::chrono::milliseconds(16));
        }
#else
        // 实际渲染循环
        while (window->valid() && frameCount < testFrames)
        {
            auto frameStart = std::chrono::high_resolution_clock::now();
            
            if (!window->pollEvents()) break;
            
            renderGraph->render(recordTraversal);
            window->present();
            
            auto frameEnd = std::chrono::high_resolution_clock::now();
            auto frameDuration = std::chrono::duration_cast<std::chrono::microseconds>(frameEnd - frameStart);
            double frameTime = frameDuration.count() / 1000.0; // ms
            
            totalFrameTime += frameTime;
            minFrameTime = std::min(minFrameTime, frameTime);
            maxFrameTime = std::max(maxFrameTime, frameTime);
            
            frameCount++;
            
            if (frameCount % 60 == 0)
            {
                auto stats = recordTraversal->getStatistics();
                double avgFps = frameCount * 1000.0 / totalFrameTime;
                std::cout << "Frame " << frameCount 
                         << " - FPS: " << avgFps
                         << " - Draw calls: " << stats.numDrawCalls 
                         << " - Triangles: " << stats.numTriangles << std::endl;
            }
        }
#endif
        
        auto endTime = std::chrono::high_resolution_clock::now();
        auto totalDuration = std::chrono::duration_cast<std::chrono::milliseconds>(endTime - startTime);
        
        // 计算性能统计
        double avgFrameTime = totalFrameTime / frameCount;
        double avgFps = frameCount * 1000.0 / totalFrameTime;
        
        std::cout << "\n=== Performance Test Results ===" << std::endl;
        std::cout << "Scene complexity:" << std::endl;
        std::cout << "  Objects: " << totalObjects << std::endl;
        std::cout << "  Triangles per frame: " << (totalObjects * 1) << std::endl;
        std::cout << "" << std::endl;
        std::cout << "Performance metrics:" << std::endl;
        std::cout << "  Frames rendered: " << frameCount << std::endl;
        std::cout << "  Total time: " << totalDuration.count() << "ms" << std::endl;
        std::cout << "  Average FPS: " << avgFps << std::endl;
        std::cout << "  Average frame time: " << avgFrameTime << "ms" << std::endl;
        std::cout << "  Min frame time: " << minFrameTime << "ms" << std::endl;
        std::cout << "  Max frame time: " << maxFrameTime << "ms" << std::endl;
        
        // 获取最终统计信息
        auto finalStats = recordTraversal->getStatistics();
        std::cout << "" << std::endl;
        std::cout << "Rendering statistics:" << std::endl;
        std::cout << "  Total draw calls: " << finalStats.numDrawCalls << std::endl;
        std::cout << "  Total triangles: " << finalStats.numTriangles << std::endl;
        std::cout << "  Total state changes: " << finalStats.numStateChanges << std::endl;
        
        // 性能评估
        std::cout << "" << std::endl;
        std::cout << "Performance assessment:" << std::endl;
        if (avgFps >= 60.0)
        {
            std::cout << "  EXCELLENT: Maintaining 60+ FPS" << std::endl;
        }
        else if (avgFps >= 30.0)
        {
            std::cout << "  GOOD: Maintaining 30+ FPS" << std::endl;
        }
        else if (avgFps >= 15.0)
        {
            std::cout << "  FAIR: Maintaining 15+ FPS" << std::endl;
        }
        else
        {
            std::cout << "  POOR: Below 15 FPS" << std::endl;
        }
        
        // 清理
        recordTraversal.reset();
        renderGraph.reset();
        root.reset();
        
        if (window)
        {
            window->destroy();
            window.reset();
        }
        
        device.reset();
        shutdownWebGPU();
        
        std::cout << "Performance test completed!" << std::endl;
        return 0;
    }
    catch (const std::exception& e)
    {
        std::cerr << "Error: " << e.what() << std::endl;
        return 1;
    }
}
