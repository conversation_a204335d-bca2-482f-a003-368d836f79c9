#!/bin/bash

# VSG渲染引擎抽象层自动化构建和测试脚本
# 完整的构建、编译、测试流程

set -e  # 遇到错误时退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

echo -e "${BLUE}========================================"
echo "VSG渲染引擎抽象层自动化构建测试"
echo -e "========================================${NC}"
echo

# 默认参数
BUILD_TYPE="Release"
BUILD_DIR="build"
INSTALL_DIR="install"
CLEAN_BUILD=0
RUN_TESTS=1
RUN_EXAMPLES=1
VERBOSE=0
JOBS=$(nproc 2>/dev/null || sysctl -n hw.ncpu 2>/dev/null || echo 4)

# 解析命令行参数
show_help() {
    echo "用法: $0 [选项]"
    echo
    echo "选项:"
    echo "  --debug       构建Debug版本 (默认: Release)"
    echo "  --clean       清理后重新构建"
    echo "  --no-tests    跳过测试运行"
    echo "  --no-examples 跳过示例运行"
    echo "  --verbose     详细输出"
    echo "  --jobs N      并行构建任务数 (默认: $JOBS)"
    echo "  --help        显示此帮助信息"
    echo
    echo "示例:"
    echo "  $0                完整构建和测试"
    echo "  $0 --debug       Debug版本构建和测试"
    echo "  $0 --clean       清理后完整构建和测试"
    echo "  $0 --jobs 8      使用8个并行任务构建"
    echo
}

while [[ $# -gt 0 ]]; do
    case $1 in
        --debug)
            BUILD_TYPE="Debug"
            shift
            ;;
        --clean)
            CLEAN_BUILD=1
            shift
            ;;
        --no-tests)
            RUN_TESTS=0
            shift
            ;;
        --no-examples)
            RUN_EXAMPLES=0
            shift
            ;;
        --verbose)
            VERBOSE=1
            shift
            ;;
        --jobs)
            JOBS="$2"
            shift 2
            ;;
        --help)
            show_help
            exit 0
            ;;
        *)
            echo -e "${RED}未知选项: $1${NC}"
            show_help
            exit 1
            ;;
    esac
done

# 记录开始时间
START_TIME=$(date +%s)

# 检查构建环境
check_environment() {
    echo -e "${BLUE}检查构建环境...${NC}"
    
    # 检查CMake
    if ! command -v cmake &> /dev/null; then
        echo -e "${RED}✗ 未找到CMake，请安装CMake 3.20或更高版本${NC}"
        exit 1
    fi
    
    CMAKE_VERSION=$(cmake --version | head -n1 | cut -d' ' -f3)
    echo -e "${GREEN}✓ CMake版本: $CMAKE_VERSION${NC}"
    
    # 检查编译器
    if command -v g++ &> /dev/null; then
        GCC_VERSION=$(g++ --version | head -n1)
        echo -e "${GREEN}✓ 编译器: $GCC_VERSION${NC}"
    elif command -v clang++ &> /dev/null; then
        CLANG_VERSION=$(clang++ --version | head -n1)
        echo -e "${GREEN}✓ 编译器: $CLANG_VERSION${NC}"
    else
        echo -e "${RED}✗ 未找到C++编译器 (g++或clang++)${NC}"
        exit 1
    fi
    
    # 检查VSG库
    VSG_FOUND=0
    if pkg-config --exists vsg 2>/dev/null; then
        VSG_VERSION=$(pkg-config --modversion vsg)
        echo -e "${GREEN}✓ 找到VSG库版本: $VSG_VERSION${NC}"
        VSG_FOUND=1
    elif find /usr/local/include /usr/include /opt -name "vsg" -type d 2>/dev/null | head -1 | grep -q vsg; then
        echo -e "${GREEN}✓ 找到VSG库 (系统安装)${NC}"
        VSG_FOUND=1
    else
        echo -e "${YELLOW}⚠ 未找到VSG库，将禁用VSG后端${NC}"
    fi
    
    echo -e "${GREEN}✓ 构建环境检查完成${NC}"
    echo
}

# 主构建函数
main() {
    echo -e "${BLUE}构建配置:${NC}"
    echo "  构建类型: $BUILD_TYPE"
    echo "  构建目录: $BUILD_DIR"
    echo "  安装目录: $INSTALL_DIR"
    echo "  并行任务: $JOBS"
    echo "  VSG支持: $VSG_FOUND"
    echo "  运行测试: $RUN_TESTS"
    echo "  运行示例: $RUN_EXAMPLES"
    echo
    
    # 检查环境
    check_environment
    
    # 清理构建目录（如果需要）
    if [[ $CLEAN_BUILD -eq 1 ]]; then
        echo -e "${YELLOW}清理构建目录...${NC}"
        rm -rf "$BUILD_DIR" "$INSTALL_DIR"
        echo -e "${GREEN}✓ 清理完成${NC}"
        echo
    fi
    
    # 创建构建目录
    mkdir -p "$BUILD_DIR"
    cd "$BUILD_DIR"
    
    echo -e "${BLUE}步骤 1/6: 配置CMake...${NC}"
    
    CMAKE_ARGS=(
        -DCMAKE_BUILD_TYPE="$BUILD_TYPE"
        -DCMAKE_INSTALL_PREFIX="../$INSTALL_DIR"
        -DVSG_ABSTRACTION_BUILD_MOCK_BACKEND=ON
        -DVSG_ABSTRACTION_BUILD_TESTS=ON
        -DVSG_ABSTRACTION_BUILD_EXAMPLES=ON
        -DVSG_ABSTRACTION_BUILD_SHARED_LIBS=ON
    )
    
    if [[ $VSG_FOUND -eq 1 ]]; then
        CMAKE_ARGS+=(-DVSG_ABSTRACTION_BUILD_VSG_BACKEND=ON)
    else
        CMAKE_ARGS+=(-DVSG_ABSTRACTION_BUILD_VSG_BACKEND=OFF)
    fi
    
    if [[ $VERBOSE -eq 1 ]]; then
        echo "执行: cmake .. ${CMAKE_ARGS[*]}"
    fi
    
    cmake .. "${CMAKE_ARGS[@]}"
    echo -e "${GREEN}✓ CMake配置成功${NC}"
    echo
    
    echo -e "${BLUE}步骤 2/6: 编译项目...${NC}"
    cmake --build . --config "$BUILD_TYPE" --parallel "$JOBS"
    echo -e "${GREEN}✓ 编译成功${NC}"
    echo
    
    echo -e "${BLUE}步骤 3/6: 安装库文件...${NC}"
    cmake --install . --config "$BUILD_TYPE"
    echo -e "${GREEN}✓ 安装成功${NC}"
    echo
    
    # 运行测试
    if [[ $RUN_TESTS -eq 1 ]]; then
        echo -e "${BLUE}步骤 4/6: 运行测试...${NC}"
        
        # 基础测试
        if [[ -x "tests/test_basic" ]]; then
            echo "运行基础功能测试..."
            if ./tests/test_basic; then
                echo -e "${GREEN}✓ 基础测试通过${NC}"
            else
                echo -e "${RED}✗ 基础测试失败${NC}"
                TEST_FAILED=1
            fi
        fi
        
        # VSG集成测试
        if [[ -x "tests/test_vsg_integration" ]]; then
            echo "运行VSG集成测试..."
            if ./tests/test_vsg_integration; then
                echo -e "${GREEN}✓ VSG集成测试通过${NC}"
            else
                echo -e "${YELLOW}⚠ VSG集成测试失败（可能是VSG不可用）${NC}"
            fi
        fi
        
        # 使用CTest运行所有测试
        echo "运行完整测试套件..."
        if ctest --output-on-failure --build-config "$BUILD_TYPE"; then
            echo -e "${GREEN}✓ 所有测试通过${NC}"
        else
            echo -e "${YELLOW}⚠ 部分测试失败${NC}"
        fi
        echo
    else
        echo -e "${YELLOW}步骤 4/6: 跳过测试${NC}"
        echo
    fi
    
    # 运行示例
    if [[ $RUN_EXAMPLES -eq 1 ]]; then
        echo -e "${BLUE}步骤 5/6: 运行示例程序...${NC}"
        
        # 基础示例
        if [[ -x "examples/basic_example" ]]; then
            echo "运行基础示例..."
            if ./examples/basic_example; then
                echo -e "${GREEN}✓ 基础示例运行成功${NC}"
            else
                echo -e "${RED}✗ 基础示例运行失败${NC}"
            fi
        fi
        
        # VSG渲染示例
        if [[ -x "examples/vsg_rendering_example" ]]; then
            echo "运行VSG渲染示例..."
            if ./examples/vsg_rendering_example; then
                echo -e "${GREEN}✓ VSG渲染示例运行成功${NC}"
            else
                echo -e "${YELLOW}⚠ VSG渲染示例运行失败（可能是VSG不可用）${NC}"
            fi
        fi
        
        echo
    else
        echo -e "${YELLOW}步骤 5/6: 跳过示例运行${NC}"
        echo
    fi
    
    echo -e "${BLUE}步骤 6/6: 生成构建报告...${NC}"
    
    # 返回根目录
    cd ..
    
    # 计算构建时间
    END_TIME=$(date +%s)
    BUILD_TIME=$((END_TIME - START_TIME))
    
    echo
    echo -e "${BLUE}========================================"
    echo -e "${GREEN}构建和测试完成!${NC}"
    echo -e "${BLUE}========================================${NC}"
    echo
    echo -e "${BLUE}构建输出:${NC}"
    echo "  库文件: $BUILD_DIR/lib/"
    echo "  可执行文件: $BUILD_DIR/bin/"
    echo "  测试程序: $BUILD_DIR/tests/"
    echo "  示例程序: $BUILD_DIR/examples/"
    echo
    echo -e "${BLUE}安装输出:${NC}"
    echo "  头文件: $INSTALL_DIR/include/"
    echo "  库文件: $INSTALL_DIR/lib/"
    echo "  可执行文件: $INSTALL_DIR/bin/"
    echo
    
    # 显示可用的程序
    echo -e "${BLUE}可用的程序:${NC}"
    for example in "$BUILD_DIR/examples"/*; do
        if [[ -x "$example" && -f "$example" ]]; then
            echo "  $example"
        fi
    done
    
    echo
    echo -e "${BLUE}快速运行命令:${NC}"
    echo "  cd $BUILD_DIR/examples"
    echo "  ./basic_example"
    echo
    
    # 设置库路径提示
    if [[ "$OSTYPE" == "linux-gnu"* ]]; then
        echo -e "${YELLOW}注意: 如果运行时找不到共享库，请设置LD_LIBRARY_PATH:${NC}"
        echo "  export LD_LIBRARY_PATH=\$PWD/$BUILD_DIR/lib:\$LD_LIBRARY_PATH"
    elif [[ "$OSTYPE" == "darwin"* ]]; then
        echo -e "${YELLOW}注意: 如果运行时找不到共享库，请设置DYLD_LIBRARY_PATH:${NC}"
        echo "  export DYLD_LIBRARY_PATH=\$PWD/$BUILD_DIR/lib:\$DYLD_LIBRARY_PATH"
    fi
    
    echo
    echo -e "${GREEN}🎉 VSG渲染引擎抽象层构建测试成功完成！${NC}"
    echo -e "${BLUE}总构建时间: ${BUILD_TIME}秒${NC}"
    echo
}

# 错误处理
trap 'echo -e "${RED}构建或测试失败!${NC}"; exit 1' ERR

# 运行主函数
main "$@"
