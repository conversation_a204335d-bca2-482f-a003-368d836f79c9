#pragma once

/* <editor-fold desc="MIT License">

Copyright(c) 2018 <PERSON>

Permission is hereby granted, free of charge, to any person obtaining a copy of this software and associated documentation files (the "Software"), to deal in the Software without restriction, including without limitation the rights to use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of the Software, and to permit persons to whom the Software is furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.

</editor-fold> */

#include <vsg/state/BufferView.h>
#include <vsg/state/Descriptor.h>

namespace vsg
{

    /// DescriptorTexelBufferView is a Descriptor class that encapsulates texelBufferViews used to set VkWriteDescriptorSet::pTexelBufferViews
    class VSG_DECLSPEC DescriptorTexelBufferView : public Inherit<Descriptor, DescriptorTexelBufferView>
    {
    public:
        DescriptorTexelBufferView();
        DescriptorTexelBufferView(const DescriptorTexelBufferView& rhs, const CopyOp& copyop = {});
        DescriptorTexelBufferView(uint32_t dstBinding, uint32_t dstArrayElement, VkDescriptorType descriptorType, const BufferViewList& in_texelBufferViews);

        /// VkWriteDescriptorSet.pTexelBufferViews settings
        BufferViewList texelBufferViews;

        void compile(Context& context) override;
        void assignTo(Context& context, VkWriteDescriptorSet& wds) const override;
        uint32_t getNumDescriptors() const override { return static_cast<uint32_t>(texelBufferViews.size()); }

    public:
        ref_ptr<Object> clone(const CopyOp& copyop = {}) const override { return DescriptorTexelBufferView::create(*this, copyop); }
        int compare(const Object& rhs_object) const override;

        void read(Input& input) override;
        void write(Output& output) const override;

    protected:
    };
    VSG_type_name(vsg::DescriptorTexelBufferView);

} // namespace vsg
