/* <editor-fold desc="MIT License">

Copyright(c) 2024 Rocky Render Engine Refactor

Permission is hereby granted, free of charge, to any person obtaining a copy of this software and associated documentation files (the "Software"), to deal in the Software without restriction, including without limitation the rights to use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of the Software, and to permit persons to whom the Software is furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.

</editor-fold> */

#include "VulkanRenderEngineWrapper.h"
#include <rocky/render/Logger.h>

namespace rocky
{
    namespace render
    {

        // ========== VulkanRenderEngineWrapper Implementation ==========

        VulkanRenderEngineWrapper::VulkanRenderEngineWrapper(std::unique_ptr<vsg_abstraction::IRenderEngine> vsgEngine) :
            vsgEngine_(std::move(vsgEngine))
        {
            ROCKY_LOG_DEBUG("VulkanRenderEngineWrapper created");
        }

        VulkanRenderEngineWrapper::~VulkanRenderEngineWrapper()
        {
            shutdown();
            ROCKY_LOG_DEBUG("VulkanRenderEngineWrapper destroyed");
        }

        bool VulkanRenderEngineWrapper::initialize()
        {
            if (!vsgEngine_)
            {
                ROCKY_LOG_ERROR("Cannot initialize: VSG engine is null");
                return false;
            }

            return vsgEngine_->initialize();
        }

        void VulkanRenderEngineWrapper::shutdown()
        {
            if (vsgEngine_)
            {
                vsgEngine_->shutdown();
            }

            // 清理适配器缓存
            std::lock_guard<std::mutex> lock(cacheMapMutex_);
            nodeAdapterCache_.clear();
        }

        bool VulkanRenderEngineWrapper::isInitialized() const
        {
            return vsgEngine_ && vsgEngine_->isInitialized();
        }

        RenderBackend VulkanRenderEngineWrapper::getBackendType() const
        {
            return RenderBackend::Vulkan;
        }

        EngineInfo VulkanRenderEngineWrapper::getEngineInfo() const
        {
            if (!vsgEngine_)
            {
                return EngineInfo{};
            }

            return convertEngineInfo(vsgEngine_->getEngineInfo());
        }

        IDevice* VulkanRenderEngineWrapper::getDevice()
        {
            if (!vsgEngine_)
            {
                return nullptr;
            }

            // 这里需要创建设备包装器，暂时返回nullptr
            // TODO: 实现设备包装器
            return nullptr;
        }

        std::vector<IPhysicalDevice*> VulkanRenderEngineWrapper::getPhysicalDevices()
        {
            if (!vsgEngine_)
            {
                return {};
            }

            // 这里需要创建物理设备包装器，暂时返回空向量
            // TODO: 实现物理设备包装器
            return {};
        }

        ref_ptr<IGroup> VulkanRenderEngineWrapper::createGroup()
        {
            if (!vsgEngine_)
            {
                ROCKY_LOG_ERROR("Cannot create group: VSG engine is null");
                return nullptr;
            }

            auto vsgGroup = vsgEngine_->createGroup();
            if (!vsgGroup)
            {
                return nullptr;
            }

            return std::make_shared<VSGGroupWrapper>(vsgGroup);
        }

        ref_ptr<ITransform> VulkanRenderEngineWrapper::createTransform()
        {
            if (!vsgEngine_)
            {
                ROCKY_LOG_ERROR("Cannot create transform: VSG engine is null");
                return nullptr;
            }

            auto vsgTransform = vsgEngine_->createTransform();
            if (!vsgTransform)
            {
                return nullptr;
            }

            return std::make_shared<VSGTransformWrapper>(vsgTransform);
        }

        ref_ptr<IGeometry> VulkanRenderEngineWrapper::createGeometry()
        {
            if (!vsgEngine_)
            {
                ROCKY_LOG_ERROR("Cannot create geometry: VSG engine is null");
                return nullptr;
            }

            auto vsgGeometry = vsgEngine_->createGeometry();
            if (!vsgGeometry)
            {
                return nullptr;
            }

            // TODO: 实现几何体包装器
            return nullptr;
        }

        ref_ptr<IStateGroup> VulkanRenderEngineWrapper::createStateGroup()
        {
            if (!vsgEngine_)
            {
                ROCKY_LOG_ERROR("Cannot create state group: VSG engine is null");
                return nullptr;
            }

            auto vsgStateGroup = vsgEngine_->createStateGroup();
            if (!vsgStateGroup)
            {
                return nullptr;
            }

            // TODO: 实现状态组包装器
            return nullptr;
        }

        ref_ptr<IWindow> VulkanRenderEngineWrapper::createWindow(const WindowConfig& config)
        {
            if (!vsgEngine_)
            {
                ROCKY_LOG_ERROR("Cannot create window: VSG engine is null");
                return nullptr;
            }

            auto vsgTraits = convertWindowConfig(config);
            auto vsgWindow = vsgEngine_->createWindow(vsgTraits);
            if (!vsgWindow)
            {
                return nullptr;
            }

            // TODO: 实现窗口包装器
            return nullptr;
        }

        ref_ptr<IRenderGraph> VulkanRenderEngineWrapper::createRenderGraph(ref_ptr<IWindow> window)
        {
            if (!vsgEngine_)
            {
                ROCKY_LOG_ERROR("Cannot create render graph: VSG engine is null");
                return nullptr;
            }

            // TODO: 实现渲染图包装器
            return nullptr;
        }

        ref_ptr<IRecordTraversal> VulkanRenderEngineWrapper::createRecordTraversal()
        {
            if (!vsgEngine_)
            {
                ROCKY_LOG_ERROR("Cannot create record traversal: VSG engine is null");
                return nullptr;
            }

            auto vsgRecordTraversal = vsgEngine_->createRecordTraversal();
            if (!vsgRecordTraversal)
            {
                return nullptr;
            }

            // TODO: 实现记录遍历包装器
            return nullptr;
        }

        ref_ptr<IBuffer> VulkanRenderEngineWrapper::createBuffer(const BufferInfo& info)
        {
            if (!vsgEngine_)
            {
                ROCKY_LOG_ERROR("Cannot create buffer: VSG engine is null");
                return nullptr;
            }

            auto vsgBufferInfo = convertBufferInfo(info);
            auto vsgBuffer = vsgEngine_->createBuffer(vsgBufferInfo);
            if (!vsgBuffer)
            {
                return nullptr;
            }

            // TODO: 实现缓冲区包装器
            return nullptr;
        }

        ref_ptr<IImage> VulkanRenderEngineWrapper::createImage(const ImageInfo& info)
        {
            if (!vsgEngine_)
            {
                ROCKY_LOG_ERROR("Cannot create image: VSG engine is null");
                return nullptr;
            }

            auto vsgImageInfo = convertImageInfo(info);
            auto vsgImage = vsgEngine_->createImage(vsgImageInfo);
            if (!vsgImage)
            {
                return nullptr;
            }

            // TODO: 实现图像包装器
            return nullptr;
        }

        ref_ptr<IPipeline> VulkanRenderEngineWrapper::createGraphicsPipeline(const GraphicsPipelineInfo& info)
        {
            if (!vsgEngine_)
            {
                ROCKY_LOG_ERROR("Cannot create graphics pipeline: VSG engine is null");
                return nullptr;
            }

            auto vsgPipelineInfo = convertGraphicsPipelineInfo(info);
            auto vsgPipeline = vsgEngine_->createGraphicsPipeline(vsgPipelineInfo);
            if (!vsgPipeline)
            {
                return nullptr;
            }

            // TODO: 实现管线包装器
            return nullptr;
        }

        ref_ptr<IPipeline> VulkanRenderEngineWrapper::createComputePipeline(const ComputePipelineInfo& info)
        {
            if (!vsgEngine_)
            {
                ROCKY_LOG_ERROR("Cannot create compute pipeline: VSG engine is null");
                return nullptr;
            }

            auto vsgPipelineInfo = convertComputePipelineInfo(info);
            auto vsgPipeline = vsgEngine_->createComputePipeline(vsgPipelineInfo);
            if (!vsgPipeline)
            {
                return nullptr;
            }

            // TODO: 实现管线包装器
            return nullptr;
        }

        void VulkanRenderEngineWrapper::waitIdle()
        {
            if (vsgEngine_)
            {
                vsgEngine_->waitIdle();
            }
        }

        RenderStatistics VulkanRenderEngineWrapper::getRenderStatistics() const
        {
            if (!vsgEngine_)
            {
                return RenderStatistics{};
            }

            return convertRenderStatistics(vsgEngine_->getRenderStatistics());
        }

        void VulkanRenderEngineWrapper::resetRenderStatistics()
        {
            if (vsgEngine_)
            {
                vsgEngine_->resetRenderStatistics();
            }
        }

        void VulkanRenderEngineWrapper::setDebugCallback(DebugCallback callback)
        {
            if (vsgEngine_)
            {
                // 转换回调函数
                vsg_abstraction::DebugCallback vsgCallback = [callback](const std::string& message, int severity) {
                    if (callback)
                    {
                        callback(message, severity);
                    }
                };
                vsgEngine_->setDebugCallback(vsgCallback);
            }
        }

        void VulkanRenderEngineWrapper::beginProfileMarker(const std::string& name)
        {
            if (vsgEngine_)
            {
                vsgEngine_->beginDebugMarker(name);
            }
        }

        void VulkanRenderEngineWrapper::endProfileMarker()
        {
            if (vsgEngine_)
            {
                vsgEngine_->endDebugMarker();
            }
        }

        void* VulkanRenderEngineWrapper::getNativeHandle() const
        {
            if (vsgEngine_)
            {
                return vsgEngine_->getNativeHandle();
            }
            return nullptr;
        }

        void VulkanRenderEngineWrapper::executeCustomCommand(std::function<void(void*)> command)
        {
            if (vsgEngine_ && command)
            {
                vsgEngine_->executeCustomCommand(command);
            }
        }

        bool VulkanRenderEngineWrapper::supportsFeature(const std::string& feature) const
        {
            if (vsgEngine_)
            {
                return vsgEngine_->supportsFeature(feature);
            }
            return false;
        }

        vsg_abstraction::IRenderEngine* VulkanRenderEngineWrapper::getVSGAbstractionEngine() const
        {
            return vsgEngine_.get();
        }

        vsg_abstraction::VSGRenderEngine* VulkanRenderEngineWrapper::getVSGRenderEngine() const
        {
            return dynamic_cast<vsg_abstraction::VSGRenderEngine*>(vsgEngine_.get());
        }

        // ========== 类型转换方法 ==========

        vsg_abstraction::WindowTraits VulkanRenderEngineWrapper::convertWindowConfig(const WindowConfig& config)
        {
            vsg_abstraction::WindowTraits traits;
            traits.windowTitle = config.windowTitle;
            traits.x = config.x;
            traits.y = config.y;
            traits.width = config.width;
            traits.height = config.height;
            traits.decoration = config.decoration;
            traits.fullscreen = config.fullscreen;
            traits.resizable = config.resizable;
            traits.hdpi = config.hdpi;
            traits.samples = config.samples;
            traits.vsync = config.vsync;
            traits.debugLayer = config.debugLayer;
            traits.apiDumpLayer = config.apiDumpLayer;
            traits.instanceExtensionNames = config.instanceExtensionNames;
            traits.deviceExtensionNames = config.deviceExtensionNames;
            return traits;
        }

        vsg_abstraction::BufferInfo VulkanRenderEngineWrapper::convertBufferInfo(const BufferInfo& info)
        {
            vsg_abstraction::BufferInfo vsgInfo;
            vsgInfo.size = info.size;
            vsgInfo.usage = info.usage;
            vsgInfo.data = info.data;

            // 转换缓冲区类型
            switch (info.type)
            {
            case BufferType::Vertex:
                vsgInfo.type = vsg_abstraction::BufferType::Vertex;
                break;
            case BufferType::Index:
                vsgInfo.type = vsg_abstraction::BufferType::Index;
                break;
            case BufferType::Uniform:
                vsgInfo.type = vsg_abstraction::BufferType::Uniform;
                break;
            case BufferType::Storage:
                vsgInfo.type = vsg_abstraction::BufferType::Storage;
                break;
            case BufferType::Staging:
                vsgInfo.type = vsg_abstraction::BufferType::Staging;
                break;
            }

            return vsgInfo;
        }

        vsg_abstraction::ImageInfo VulkanRenderEngineWrapper::convertImageInfo(const ImageInfo& info)
        {
            vsg_abstraction::ImageInfo vsgInfo;
            vsgInfo.width = info.width;
            vsgInfo.height = info.height;
            vsgInfo.depth = info.depth;
            vsgInfo.mipLevels = info.mipLevels;
            vsgInfo.arrayLayers = info.arrayLayers;
            vsgInfo.samples = info.samples;
            vsgInfo.usage = info.usage;

            // 转换图像格式
            switch (info.format)
            {
            case ImageFormat::RGBA8:
                vsgInfo.format = vsg_abstraction::ImageFormat::RGBA8;
                break;
            case ImageFormat::RGBA16F:
                vsgInfo.format = vsg_abstraction::ImageFormat::RGBA16F;
                break;
            case ImageFormat::RGBA32F:
                vsgInfo.format = vsg_abstraction::ImageFormat::RGBA32F;
                break;
            case ImageFormat::RGB8:
                vsgInfo.format = vsg_abstraction::ImageFormat::RGB8;
                break;
            case ImageFormat::RG8:
                vsgInfo.format = vsg_abstraction::ImageFormat::RG8;
                break;
            case ImageFormat::R8:
                vsgInfo.format = vsg_abstraction::ImageFormat::R8;
                break;
            case ImageFormat::Depth24Stencil8:
                vsgInfo.format = vsg_abstraction::ImageFormat::Depth24Stencil8;
                break;
            }

            return vsgInfo;
        }

        vsg_abstraction::GraphicsPipelineInfo VulkanRenderEngineWrapper::convertGraphicsPipelineInfo(const GraphicsPipelineInfo& info)
        {
            vsg_abstraction::GraphicsPipelineInfo vsgInfo;

            // 转换着色器信息
            for (const auto& shader : info.shaders)
            {
                vsg_abstraction::ShaderInfo vsgShader;
                vsgShader.source = shader.source;
                vsgShader.spirv = shader.spirv;
                vsgShader.entryPoint = shader.entryPoint;

                // 转换着色器阶段
                switch (shader.stage)
                {
                case ShaderStage::Vertex:
                    vsgShader.stage = vsg_abstraction::ShaderStage::Vertex;
                    break;
                case ShaderStage::Fragment:
                    vsgShader.stage = vsg_abstraction::ShaderStage::Fragment;
                    break;
                case ShaderStage::Geometry:
                    vsgShader.stage = vsg_abstraction::ShaderStage::Geometry;
                    break;
                case ShaderStage::Compute:
                    vsgShader.stage = vsg_abstraction::ShaderStage::Compute;
                    break;
                }

                vsgInfo.shaders.push_back(vsgShader);
            }

            // 转换图元拓扑
            switch (info.topology)
            {
            case PrimitiveTopology::PointList:
                vsgInfo.topology = vsg_abstraction::PrimitiveTopology::PointList;
                break;
            case PrimitiveTopology::LineList:
                vsgInfo.topology = vsg_abstraction::PrimitiveTopology::LineList;
                break;
            case PrimitiveTopology::LineStrip:
                vsgInfo.topology = vsg_abstraction::PrimitiveTopology::LineStrip;
                break;
            case PrimitiveTopology::TriangleList:
                vsgInfo.topology = vsg_abstraction::PrimitiveTopology::TriangleList;
                break;
            case PrimitiveTopology::TriangleStrip:
                vsgInfo.topology = vsg_abstraction::PrimitiveTopology::TriangleStrip;
                break;
            case PrimitiveTopology::TriangleFan:
                vsgInfo.topology = vsg_abstraction::PrimitiveTopology::TriangleFan;
                break;
            }

            vsgInfo.depthTest = info.depthTest;
            vsgInfo.depthWrite = info.depthWrite;
            vsgInfo.blending = info.blending;
            vsgInfo.wireframe = info.wireframe;
            vsgInfo.cullMode = info.cullMode;

            return vsgInfo;
        }

        vsg_abstraction::ComputePipelineInfo VulkanRenderEngineWrapper::convertComputePipelineInfo(const ComputePipelineInfo& info)
        {
            vsg_abstraction::ComputePipelineInfo vsgInfo;

            // 转换计算着色器
            vsgInfo.computeShader.source = info.computeShader.source;
            vsgInfo.computeShader.spirv = info.computeShader.spirv;
            vsgInfo.computeShader.entryPoint = info.computeShader.entryPoint;
            vsgInfo.computeShader.stage = vsg_abstraction::ShaderStage::Compute;

            return vsgInfo;
        }

        EngineInfo VulkanRenderEngineWrapper::convertEngineInfo(const vsg_abstraction::EngineInfo& info)
        {
            EngineInfo rockyInfo;
            rockyInfo.name = info.name;
            rockyInfo.version = info.version;
            rockyInfo.vendor = info.vendor;
            rockyInfo.extensions = info.extensions;

            // 转换后端类型
            switch (info.backend)
            {
            case vsg_abstraction::RenderBackend::Vulkan:
                rockyInfo.backend = RenderBackend::Vulkan;
                break;
            case vsg_abstraction::RenderBackend::WebGPU:
                rockyInfo.backend = RenderBackend::WebGPU;
                break;
            case vsg_abstraction::RenderBackend::OpenGL:
                rockyInfo.backend = RenderBackend::OpenGL;
                break;
            case vsg_abstraction::RenderBackend::Mock:
                rockyInfo.backend = RenderBackend::Mock;
                break;
            }

            return rockyInfo;
        }

        RenderStatistics VulkanRenderEngineWrapper::convertRenderStatistics(const vsg_abstraction::RenderStatistics& stats)
        {
            RenderStatistics rockyStats;
            rockyStats.frameCount = stats.frameCount;
            rockyStats.drawCalls = stats.drawCalls;
            rockyStats.triangles = stats.triangles;
            rockyStats.vertices = stats.vertices;
            rockyStats.frameTime = stats.frameTime;
            rockyStats.cpuTime = stats.cpuTime;
            rockyStats.gpuTime = stats.gpuTime;
            rockyStats.memoryUsed = stats.memoryUsed;
            rockyStats.memoryAllocated = stats.memoryAllocated;
            return rockyStats;
        }
