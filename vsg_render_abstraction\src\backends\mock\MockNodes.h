/* <editor-fold desc="MIT License">

Copyright(c) 2024 VSG Render Engine Abstraction

Permission is hereby granted, free of charge, to any person obtaining a copy of this software and associated documentation files (the "Software"), to deal in the Software without restriction, including without limitation the rights to use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of the Software, and to permit persons to whom the Software is furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND <PERSON>NINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.

</editor-fold> */

#pragma once

#include <algorithm>
#include <vector>
#include <vsg_abstraction/core/IGeometry.h>
#include <vsg_abstraction/core/IGroup.h>
#include <vsg_abstraction/core/INode.h>
#include <vsg_abstraction/core/IStateGroup.h>
#include <vsg_abstraction/core/ITransform.h>

namespace vsg_abstraction
{

    /**
 * @brief Mock节点基类
 */
    class MockNode : public INode
    {
    public:
        MockNode() :
            nodeId_(generateNodeId()) {}
        virtual ~MockNode() = default;

        // INode接口实现
        void setName(const std::string& name) override { name_ = name; }
        std::string getName() const override { return name_; }

        NodeType getNodeType() const override { return NodeType::Node; }

        void setUserData(const std::string& key, void* data) override
        {
            userData_[key] = data;
        }

        void* getUserData(const std::string& key) const override
        {
            auto it = userData_.find(key);
            return it != userData_.end() ? it->second : nullptr;
        }

        bool hasUserData(const std::string& key) const override
        {
            return userData_.find(key) != userData_.end();
        }

        void removeUserData(const std::string& key) override
        {
            userData_.erase(key);
        }

        uint32_t getNodeId() const override
        {
            return nodeId_;
        }

        void accept(IVisitor& visitor) override
        {
            (void)visitor; // Mock实现，暂时不处理
        }

        void traverse(IVisitor& visitor) override
        {
            (void)visitor; // Mock实现，暂时不处理
        }

        BoundingBox getLocalBounds() const override
        {
            return localBounds_;
        }

        BoundingBox getWorldBounds() const override
        {
            return worldBounds_;
        }

        void computeBounds() override
        {
            // Mock实现，简单设置默认边界框
            localBounds_ = BoundingBox();
            worldBounds_ = localBounds_;
        }

        void* getNativeHandle() const override
        {
            return nullptr; // Mock实现，没有原生句柄
        }

    protected:
        std::string name_;
        uint32_t nodeId_;
        std::unordered_map<std::string, void*> userData_;
        mutable BoundingBox localBounds_;
        mutable BoundingBox worldBounds_;
    };

    /**
 * @brief Mock组节点实现
 */
    class MockGroup : public MockNode, public IGroup
    {
    public:
        MockGroup() = default;
        virtual ~MockGroup() = default;

        NodeType getNodeType() const override { return NodeType::Group; }

        // IGroup接口实现
        void addChild(ref_ptr<INode> child) override
        {
            if (child)
            {
                children_.push_back(child);
            }
        }

        void removeChild(ref_ptr<INode> child) override
        {
            auto it = std::find(children_.begin(), children_.end(), child);
            if (it != children_.end())
            {
                children_.erase(it);
            }
        }

        void removeChild(size_t index) override
        {
            if (index < children_.size())
            {
                children_.erase(children_.begin() + index);
            }
        }

        void removeAllChildren() override
        {
            children_.clear();
        }

        size_t getNumChildren() const override
        {
            return children_.size();
        }

        ref_ptr<INode> getChild(size_t index) const override
        {
            return index < children_.size() ? children_[index] : nullptr;
        }

        std::vector<ref_ptr<INode>> getChildren() const override
        {
            return children_;
        }

        ref_ptr<INode> findChild(const std::string& name) const override
        {
            for (const auto& child : children_)
            {
                if (child && child->getName() == name)
                {
                    return child;
                }
            }
            return nullptr;
        }

    protected:
        std::vector<ref_ptr<INode>> children_;
    };

    /**
 * @brief Mock变换节点实现
 */
    class MockTransform : public MockGroup, public ITransform
    {
    public:
        MockTransform()
        {
            // 初始化为单位矩阵
            matrix_ = {{{{1.0f, 0.0f, 0.0f, 0.0f}},
                        {{0.0f, 1.0f, 0.0f, 0.0f}},
                        {{0.0f, 0.0f, 1.0f, 0.0f}},
                        {{0.0f, 0.0f, 0.0f, 1.0f}}}};
            position_ = {{0.0f, 0.0f, 0.0f}};
            rotation_ = {{0.0f, 0.0f, 0.0f, 1.0f}}; // 单位四元数
            scale_ = {{1.0f, 1.0f, 1.0f}};
        }

        virtual ~MockTransform() = default;

        NodeType getNodeType() const override { return NodeType::Transform; }

        // ITransform接口实现
        void setMatrix(const mat4& matrix) override
        {
            matrix_ = matrix;
            // 从矩阵分解出位置、旋转、缩放
            decomposeMatrix();
        }

        mat4 getMatrix() const override
        {
            return matrix_;
        }

        void getWorldMatrix(mat4& matrix) const override
        {
            // 简化实现，直接返回本地矩阵
            matrix = matrix_;
        }

        void setPosition(const vec3& position) override
        {
            position_ = position;
            updateMatrix();
        }

        vec3 getPosition() const override
        {
            return position_;
        }

        void setRotation(const vec4& rotation) override
        {
            rotation_ = rotation;
            updateMatrix();
        }

        vec4 getRotation() const override
        {
            return rotation_;
        }

        void setScale(const vec3& scale) override
        {
            scale_ = scale;
            updateMatrix();
        }

        vec3 getScale() const override
        {
            return scale_;
        }

    private:
        void updateMatrix()
        {
            // 简化的矩阵组合：T * R * S
            // 这里只实现位置和缩放，旋转需要四元数转换
            matrix_ = {{{{scale_[0], 0.0f, 0.0f, position_[0]}},
                        {{0.0f, scale_[1], 0.0f, position_[1]}},
                        {{0.0f, 0.0f, scale_[2], position_[2]}},
                        {{0.0f, 0.0f, 0.0f, 1.0f}}}};
        }

        void decomposeMatrix()
        {
            // 简化的矩阵分解
            position_[0] = matrix_[0][3];
            position_[1] = matrix_[1][3];
            position_[2] = matrix_[2][3];

            scale_[0] = matrix_[0][0];
            scale_[1] = matrix_[1][1];
            scale_[2] = matrix_[2][2];

            // 旋转保持不变（简化）
        }

    protected:
        mat4 matrix_;
        vec3 position_;
        vec4 rotation_;
        vec3 scale_;
    };

    /**
 * @brief Mock几何体实现
 */
    class MockGeometry : public MockNode, public IGeometry
    {
    public:
        MockGeometry() :
            topology_(PrimitiveTopology::TriangleList) {}
        virtual ~MockGeometry() = default;

        NodeType getNodeType() const override { return NodeType::Geometry; }

        // IGeometry接口实现
        void setVertices(const std::vector<vec3>& vertices) override
        {
            vertices_ = vertices;
        }

        std::vector<vec3> getVertices() const override
        {
            return vertices_;
        }

        void setIndices(const std::vector<uint32_t>& indices) override
        {
            indices_ = indices;
        }

        std::vector<uint32_t> getIndices() const override
        {
            return indices_;
        }

        void setNormals(const std::vector<vec3>& normals) override
        {
            normals_ = normals;
        }

        std::vector<vec3> getNormals() const override
        {
            return normals_;
        }

        void setTexCoords(const std::vector<vec2>& texCoords) override
        {
            texCoords_ = texCoords;
        }

        std::vector<vec2> getTexCoords() const override
        {
            return texCoords_;
        }

        void setColors(const std::vector<vec4>& colors) override
        {
            colors_ = colors;
        }

        std::vector<vec4> getColors() const override
        {
            return colors_;
        }

        void setPrimitiveTopology(PrimitiveTopology topology) override
        {
            topology_ = topology;
        }

        PrimitiveTopology getPrimitiveTopology() const override
        {
            return topology_;
        }

    protected:
        std::vector<vec3> vertices_;
        std::vector<uint32_t> indices_;
        std::vector<vec3> normals_;
        std::vector<vec2> texCoords_;
        std::vector<vec4> colors_;
        PrimitiveTopology topology_;
    };

    /**
 * @brief Mock状态组实现
 */
    class MockStateGroup : public MockGroup, public IStateGroup
    {
    public:
        MockStateGroup() = default;
        virtual ~MockStateGroup() = default;

        NodeType getNodeType() const override { return NodeType::StateGroup; }

        // IStateGroup接口实现
        void addState(const std::string& name, void* state) override
        {
            states_[name] = state;
        }

        void removeState(const std::string& name) override
        {
            states_.erase(name);
        }

        void* getState(const std::string& name) const override
        {
            auto it = states_.find(name);
            return it != states_.end() ? it->second : nullptr;
        }

        bool hasState(const std::string& name) const override
        {
            return states_.find(name) != states_.end();
        }

    protected:
        std::unordered_map<std::string, void*> states_;
    };

} // namespace vsg_abstraction
