// include/vsg_webgpu/GraphicsPipeline.h
#pragma once
#include <vsg/core/Object.h>
#include <vsg/core/ref_ptr.h>
#include <vsg/state/StateCommand.h>
#include <vsg/state/GraphicsPipeline.h>
#include <webgpu/webgpu_cpp.h>
#include <vector>
#include <string>

namespace vsg_webgpu
{
    // forward declarations
    class Device;
    class State;
    class Context;

    /// WebGPU渲染管线状态基类，对应VSG的GraphicsPipelineState
    class VSG_DECLSPEC GraphicsPipelineState : public vsg::Inherit<vsg::StateCommand, GraphicsPipelineState>
    {
    public:
        GraphicsPipelineState() {}
        GraphicsPipelineState(const GraphicsPipelineState& gps) :
            vsg::Inherit<vsg::StateCommand, GraphicsPipelineState>(), mask(gps.mask) {}

        /// 应用GraphicsPipelineState当(mask & view.mask)非零时
        vsg::Mask mask = vsg::MASK_ALL;

        virtual void apply(Context& context, wgpu::RenderPipelineDescriptor& pipelineDesc) const = 0;
        void record(vsg::CommandBuffer&) const override {}
    };

    using GraphicsPipelineStates = std::vector<vsg::ref_ptr<GraphicsPipelineState>>;

    /// WebGPU着色器阶段
    class VSG_DECLSPEC ShaderStage : public vsg::Inherit<vsg::Object, ShaderStage>
    {
    public:
        ShaderStage(const std::string& wgslCode, const std::string& entryPoint = "main");

        std::string wgslCode;
        std::string entryPoint;
        
        // 编译着色器模块
        wgpu::ShaderModule compile(Device* device) const;

        int compare(const vsg::Object& rhs_object) const override;
    };

    using ShaderStages = std::vector<vsg::ref_ptr<ShaderStage>>;

    /// WebGPU顶点输入状态
    class VSG_DECLSPEC VertexInputState : public vsg::Inherit<GraphicsPipelineState, VertexInputState>
    {
    public:
        VertexInputState();

        std::vector<wgpu::VertexAttribute> vertexAttributes;
        std::vector<wgpu::VertexBufferLayout> vertexBufferLayouts;

        void apply(Context& context, wgpu::RenderPipelineDescriptor& pipelineDesc) const override;

        int compare(const vsg::Object& rhs_object) const override;
    };

    /// WebGPU图元状态
    class VSG_DECLSPEC PrimitiveState : public vsg::Inherit<GraphicsPipelineState, PrimitiveState>
    {
    public:
        PrimitiveState();

        wgpu::PrimitiveTopology topology = wgpu::PrimitiveTopology::TriangleList;
        wgpu::IndexFormat stripIndexFormat = wgpu::IndexFormat::Undefined;
        wgpu::FrontFace frontFace = wgpu::FrontFace::CCW;
        wgpu::CullMode cullMode = wgpu::CullMode::Back;

        void apply(Context& context, wgpu::RenderPipelineDescriptor& pipelineDesc) const override;

        int compare(const vsg::Object& rhs_object) const override;
    };

    /// WebGPU深度模板状态
    class VSG_DECLSPEC DepthStencilState : public vsg::Inherit<GraphicsPipelineState, DepthStencilState>
    {
    public:
        DepthStencilState();

        wgpu::TextureFormat format = wgpu::TextureFormat::Depth32Float;
        bool depthWriteEnabled = true;
        wgpu::CompareFunction depthCompare = wgpu::CompareFunction::Less;

        // 模板测试
        wgpu::StencilFaceState stencilFront = {};
        wgpu::StencilFaceState stencilBack = {};
        uint32_t stencilReadMask = 0xFFFFFFFF;
        uint32_t stencilWriteMask = 0xFFFFFFFF;

        void apply(Context& context, wgpu::RenderPipelineDescriptor& pipelineDesc) const override;

        int compare(const vsg::Object& rhs_object) const override;
    };

    /// WebGPU多重采样状态
    class VSG_DECLSPEC MultisampleState : public vsg::Inherit<GraphicsPipelineState, MultisampleState>
    {
    public:
        MultisampleState();

        uint32_t count = 1;
        uint32_t mask = 0xFFFFFFFF;
        bool alphaToCoverageEnabled = false;

        void apply(Context& context, wgpu::RenderPipelineDescriptor& pipelineDesc) const override;

        int compare(const vsg::Object& rhs_object) const override;
    };

    /// WebGPU颜色混合状态
    class VSG_DECLSPEC ColorBlendState : public vsg::Inherit<GraphicsPipelineState, ColorBlendState>
    {
    public:
        ColorBlendState();

        std::vector<wgpu::ColorTargetState> targets;

        void apply(Context& context, wgpu::RenderPipelineDescriptor& pipelineDesc) const override;

        int compare(const vsg::Object& rhs_object) const override;
    };

    /// WebGPU图形管线
    class VSG_DECLSPEC GraphicsPipeline : public vsg::Inherit<vsg::Object, GraphicsPipeline>
    {
    public:
        GraphicsPipeline();
        GraphicsPipeline(const ShaderStages& shaderStages, 
                        const GraphicsPipelineStates& pipelineStates);

        /// 获取WebGPU管线对象
        wgpu::RenderPipeline getPipeline(uint32_t viewID = 0) const;

        /// 管线配置
        ShaderStages stages;
        GraphicsPipelineStates pipelineStates;

        int compare(const vsg::Object& rhs_object) const override;

        // 编译管线
        void compile(Context& context);

        // 释放资源
        void release(uint32_t viewID = 0);
        void release();

    protected:
        virtual ~GraphicsPipeline();

    private:
        struct Implementation
        {
            wgpu::RenderPipeline pipeline;
            Device* device;
        };

        std::vector<std::unique_ptr<Implementation>> _implementations;
    };

    /// WebGPU绑定图形管线命令
    class VSG_DECLSPEC BindGraphicsPipeline : public vsg::Inherit<vsg::StateCommand, BindGraphicsPipeline>
    {
    public:
        explicit BindGraphicsPipeline(vsg::ref_ptr<GraphicsPipeline> pipeline = {});

        vsg::ref_ptr<GraphicsPipeline> pipeline;

        int compare(const vsg::Object& rhs_object) const override;

        void record(vsg::CommandBuffer& commandBuffer) const override;
        void record(State& state) const;
    };

    VSG_type_name(vsg_webgpu::GraphicsPipeline);
    VSG_type_name(vsg_webgpu::BindGraphicsPipeline);

} // namespace vsg_webgpu
