# VSG Render Engine Abstraction CMake Configuration File


####### Expanded from @PACKAGE_INIT@ by configure_package_config_file() #######
####### Any changes to this file will be overwritten by the next CMake run ####
####### The input file was VSGAbstractionConfig.cmake.in                            ########

get_filename_component(PACKAGE_PREFIX_DIR "${CMAKE_CURRENT_LIST_DIR}/../../../" ABSOLUTE)

macro(set_and_check _var _file)
  set(${_var} "${_file}")
  if(NOT EXISTS "${_file}")
    message(FATAL_ERROR "File or directory ${_file} referenced by variable ${_var} does not exist !")
  endif()
endmacro()

macro(check_required_components _NAME)
  foreach(comp ${${_NAME}_FIND_COMPONENTS})
    if(NOT ${_NAME}_${comp}_FOUND)
      if(${_NAME}_FIND_REQUIRED_${comp})
        set(${_NAME}_FOUND FALSE)
      endif()
    endif()
  endforeach()
endmacro()

####################################################################################

# 版本信息
set(VSGAbstraction_VERSION "1.0.0")
set(VSGAbstraction_VERSION_MAJOR "1")
set(VSGAbstraction_VERSION_MINOR "0")
set(VSGAbstraction_VERSION_PATCH "0")

# 后端支持信息
set(VSGAbstraction_HAS_VSG )
set(VSGAbstraction_HAS_WEBGPU ON)
set(VSGAbstraction_HAS_OPENGL )
set(VSGAbstraction_HAS_MOCK )

# 构建配置
set(VSGAbstraction_SHARED_LIBS ON)

# 检查依赖
include(CMakeFindDependencyMacro)

# 线程库依赖
find_dependency(Threads REQUIRED)

# VSG依赖（如果启用）
if(VSGAbstraction_HAS_VSG)
    find_dependency(vsg)
endif()

# OpenGL依赖（如果启用）
if(VSGAbstraction_HAS_OPENGL)
    find_dependency(OpenGL)
endif()

# 包含目标文件
include("${CMAKE_CURRENT_LIST_DIR}/VSGAbstractionTargets.cmake")

# 检查目标是否存在
check_required_components(VSGAbstraction)

# 设置变量
set(VSGAbstraction_LIBRARIES VSGAbstraction::VSGAbstraction)
set(VSGAbstraction_INCLUDE_DIRS "${PACKAGE_PREFIX_DIR}/include")

# 兼容性变量
set(VSGABSTRACTION_FOUND TRUE)
set(VSGABSTRACTION_VERSION ${VSGAbstraction_VERSION})
set(VSGABSTRACTION_LIBRARIES ${VSGAbstraction_LIBRARIES})
set(VSGABSTRACTION_INCLUDE_DIRS ${VSGAbstraction_INCLUDE_DIRS})

# 显示配置信息
if(NOT VSGAbstraction_FIND_QUIETLY)
    message(STATUS "Found VSG Render Engine Abstraction: ${VSGAbstraction_VERSION}")
    message(STATUS "  Include directories: ${VSGAbstraction_INCLUDE_DIRS}")
    message(STATUS "  Libraries: ${VSGAbstraction_LIBRARIES}")
    message(STATUS "  Backend support:")
    message(STATUS "    VSG (Vulkan): ${VSGAbstraction_HAS_VSG}")
    message(STATUS "    WebGPU: ${VSGAbstraction_HAS_WEBGPU}")
    message(STATUS "    OpenGL: ${VSGAbstraction_HAS_OPENGL}")
    message(STATUS "    Mock: ${VSGAbstraction_HAS_MOCK}")
endif()

# 辅助宏
macro(vsg_abstraction_check_backend backend)
    string(TOUPPER ${backend} backend_upper)
    if(NOT VSGAbstraction_HAS_${backend_upper})
        message(FATAL_ERROR "VSG Abstraction was not built with ${backend} backend support")
    endif()
endmacro()

# 使用示例：
# find_package(VSGAbstraction REQUIRED)
# vsg_abstraction_check_backend(VSG)
# target_link_libraries(my_target ${VSGAbstraction_LIBRARIES})
