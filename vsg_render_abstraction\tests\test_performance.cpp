/* <editor-fold desc="MIT License">

Copyright(c) 2024 VSG Render Engine Abstraction

Permission is hereby granted, free of charge, to any person obtaining a copy of this software and associated documentation files (the "Software"), to deal in the Software without restriction, including without limitation the rights to use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of the Software, and to permit persons to whom the Software is furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND <PERSON>NINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.

</editor-fold> */

/**
 * @file test_performance.cpp
 * @brief 性能测试
 */

#include <vsg_abstraction/core/RenderEngineManager.h>
#include <vsg_abstraction/core/Config.h>

#include <iostream>
#include <chrono>
#include <vector>

using namespace vsg_abstraction;

int main() {
    std::cout << "=== 性能测试 ===" << std::endl;
    
    auto& manager = getRenderManager();
    
    // 切换到Mock后端进行测试
    if (!manager.switchBackend(RenderBackend::Mock)) {
        std::cout << "无法切换到Mock后端" << std::endl;
        return 1;
    }
    
    // 测试对象创建性能
    const int numObjects = 1000;
    
    std::cout << "测试创建 " << numObjects << " 个对象的性能..." << std::endl;
    
    auto startTime = std::chrono::high_resolution_clock::now();
    
    std::vector<ref_ptr<IGroup>> groups;
    std::vector<ref_ptr<ITransform>> transforms;
    std::vector<ref_ptr<IGeometry>> geometries;
    
    for (int i = 0; i < numObjects; ++i) {
        auto group = manager.createGroup();
        auto transform = manager.createTransform();
        auto geometry = manager.createGeometry();
        
        if (group) groups.push_back(group);
        if (transform) transforms.push_back(transform);
        if (geometry) geometries.push_back(geometry);
    }
    
    auto endTime = std::chrono::high_resolution_clock::now();
    auto duration = std::chrono::duration<double, std::milli>(endTime - startTime).count();
    
    std::cout << "创建时间: " << duration << "ms" << std::endl;
    std::cout << "平均每个对象: " << (duration / (numObjects * 3)) << "ms" << std::endl;
    
    // 测试场景图构建性能
    std::cout << "\n测试场景图构建性能..." << std::endl;
    
    startTime = std::chrono::high_resolution_clock::now();
    
    auto rootGroup = manager.createGroup();
    for (size_t i = 0; i < groups.size() && i < transforms.size() && i < geometries.size(); ++i) {
        transforms[i]->addChild(geometries[i]);
        groups[i]->addChild(transforms[i]);
        rootGroup->addChild(groups[i]);
    }
    
    endTime = std::chrono::high_resolution_clock::now();
    duration = std::chrono::duration<double, std::milli>(endTime - startTime).count();
    
    std::cout << "场景图构建时间: " << duration << "ms" << std::endl;
    
    // 测试统计信息
    auto stats = manager.getRenderStatistics();
    std::cout << "\n渲染统计信息:" << std::endl;
    std::cout << "  帧数: " << stats.frameCount << std::endl;
    std::cout << "  绘制调用: " << stats.drawCalls << std::endl;
    std::cout << "  三角形数: " << stats.triangles << std::endl;
    std::cout << "  内存使用: " << (stats.memoryUsed / 1024) << "KB" << std::endl;
    
    std::cout << "\n🎉 性能测试完成！" << std::endl;
    return 0;
}
