#pragma once

/* <editor-fold desc="MIT License">

Copyright(c) 2024 VSG WebGPU Extension

Permission is hereby granted, free of charge, to any person obtaining a copy of this software and associated documentation files (the "Software"), to deal in the Software without restriction, including without limitation the rights to use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of the Software, and to permit persons to whom the Software is furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.

</editor-fold> */

#include <vsg_webgpu/core/Export.h>
#include <vsg_webgpu/core/WebGPUHeaders.h>
#include <vsg_webgpu/vk/Device.h>
#include <vsg_webgpu/vk/CommandBuffer.h>
#include <vsg_webgpu/app/RecordTraversal.h>
#include <vsg/app/RenderGraph.h>
#include <vsg/core/Inherit.h>
#include <vsg/core/Object.h>
#include <vsg/core/ref_ptr.h>
#include <vsg/nodes/Group.h>
#include <vector>

namespace vsg_webgpu
{
    // forward declarations
    class Window;

    /// WebGPU渲染图，对应VSG的RenderGraph类
    /// 管理渲染通道和渲染目标
    class VSG_WEBGPU_DECLSPEC RenderGraph : public vsg::Inherit<vsg::Object, RenderGraph>
    {
    public:
        RenderGraph(Device* device, Window* window = nullptr);

        // 对应VSG RenderGraph的核心接口
        Device* device = nullptr;
        Window* window = nullptr;

        // 渲染通道配置
        struct RenderPass
        {
            std::string name;
            WGPURenderPassDescriptor descriptor;
            vsg::ref_ptr<vsg::Group> contents;
            
            // 渲染目标
            std::vector<WGPUTextureView> colorAttachments;
            WGPUTextureView depthStencilAttachment;
            
            // 清除值
            std::vector<WGPUColor> clearColors;
            float clearDepth = 1.0f;
            uint32_t clearStencil = 0;
        };

        std::vector<RenderPass> renderPasses;

        // 添加渲染通道
        void addRenderPass(const RenderPass& renderPass);
        void addRenderPass(const std::string& name, 
                          vsg::ref_ptr<vsg::Group> contents,
                          const std::vector<WGPUTextureView>& colorTargets = {},
                          WGPUTextureView depthTarget = {});

        // 录制渲染命令
        void record(vsg::ref_ptr<RecordTraversal> recordTraversal, 
                   vsg::ref_ptr<CommandBuffer> commandBuffer);

        // 执行渲染
        void render(vsg::ref_ptr<RecordTraversal> recordTraversal);

        // 编译渲染图
        void compile();

        // 获取统计信息
        struct Statistics
        {
            uint32_t numRenderPasses = 0;
            uint32_t numDrawCalls = 0;
            uint32_t totalTriangles = 0;
            double renderTime = 0.0;
        };

        const Statistics& getStatistics() const { return _statistics; }
        void resetStatistics() { _statistics = {}; }

        // 资源管理
        void createFramebufferResources(uint32_t width, uint32_t height);
        void resizeFramebufferResources(uint32_t width, uint32_t height);
        void releaseFramebufferResources();

    protected:
        virtual ~RenderGraph();

    private:
        Statistics _statistics;
        
        // 帧缓冲资源
        struct FramebufferResources
        {
            uint32_t width = 0;
            uint32_t height = 0;
            WGPUTexture colorTexture;
            WGPUTextureView colorTextureView;
            WGPUTexture depthTexture;
            WGPUTextureView depthTextureView;
        };
        
        FramebufferResources _framebufferResources;
        bool _framebufferResourcesValid = false;

        // 内部辅助方法
        void _setupRenderPassDescriptor(RenderPass& renderPass);
        void _recordRenderPass(const RenderPass& renderPass, 
                              vsg::ref_ptr<RecordTraversal> recordTraversal,
                              vsg::ref_ptr<CommandBuffer> commandBuffer);
        void _updateStatistics(const RecordTraversal::Statistics& traversalStats);
    };

    VSG_type_name(vsg_webgpu::RenderGraph);

} // namespace vsg_webgpu
