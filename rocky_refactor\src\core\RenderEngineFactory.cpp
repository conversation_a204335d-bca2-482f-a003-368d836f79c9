/* <editor-fold desc="MIT License">

Copyright(c) 2024 Rocky Render Engine Refactor

Permission is hereby granted, free of charge, to any person obtaining a copy of this software and associated documentation files (the "Software"), to deal in the Software without restriction, including without limitation the rights to use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of the Software, and to permit persons to whom the Software is furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.

</editor-fold> */

#include "RenderEngineFactory.h"
#include <rocky/render/Config.h>

// 包含各个后端的头文件
#ifdef ROCKY_HAS_VSG
#    include "../backends/vulkan/VulkanRenderEngine.h"
#endif

#ifdef ROCKY_HAS_WEBGPU
#    include "../backends/webgpu/WebGPURenderEngine.h"
#endif

#ifdef ROCKY_HAS_OPENGL
#    include "../backends/opengl/OpenGLRenderEngine.h"
#endif

#include "../backends/mock/MockRenderEngine.h"

namespace rocky
{
    namespace render
    {

        std::unique_ptr<IRenderEngine> RenderEngineFactory::create(RenderBackend backend, const void* config)
        {
            switch (backend)
            {
            case RenderBackend::Vulkan:
                return createVulkanEngine(config);

            case RenderBackend::WebGPU:
                return createWebGPUEngine(config);

            case RenderBackend::OpenGL:
                return createOpenGLEngine(config);

            case RenderBackend::Mock:
                return createMockEngine(config);

            default:
                ROCKY_LOG_ERROR("Unsupported render backend: " << static_cast<int>(backend));
                return nullptr;
            }
        }

        std::unique_ptr<IRenderEngine> RenderEngineFactory::createDefault()
        {
            auto backends = getBackendPriority();

            for (auto backend : backends)
            {
                if (isBackendSupported(backend))
                {
                    auto engine = create(backend);
                    if (engine)
                    {
                        ROCKY_LOG_INFO("Created default render engine: " << getRenderBackendName(backend));
                        return engine;
                    }
                }
            }

            ROCKY_LOG_ERROR("Failed to create any render engine");
            return nullptr;
        }

        std::vector<RenderBackend> RenderEngineFactory::getSupportedBackends()
        {
            std::vector<RenderBackend> supported;

            if (isVulkanAvailable())
            {
                supported.push_back(RenderBackend::Vulkan);
            }

            if (isWebGPUAvailable())
            {
                supported.push_back(RenderBackend::WebGPU);
            }

            if (isOpenGLAvailable())
            {
                supported.push_back(RenderBackend::OpenGL);
            }

            if (isMockAvailable())
            {
                supported.push_back(RenderBackend::Mock);
            }

            return supported;
        }

        bool RenderEngineFactory::isBackendSupported(RenderBackend backend)
        {
            switch (backend)
            {
            case RenderBackend::Vulkan:
                return isVulkanAvailable();

            case RenderBackend::WebGPU:
                return isWebGPUAvailable();

            case RenderBackend::OpenGL:
                return isOpenGLAvailable();

            case RenderBackend::Mock:
                return isMockAvailable();

            default:
                return false;
            }
        }

        RenderBackend RenderEngineFactory::getRecommendedBackend()
        {
            // 根据平台推荐最佳后端
#ifdef ROCKY_PLATFORM_WINDOWS
            // Windows: 优先Vulkan，其次WebGPU
            if (isVulkanAvailable()) return RenderBackend::Vulkan;
            if (isWebGPUAvailable()) return RenderBackend::WebGPU;
            if (isOpenGLAvailable()) return RenderBackend::OpenGL;
#elif defined(ROCKY_PLATFORM_LINUX)
            // Linux: 优先Vulkan，其次OpenGL
            if (isVulkanAvailable()) return RenderBackend::Vulkan;
            if (isOpenGLAvailable()) return RenderBackend::OpenGL;
            if (isWebGPUAvailable()) return RenderBackend::WebGPU;
#elif defined(ROCKY_PLATFORM_MACOS)
            // macOS: 优先WebGPU（Metal后端），其次OpenGL
            if (isWebGPUAvailable()) return RenderBackend::WebGPU;
            if (isOpenGLAvailable()) return RenderBackend::OpenGL;
            if (isVulkanAvailable()) return RenderBackend::Vulkan;
#elif defined(ROCKY_PLATFORM_EMSCRIPTEN)
            // Web: 只支持WebGPU
            if (isWebGPUAvailable()) return RenderBackend::WebGPU;
#endif

            // 默认返回Mock
            return RenderBackend::Mock;
        }

        std::vector<RenderBackend> RenderEngineFactory::getBackendPriority()
        {
            std::vector<RenderBackend> priority;

            // 根据平台设置优先级
#ifdef ROCKY_PLATFORM_WINDOWS
            priority = {RenderBackend::Vulkan, RenderBackend::WebGPU, RenderBackend::OpenGL, RenderBackend::Mock};
#elif defined(ROCKY_PLATFORM_LINUX)
            priority = {RenderBackend::Vulkan, RenderBackend::OpenGL, RenderBackend::WebGPU, RenderBackend::Mock};
#elif defined(ROCKY_PLATFORM_MACOS)
            priority = {RenderBackend::WebGPU, RenderBackend::OpenGL, RenderBackend::Vulkan, RenderBackend::Mock};
#elif defined(ROCKY_PLATFORM_EMSCRIPTEN)
            priority = {RenderBackend::WebGPU, RenderBackend::Mock};
#else
            priority = {RenderBackend::Mock};
#endif

            return priority;
        }

        // ========== 具体后端创建函数 ==========

        std::unique_ptr<IRenderEngine> RenderEngineFactory::createVulkanEngine(const void* config)
        {
            //#ifdef ROCKY_HAS_VSG
            if (isVulkanAvailable())
            {
                try
                {
                    return std::make_unique<VulkanRenderEngine>(config);
                }
                catch (const std::exception& e)
                {
                    ROCKY_LOG_ERROR("Failed to create Vulkan engine: " << e.what());
                }
            }
            //#else
            //    (void)config; // 避免未使用参数警告
            //    ROCKY_LOG_ERROR("Vulkan backend not compiled in");
            //#endif
            return nullptr;
        }

        std::unique_ptr<IRenderEngine> RenderEngineFactory::createWebGPUEngine(const void* config)
        {
#ifdef ROCKY_HAS_WEBGPU
            if (isWebGPUAvailable())
            {
                try
                {
                    return std::make_unique<WebGPURenderEngine>(config);
                }
                catch (const std::exception& e)
                {
                    ROCKY_LOG_ERROR("Failed to create WebGPU engine: " << e.what());
                }
            }
#else
            (void)config;
            ROCKY_LOG_ERROR("WebGPU backend not compiled in");
#endif
            return nullptr;
        }

        std::unique_ptr<IRenderEngine> RenderEngineFactory::createOpenGLEngine(const void* config)
        {
#ifdef ROCKY_HAS_OPENGL
            if (isOpenGLAvailable())
            {
                try
                {
                    return std::make_unique<OpenGLRenderEngine>(config);
                }
                catch (const std::exception& e)
                {
                    ROCKY_LOG_ERROR("Failed to create OpenGL engine: " << e.what());
                }
            }
#else
            (void)config;
            ROCKY_LOG_ERROR("OpenGL backend not compiled in");
#endif
            return nullptr;
        }

        std::unique_ptr<IRenderEngine> RenderEngineFactory::createMockEngine(const void* config)
        {
            try
            {
                return std::make_unique<MockRenderEngine>(config);
            }
            catch (const std::exception& e)
            {
                ROCKY_LOG_ERROR("Failed to create Mock engine: " << e.what());
            }
            return nullptr;
        }

        // ========== 后端可用性检测 ==========

        bool RenderEngineFactory::isVulkanAvailable()
        {
#ifdef ROCKY_HAS_VSG
            // 这里可以添加更详细的Vulkan可用性检测
            // 例如检查Vulkan驱动、VSG库等
            return true;
#else
            return false;
#endif
        }

        bool RenderEngineFactory::isWebGPUAvailable()
        {
#ifdef ROCKY_HAS_WEBGPU
            // 这里可以添加WebGPU可用性检测
            // 例如检查浏览器支持、Dawn库等
            return true;
#else
            return false;
#endif
        }

        bool RenderEngineFactory::isOpenGLAvailable()
        {
#ifdef ROCKY_HAS_OPENGL
            // 这里可以添加OpenGL可用性检测
            // 例如检查OpenGL驱动版本等
            return true;
#else
            return false;
#endif
        }

        bool RenderEngineFactory::isMockAvailable()
        {
            // Mock后端总是可用
            return true;
        }

    } // namespace render
} // namespace rocky
