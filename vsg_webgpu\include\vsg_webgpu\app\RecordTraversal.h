#pragma once

/* <editor-fold desc="MIT License">

Copyright(c) 2024 VSG WebGPU Extension

Permission is hereby granted, free of charge, to any person obtaining a copy of this software and associated documentation files (the "Software"), to deal in the Software without restriction, including without limitation the rights to use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of the Software, and to permit persons to whom the Software is furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.

</editor-fold> */

#include <vsg_webgpu/core/Export.h>
#include <vsg_webgpu/vk/CommandBuffer.h>
#include <vsg_webgpu/vk/State.h>
#include <vsg/app/RecordTraversal.h>
#include <vsg/core/Inherit.h>
#include <vsg/core/Visitor.h>
#include <vsg/core/ref_ptr.h>

namespace vsg_webgpu
{
    // forward declarations
    class Device;

    /// WebGPU记录遍历器，对应VSG的RecordTraversal类
    /// 用于遍历场景图并录制WebGPU渲染命令
    class VSG_WEBGPU_DECLSPEC RecordTraversal : public vsg::Inherit<vsg::Visitor, RecordTraversal>
    {
    public:
        RecordTraversal(vsg::ref_ptr<CommandBuffer> commandBuffer = {}, 
                       uint32_t maxSlot = 2, 
                       vsg::ref_ptr<State> state = {});

        // 对应VSG RecordTraversal的核心接口
        vsg::ref_ptr<CommandBuffer> getCommandBuffer() { return _commandBuffer; }
        vsg::ref_ptr<State> getState() { return _state; }

        const uint32_t maxSlot;
        const uint32_t binNumber = 0;

        // 设置命令缓冲区和状态
        void setCommandBuffer(vsg::ref_ptr<CommandBuffer> commandBuffer);
        void setState(vsg::ref_ptr<State> state);

        // 访问者接口实现
        void apply(vsg::Object& object) override;
        void apply(vsg::Group& group) override;
        void apply(vsg::StateGroup& stateGroup) override;
        void apply(vsg::Transform& transform) override;
        void apply(vsg::MatrixTransform& matrixTransform) override;
        void apply(vsg::Geometry& geometry) override;
        void apply(vsg::VertexIndexDraw& vid) override;
        void apply(vsg::BindVertexBuffers& bvb) override;
        void apply(vsg::BindIndexBuffer& bib) override;
        void apply(vsg::Draw& draw) override;
        void apply(vsg::DrawIndexed& drawIndexed) override;

        // WebGPU特定的访问方法
        void apply(vsg::StateCommand& stateCommand);

        // 渲染通道管理
        void beginRenderPass(const WGPURenderPassDescriptor& descriptor);
        void endRenderPass();

        // 计算通道管理
        void beginComputePass(const WGPUComputePassDescriptor& descriptor = {});
        void endComputePass();

        // 状态管理
        void pushState();
        void popState();

        // 统计信息
        struct Statistics
        {
            uint32_t numDrawCalls = 0;
            uint32_t numTriangles = 0;
            uint32_t numVertices = 0;
            uint32_t numStateChanges = 0;
            uint32_t numPipelineBinds = 0;
        };

        const Statistics& getStatistics() const { return _statistics; }
        void resetStatistics() { _statistics = {}; }

    protected:
        virtual ~RecordTraversal();

    private:
        vsg::ref_ptr<CommandBuffer> _commandBuffer;
        vsg::ref_ptr<State> _state;
        
        Statistics _statistics;

        // 内部状态管理
        struct StateFrame
        {
            vsg::MatrixStack::value_type projectionMatrix;
            vsg::MatrixStack::value_type modelviewMatrix;
        };
        
        std::vector<StateFrame> _stateStack;

        // 内部辅助方法
        void _recordStateCommands();
        void _updateStatistics(const std::string& operation, uint32_t count = 1);
    };

    VSG_type_name(vsg_webgpu::RecordTraversal);

} // namespace vsg_webgpu
