// include/rocky_webgpu/RenderGraph.h
#pragma once
#include <rocky_webgpu/Device.h>
#include <rocky_webgpu/RecordTraversal.h>
#include <rocky_webgpu/State.h>
#include <vsg/app/RenderGraph.h>
#include <vsg/app/View.h>

namespace rocky_webgpu
{

    // WebGPU渲染图
    class RenderGraph : public vsg::Inherit<vsg::RenderGraph, RenderGraph>
    {
    public:
        RenderGraph(Device* device);

        // 添加视图
        void addView(vsg::ref_ptr<vsg::View> view);

        // 渲染方法
        void render();

    private:
        vsg::ref_ptr<Device> _device;
        std::vector<vsg::ref_ptr<vsg::View>> _views;
    };

    // 实现
    RenderGraph::RenderGraph(Device* device) :
        _device(device) {}

    void RenderGraph::addView(vsg::ref_ptr<vsg::View> view)
    {
        _views.push_back(view);
    }

    void RenderGraph::render()
    {
        auto device = _device->getDevice();
        auto swapChain = _device->getSwapChain();
        auto textureView = _device->getCurrentTextureView();

        // 创建命令编码器
        auto commandEncoder = _device->createCommandEncoder();

        // 开始渲染通道
        wgpu::RenderPassDescriptor renderPassDesc{};
        wgpu::RenderPassColorAttachment colorAttachment{};
        colorAttachment.view = textureView;
        colorAttachment.loadOp = wgpu::LoadOp::Clear;
        colorAttachment.storeOp = wgpu::StoreOp::Store;
        colorAttachment.clearValue = {0.1f, 0.1f, 0.1f, 1.0f};

        renderPassDesc.colorAttachmentCount = 1;
        renderPassDesc.colorAttachments = &colorAttachment;

        wgpu::RenderPassDepthStencilAttachment depthAttachment{};
        depthAttachment.view = _device->getDepthTextureView();
        depthAttachment.depthLoadOp = wgpu::LoadOp::Clear;
        depthAttachment.depthStoreOp = wgpu::StoreOp::Store;
        depthAttachment.clearDepth = 1.0f;
        depthAttachment.stencilLoadOp = wgpu::LoadOp::Clear;
        depthAttachment.stencilStoreOp = wgpu::StoreOp::Store;
        depthAttachment.clearStencil = 0;

        renderPassDesc.depthStencilAttachment = &depthAttachment;

        auto renderPassEncoder = commandEncoder.BeginRenderPass(&renderPassDesc);

        // 创建状态对象
        auto state = vsg::ref_ptr<State>(new State(_device));
        state->setRenderPassEncoder(renderPassEncoder);

        // 遍历所有视图并渲染
        for (auto& view : _views)
        {
            auto recordTraversal = vsg::ref_ptr<RecordTraversal>(new RecordTraversal(state, renderPassEncoder));
            view->accept(*recordTraversal);
        }

        renderPassEncoder.EndPass();

        // 提交命令缓冲区
        auto commandBuffer = commandEncoder.Finish();
        _device->submitCommandBuffer(commandBuffer);
    }

} // namespace rocky_webgpu
