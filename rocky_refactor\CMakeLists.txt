cmake_minimum_required(VERSION 3.20)

project(RockyRenderRefactor
    VERSION 1.0.0
    DESCRIPTION "Rocky Render Engine Refactor - Multi-backend Rendering Abstraction"
    LANGUAGES CXX
)

# ========== 编译选项 ==========

set(CMAKE_CXX_STANDARD 20)
set(CMAKE_CXX_STANDARD_REQUIRED ON)
set(CMAKE_CXX_EXTENSIONS OFF)

# 设置输出目录
set(CMAKE_RUNTIME_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/bin)
set(CMAKE_LIBRARY_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/lib)
set(CMAKE_ARCHIVE_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/lib)

# Windows特定设置
if(WIN32)
    add_compile_options(/utf-8)
    add_compile_definitions(_CRT_SECURE_NO_WARNINGS)
endif()

# ========== 构建选项 ==========

option(ROCKY_BUILD_VULKAN_BACKEND "Build Vulkan backend (requires VSG)" ON)
option(ROCKY_BUILD_WEBGPU_BACKEND "Build WebGPU backend" ON)
option(ROCKY_BUILD_OPENGL_BACKEND "Build OpenGL backend" OFF)
option(ROCKY_BUILD_MOCK_BACKEND "Build Mock backend for testing" ON)
option(ROCKY_BUILD_TESTS "Build test programs" ON)
option(ROCKY_BUILD_EXAMPLES "Build example programs" ON)
option(ROCKY_BUILD_DOCS "Build documentation" OFF)

# ========== 依赖查找 ==========

# 查找VSG（用于Vulkan后端）
if(ROCKY_BUILD_VULKAN_BACKEND)
    find_package(vsg QUIET)
    if(vsg_FOUND)
        message(STATUS "Found VSG: ${vsg_DIR}")
        set(ROCKY_HAS_VSG ON)
    else()
        message(WARNING "VSG not found, Vulkan backend will be disabled")
        set(ROCKY_BUILD_VULKAN_BACKEND OFF)
        set(ROCKY_HAS_VSG OFF)
    endif()
endif()

# 查找WebGPU（用于WebGPU后端）
if(ROCKY_BUILD_WEBGPU_BACKEND)
    # 这里可以添加WebGPU的查找逻辑
    # find_package(webgpu QUIET)
    set(ROCKY_HAS_WEBGPU ON) # 暂时设为ON，实际应该根据查找结果
endif()

# 查找OpenGL（用于OpenGL后端）
if(ROCKY_BUILD_OPENGL_BACKEND)
    find_package(OpenGL QUIET)
    if(OpenGL_FOUND)
        set(ROCKY_HAS_OPENGL ON)
    else()
        message(WARNING "OpenGL not found, OpenGL backend will be disabled")
        set(ROCKY_BUILD_OPENGL_BACKEND OFF)
        set(ROCKY_HAS_OPENGL OFF)
    endif()
endif()

# ========== 配置头文件生成 ==========

configure_file(
    "${CMAKE_CURRENT_SOURCE_DIR}/include/rocky/render/Config.h.in"
    "${CMAKE_CURRENT_BINARY_DIR}/include/rocky/render/Config.h"
    @ONLY
)

# ========== 包含目录 ==========

include_directories(
    ${CMAKE_CURRENT_SOURCE_DIR}/include
    ${CMAKE_CURRENT_BINARY_DIR}/include
)

# ========== 子目录 ==========

# 核心库
add_subdirectory(src)

# 测试程序
if(ROCKY_BUILD_TESTS)
    enable_testing()
    add_subdirectory(tests)
endif()

# 示例程序
if(ROCKY_BUILD_EXAMPLES)
    add_subdirectory(examples)
endif()

# 文档
if(ROCKY_BUILD_DOCS)
    add_subdirectory(docs)
endif()

# ========== 安装配置 ==========

# 安装头文件
install(DIRECTORY include/rocky
    DESTINATION include
    FILES_MATCHING PATTERN "*.h"
)

# 安装生成的配置头文件
install(FILES "${CMAKE_CURRENT_BINARY_DIR}/include/rocky/render/Config.h"
    DESTINATION include/rocky/render
)

# ========== 构建信息输出 ==========

message(STATUS "")
message(STATUS "========== Rocky Render Refactor Configuration ==========")
message(STATUS "Version: ${PROJECT_VERSION}")
message(STATUS "Build Type: ${CMAKE_BUILD_TYPE}")
message(STATUS "C++ Standard: ${CMAKE_CXX_STANDARD}")
message(STATUS "")
message(STATUS "Backend Support:")
message(STATUS "  Vulkan (VSG): ${ROCKY_BUILD_VULKAN_BACKEND}")
if(ROCKY_BUILD_VULKAN_BACKEND)
    message(STATUS "    VSG Found: ${ROCKY_HAS_VSG}")
endif()
message(STATUS "  WebGPU: ${ROCKY_BUILD_WEBGPU_BACKEND}")
message(STATUS "  OpenGL: ${ROCKY_BUILD_OPENGL_BACKEND}")
message(STATUS "  Mock: ${ROCKY_BUILD_MOCK_BACKEND}")
message(STATUS "")
message(STATUS "Build Options:")
message(STATUS "  Tests: ${ROCKY_BUILD_TESTS}")
message(STATUS "  Examples: ${ROCKY_BUILD_EXAMPLES}")
message(STATUS "  Documentation: ${ROCKY_BUILD_DOCS}")
message(STATUS "")
message(STATUS "Install Prefix: ${CMAKE_INSTALL_PREFIX}")
message(STATUS "==========================================================")
