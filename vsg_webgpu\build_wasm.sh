#!/bin/bash

# VSG WebGPU WebAssembly Build Script

set -e  # Exit on any error

echo "========================================"
echo "VSG WebGPU WebAssembly Build Script"
echo "========================================"

# 默认设置
BUILD_TYPE="Release"
BUILD_DIR="build_wasm"
INSTALL_DIR="redist_wasm"
CLEAN_BUILD=false
SHOW_HELP=false
EMSCRIPTEN_ROOT="${EMSCRIPTEN:-/usr/local/emsdk}"

# 解析命令行参数
while [[ $# -gt 0 ]]; do
    case $1 in
        debug)
            BUILD_TYPE="Debug"
            shift
            ;;
        release)
            BUILD_TYPE="Release"
            shift
            ;;
        clean)
            CLEAN_BUILD=true
            shift
            ;;
        help|--help|-h)
            SHOW_HELP=true
            shift
            ;;
        --emscripten-root)
            EMSCRIPTEN_ROOT="$2"
            shift 2
            ;;
        *)
            echo "Unknown option: $1"
            echo "Use 'help' for usage information"
            exit 1
            ;;
    esac
done

# 显示帮助
if [ "$SHOW_HELP" = true ]; then
    echo "Usage: $0 [options]"
    echo ""
    echo "Options:"
    echo "  debug                    - Build in Debug mode"
    echo "  release                  - Build in Release mode (default)"
    echo "  clean                    - Clean build and install directories"
    echo "  help                     - Show this help message"
    echo "  --emscripten-root PATH   - Set Emscripten root directory"
    echo ""
    echo "Environment Variables:"
    echo "  EMSCRIPTEN              - Emscripten root directory (default: /usr/local/emsdk)"
    echo ""
    echo "Examples:"
    echo "  $0                      - Build in Release mode"
    echo "  $0 debug                - Build in Debug mode"
    echo "  $0 clean                - Clean all build artifacts"
    echo "  $0 --emscripten-root /path/to/emsdk"
    echo ""
    exit 0
fi

# 清理构建
if [ "$CLEAN_BUILD" = true ]; then
    echo "Cleaning build directory..."
    rm -rf "$BUILD_DIR"
    rm -rf "$INSTALL_DIR"
    echo "Clean completed."
    exit 0
fi

echo "Build Type: $BUILD_TYPE"
echo "Build Directory: $BUILD_DIR"
echo "Install Directory: $INSTALL_DIR"
echo "Emscripten Root: $EMSCRIPTEN_ROOT"
echo ""

# 检查Emscripten
echo "Checking Emscripten installation..."

# 尝试多个可能的Emscripten位置
EMSCRIPTEN_PATHS=(
    "$EMSCRIPTEN_ROOT"
    "/usr/local/emsdk"
    "/opt/emsdk"
    "$HOME/emsdk"
    "C:/dev/emsdk"
)

EMSCRIPTEN_FOUND=false
for path in "${EMSCRIPTEN_PATHS[@]}"; do
    if [ -f "$path/emsdk_env.sh" ]; then
        EMSCRIPTEN_ROOT="$path"
        EMSCRIPTEN_FOUND=true
        echo "Found Emscripten at: $EMSCRIPTEN_ROOT"
        break
    fi
done

if [ "$EMSCRIPTEN_FOUND" = false ]; then
    echo "ERROR: Emscripten not found"
    echo "Please install Emscripten and set the EMSCRIPTEN environment variable"
    echo "or use --emscripten-root to specify the path"
    echo ""
    echo "To install Emscripten:"
    echo "  git clone https://github.com/emscripten-core/emsdk.git"
    echo "  cd emsdk"
    echo "  ./emsdk install latest"
    echo "  ./emsdk activate latest"
    echo "  source ./emsdk_env.sh"
    exit 1
fi

# 激活Emscripten环境
echo "Activating Emscripten environment..."
source "$EMSCRIPTEN_ROOT/emsdk_env.sh"

# 验证Emscripten工具
if ! command -v emcc &> /dev/null; then
    echo "ERROR: emcc not found after activating Emscripten"
    echo "Please check your Emscripten installation"
    exit 1
fi

if ! command -v em++ &> /dev/null; then
    echo "ERROR: em++ not found after activating Emscripten"
    echo "Please check your Emscripten installation"
    exit 1
fi

echo "Emscripten version: $(emcc --version | head -n1)"
echo ""

# 检查VSG for WebAssembly
echo "Checking VSG WebAssembly build..."
echo "Note: VSG must be built for WebAssembly separately"
echo ""

# 创建构建目录
echo "Creating build directory..."
mkdir -p "$BUILD_DIR"
mkdir -p "$INSTALL_DIR"

# 配置CMake for WebAssembly
echo ""
echo "========================================"
echo "Configuring CMake for WebAssembly..."
echo "========================================"

cd "$BUILD_DIR"

# Emscripten CMake参数
CMAKE_ARGS=(
    -DCMAKE_TOOLCHAIN_FILE="$EMSCRIPTEN_ROOT/upstream/emscripten/cmake/Modules/Platform/Emscripten.cmake"
    -DCMAKE_BUILD_TYPE="$BUILD_TYPE"
    -DCMAKE_INSTALL_PREFIX="../$INSTALL_DIR"
    -DVSSG_WEBGPU_BUILD_TESTS=ON
    -DVSSG_WEBGPU_BUILD_EXAMPLES=ON
    -DVSSG_WEBGPU_USE_EMSCRIPTEN=ON
    -DVSSG_WEBGPU_USE_DAWN=OFF
)

# WebAssembly特定的编译标志
if [ "$BUILD_TYPE" = "Debug" ]; then
    export EMCC_DEBUG=1
    CMAKE_ARGS+=(
        -DCMAKE_CXX_FLAGS="-g -O0 -sASSERTIONS=1"
        -DCMAKE_EXE_LINKER_FLAGS="-g -O0 -sASSERTIONS=1 -sUSE_WEBGPU=1 -sASYNCIFY -sALLOW_MEMORY_GROWTH=1"
    )
else
    CMAKE_ARGS+=(
        -DCMAKE_CXX_FLAGS="-O3 -DNDEBUG"
        -DCMAKE_EXE_LINKER_FLAGS="-O3 -sUSE_WEBGPU=1 -sASYNCIFY -sALLOW_MEMORY_GROWTH=1 -sMINIFY_HTML=0"
    )
fi

cmake .. "${CMAKE_ARGS[@]}"

if [ $? -ne 0 ]; then
    echo "ERROR: CMake configuration failed"
    cd ..
    exit 1
fi

echo "CMake configuration completed successfully."
echo ""

# 构建项目
echo "========================================"
echo "Building project for WebAssembly..."
echo "========================================"

cmake --build . --config "$BUILD_TYPE"

if [ $? -ne 0 ]; then
    echo "ERROR: Build failed"
    cd ..
    exit 1
fi

echo "Build completed successfully."
echo ""

# 安装项目
echo "========================================"
echo "Installing project..."
echo "========================================"

cmake --install . --config "$BUILD_TYPE"

if [ $? -ne 0 ]; then
    echo "ERROR: Installation failed"
    cd ..
    exit 1
fi

echo "Installation completed successfully."
echo ""

# 创建简单的HTTP服务器脚本
echo "Creating HTTP server script..."
cat > "../$INSTALL_DIR/serve.py" << 'EOF'
#!/usr/bin/env python3
import http.server
import socketserver
import os
import sys

PORT = 8000

class MyHTTPRequestHandler(http.server.SimpleHTTPRequestHandler):
    def end_headers(self):
        self.send_header('Cross-Origin-Embedder-Policy', 'require-corp')
        self.send_header('Cross-Origin-Opener-Policy', 'same-origin')
        super().end_headers()

if __name__ == "__main__":
    if len(sys.argv) > 1:
        PORT = int(sys.argv[1])
    
    os.chdir(os.path.dirname(os.path.abspath(__file__)))
    
    with socketserver.TCPServer(("", PORT), MyHTTPRequestHandler) as httpd:
        print(f"Serving at http://localhost:{PORT}")
        print("Press Ctrl+C to stop the server")
        try:
            httpd.serve_forever()
        except KeyboardInterrupt:
            print("\nServer stopped.")
EOF

chmod +x "../$INSTALL_DIR/serve.py"

cd ..

echo ""
echo "========================================"
echo "Build Summary"
echo "========================================"
echo "Build Type: $BUILD_TYPE"
echo "Build Directory: $BUILD_DIR"
echo "Install Directory: $INSTALL_DIR"
echo ""
echo "WebAssembly artifacts:"
if [ -d "$INSTALL_DIR" ] && [ "$(ls -A $INSTALL_DIR/*.html 2>/dev/null)" ]; then
    echo "  HTML files: $INSTALL_DIR/*.html"
fi
if [ -d "$INSTALL_DIR" ] && [ "$(ls -A $INSTALL_DIR/*.js 2>/dev/null)" ]; then
    echo "  JavaScript files: $INSTALL_DIR/*.js"
fi
if [ -d "$INSTALL_DIR" ] && [ "$(ls -A $INSTALL_DIR/*.wasm 2>/dev/null)" ]; then
    echo "  WebAssembly files: $INSTALL_DIR/*.wasm"
fi
echo ""
echo "To test the WebAssembly build:"
echo "  cd $INSTALL_DIR"
echo "  python3 serve.py"
echo "  Open http://localhost:8000 in a WebGPU-enabled browser"
echo ""
echo "Build completed successfully!"
