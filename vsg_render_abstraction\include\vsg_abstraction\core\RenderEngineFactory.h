#pragma once

/* <editor-fold desc="MIT License">

Copyright(c) 2024 VSG Render Engine Abstraction

Permission is hereby granted, free of charge, to any person obtaining a copy of this software and associated documentation files (the "Software"), to deal in the Software without restriction, including without limitation the rights to use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of the Software, and to permit persons to whom the Software is furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND <PERSON>NINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.

</editor-fold> */

#include <functional>
#include <memory>
#include <vector>
#include <vsg_abstraction/core/Export.h>
#include <vsg_abstraction/core/IRenderEngine.h>
#include <vsg_abstraction/core/Types.h>

// 前向声明VSG类型
namespace vsg
{
    class Instance;
    class Device;
    class PhysicalDevice;
    template<typename T>
    class ref_ptr;
} // namespace vsg

namespace vsg_abstraction
{

    // RenderEngineConfig已在Types.h中定义

    /**
 * @brief 渲染引擎工厂类
 * 
 * 负责创建不同类型的渲染引擎实例。
 * 支持运行时检测可用的渲染后端。
 * 
 * 设计目标：
 * 1. 支持多种渲染后端的创建
 * 2. 自动检测平台能力
 * 3. 提供智能的后端选择策略
 * 4. 支持自定义配置
 * 5. 与VSG无缝集成
 */
    class VSG_ABSTRACTION_DECLSPEC RenderEngineFactory
    {
    public:
        /**
     * @brief 创建指定类型的渲染引擎
     * @param backend 渲染后端类型
     * @param config 可选的配置参数
     * @return 渲染引擎实例，失败返回nullptr
     */
        static std::unique_ptr<IRenderEngine> create(RenderBackend backend, const RenderEngineConfig* config = nullptr);

        /**
     * @brief 创建默认渲染引擎
     * 按优先级顺序尝试创建可用的渲染引擎
     * @param config 可选的配置参数
     * @return 渲染引擎实例，失败返回nullptr
     */
        static std::unique_ptr<IRenderEngine> createDefault(const RenderEngineConfig* config = nullptr);

#ifdef VSG_ABSTRACTION_HAS_VSG
        /**
     * @brief 从现有VSG对象创建渲染引擎
     * @param instance VSG实例
     * @param device VSG设备（可选）
     * @param config 可选的配置参数
     * @return 渲染引擎实例，失败返回nullptr
     */
        static std::unique_ptr<IRenderEngine> createFromVSG(
            vsg::ref_ptr<vsg::Instance> instance,
            vsg::ref_ptr<vsg::Device> device = {},
            const RenderEngineConfig* config = nullptr);
#endif

        /**
     * @brief 获取所有支持的渲染后端
     * @return 支持的渲染后端列表
     */
        static std::vector<RenderBackend> getSupportedBackends();

        /**
     * @brief 检查指定后端是否支持
     * @param backend 渲染后端类型
     * @return 支持返回true
     */
        static bool isBackendSupported(RenderBackend backend);

        /**
     * @brief 检查指定后端是否可用
     * @param backend 渲染后端类型
     * @return 可用返回true
     */
        static bool isBackendAvailable(RenderBackend backend);

        /**
     * @brief 获取后端描述信息
     * @param backend 渲染后端类型
     * @return 后端描述字符串
     */
        static std::string getBackendDescription(RenderBackend backend);

        /**
     * @brief 获取推荐的渲染后端
     * 根据当前平台和硬件能力推荐最佳后端
     * @return 推荐的渲染后端
     */
        static RenderBackend getRecommendedBackend();

        /**
     * @brief 获取后端优先级列表
     * 按性能和兼容性排序的后端列表
     * @return 后端优先级列表
     */
        static std::vector<RenderBackend> getBackendPriority();

        // ========== 后端能力查询 ==========

        /**
     * @brief 获取后端能力信息
     * @param backend 渲染后端类型
     * @return 能力信息
     */
        static DeviceCapabilities getBackendCapabilities(RenderBackend backend);

        /**
     * @brief 检查后端是否支持特定功能
     * @param backend 渲染后端类型
     * @param feature 功能名称
     * @return 支持返回true
     */
        static bool supportsFeature(RenderBackend backend, const std::string& feature);

        /**
     * @brief 获取后端需求信息
     * @param backend 渲染后端类型
     * @return 需求信息列表
     */
        static std::vector<std::string> getBackendRequirements(RenderBackend backend);

        /**
     * @brief 打印后端信息
     * @param backend 渲染后端类型
     */
        static void printBackendInfo(RenderBackend backend);

        // ========== 平台检测 ==========

        /**
     * @brief 检查是否在Web平台
     */
        static bool isWebPlatform();

        /**
     * @brief 检查是否在移动平台
     */
        static bool isMobilePlatform();

        /**
     * @brief 检查是否在桌面平台
     */
        static bool isDesktopPlatform();

        /**
     * @brief 获取平台名称
     */
        static std::string getPlatformName();

        // ========== 回调和事件 ==========

        /**
     * @brief 后端创建回调函数类型
     */
        using BackendCreateCallback = std::function<void(RenderBackend backend, bool success)>;

        /**
     * @brief 设置后端创建回调
     * @param callback 回调函数
     */
        static void setBackendCreateCallback(BackendCreateCallback callback);

        // ========== 注册和扩展 ==========

        /**
     * @brief 渲染引擎创建函数类型
     */
        using EngineCreateFunction = std::function<std::unique_ptr<IRenderEngine>(const RenderEngineConfig*)>;

        /**
     * @brief 注册自定义渲染引擎创建函数
     * @param backend 渲染后端类型
     * @param createFunc 创建函数
     */
        static void registerEngineCreator(RenderBackend backend, EngineCreateFunction createFunc);

        /**
     * @brief 注销渲染引擎创建函数
     * @param backend 渲染后端类型
     */
        static void unregisterEngineCreator(RenderBackend backend);

    private:
        // 具体后端创建函数
        static std::unique_ptr<IRenderEngine> createVulkanEngine(const RenderEngineConfig* config);
        static std::unique_ptr<IRenderEngine> createWebGPUEngine(const RenderEngineConfig* config);
        static std::unique_ptr<IRenderEngine> createOpenGLEngine(const RenderEngineConfig* config);
        static std::unique_ptr<IRenderEngine> createMockEngine(const RenderEngineConfig* config);

        // 后端可用性检测
        static bool isVulkanAvailable();
        static bool isWebGPUAvailable();
        static bool isOpenGLAvailable();
        static bool isMockAvailable();

        // 内部辅助函数
        static void _notifyBackendCreate(RenderBackend backend, bool success);
        static RenderEngineConfig _getDefaultConfig(RenderBackend backend);

        // 静态成员
        static BackendCreateCallback backendCreateCallback_;
        static std::unordered_map<RenderBackend, EngineCreateFunction> customCreators_;
    };

    // ========== 便捷函数 ==========

#ifdef VSG_ABSTRACTION_HAS_VSG
    /**
 * @brief 创建Vulkan渲染引擎的便捷函数
 * @param instance VSG实例（可选）
 * @param device VSG设备（可选）
 * @return 渲染引擎实例
 */
    inline std::unique_ptr<IRenderEngine> createVulkanEngine(
        vsg::ref_ptr<vsg::Instance> instance = {},
        vsg::ref_ptr<vsg::Device> device = {})
    {
        RenderEngineConfig config;
        config.vulkan.instance = instance;
        config.vulkan.device = device;
        config.vulkan.useExistingVSGObjects = (instance != nullptr);
        return RenderEngineFactory::create(RenderBackend::Vulkan, &config);
    }
#endif

    /**
 * @brief 创建WebGPU渲染引擎的便捷函数
 * @param enableValidation 是否启用验证
 * @return 渲染引擎实例
 */
    inline std::unique_ptr<IRenderEngine> createWebGPUEngine(bool enableValidation = false)
    {
        RenderEngineConfig config;
        config.webgpu.enableDebugAdapter = enableValidation;
        return RenderEngineFactory::create(RenderBackend::WebGPU, &config);
    }

    /**
 * @brief 创建OpenGL渲染引擎的便捷函数
 * @param majorVersion OpenGL主版本号
 * @param minorVersion OpenGL次版本号
 * @return 渲染引擎实例
 */
    inline std::unique_ptr<IRenderEngine> createOpenGLEngine(int majorVersion = 4, int minorVersion = 5)
    {
        RenderEngineConfig config;
        config.opengl.majorVersion = majorVersion;
        config.opengl.minorVersion = minorVersion;
        return RenderEngineFactory::create(RenderBackend::OpenGL, &config);
    }

    /**
 * @brief 创建Mock渲染引擎的便捷函数
 * @param enableStatistics 是否启用统计
 * @return 渲染引擎实例
 */
    inline std::unique_ptr<IRenderEngine> createMockEngine(bool enableStatistics = true)
    {
        RenderEngineConfig config;
        config.mock.enableStatistics = enableStatistics;
        return RenderEngineFactory::create(RenderBackend::Mock, &config);
    }

    /**
 * @brief 自动选择最佳渲染引擎的便捷函数
 * @param preferVSG 是否优先使用VSG（如果可用）
 * @return 渲染引擎实例
 */
    inline std::unique_ptr<IRenderEngine> createBestEngine(bool preferVSG = true)
    {
        if (preferVSG && RenderEngineFactory::isBackendSupported(RenderBackend::Vulkan))
        {
            return RenderEngineFactory::create(RenderBackend::Vulkan);
        }
        return RenderEngineFactory::createDefault();
    }

} // namespace vsg_abstraction
