#pragma once

/* <editor-fold desc="MIT License">

Copyright(c) 2024 VSG Render Engine Abstraction

Permission is hereby granted, free of charge, to any person obtaining a copy of this software and associated documentation files (the "Software"), to deal in the Software without restriction, including without limitation the rights to use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of the Software, and to permit persons to whom the Software is furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.

</editor-fold> */

#include <vsg_abstraction/core/IRenderEngine.h>
#include <vsg_abstraction/core/RenderEngineFactory.h>
#include <vsg_abstraction/core/Export.h>

// VSG头文件
#include <vsg/all.h>

namespace vsg_abstraction {

// 前向声明
class VSGDevice;
class VSGInstance;
class VSGPhysicalDevice;
class VSGNode;
class VSGGroup;
class VSGTransform;
class VSGGeometry;
class VSGStateGroup;
class VSGWindow;
class VSGRenderGraph;
class VSGRecordTraversal;

/**
 * @brief VSG渲染引擎实现
 * 
 * 这是基于VulkanSceneGraph的渲染引擎实现，作为抽象层的Vulkan后端。
 * 
 * 设计目标：
 * 1. 完全兼容现有VSG API
 * 2. 提供零成本抽象
 * 3. 支持现有VSG对象的直接使用
 * 4. 保持VSG的高性能特性
 * 5. 支持渐进式迁移
 */
class VSG_ABSTRACTION_DECLSPEC VSGRenderEngine : public IRenderEngine {
public:
    /**
     * @brief 构造函数
     * @param config 配置参数
     */
    explicit VSGRenderEngine(const RenderEngineConfig* config = nullptr);
    
    /**
     * @brief 从现有VSG对象构造
     * @param instance VSG实例
     * @param device VSG设备（可选）
     * @param config 配置参数
     */
    VSGRenderEngine(vsg::ref_ptr<vsg::Instance> instance, 
                    vsg::ref_ptr<vsg::Device> device = {},
                    const RenderEngineConfig* config = nullptr);
    
    virtual ~VSGRenderEngine();

    // ========== 生命周期管理 ==========
    bool initialize() override;
    void shutdown() override;
    bool isInitialized() const override;

    // ========== 后端信息 ==========
    RenderBackend getBackendType() const override;
    EngineInfo getEngineInfo() const override;

    // ========== 设备管理 ==========
    IDevice* getDevice() override;
    IInstance* getInstance() override;
    std::vector<IPhysicalDevice*> getPhysicalDevices() override;

    // ========== 场景图创建 ==========
    ref_ptr<IGroup> createGroup() override;
    ref_ptr<ITransform> createTransform() override;
    ref_ptr<ITransform> createMatrixTransform() override;
    ref_ptr<IGeometry> createGeometry() override;
    ref_ptr<IStateGroup> createStateGroup() override;

    // ========== 窗口和渲染管理 ==========
    ref_ptr<IWindow> createWindow(const WindowTraits& traits) override;
    ref_ptr<IRenderGraph> createRenderGraph(ref_ptr<IWindow> window, ref_ptr<INode> view = {}) override;
    ref_ptr<IRecordTraversal> createRecordTraversal() override;

    // ========== 资源管理 ==========
    ref_ptr<IBuffer> createBuffer(const BufferInfo& info) override;
    ref_ptr<IImage> createImage(const ImageInfo& info) override;
    ref_ptr<IPipeline> createGraphicsPipeline(const GraphicsPipelineInfo& info) override;
    ref_ptr<IPipeline> createComputePipeline(const ComputePipelineInfo& info) override;

    // ========== VSG兼容性接口 ==========
    vsg::ref_ptr<vsg::Group> createVSGGroup() override;
    vsg::ref_ptr<vsg::MatrixTransform> createVSGTransform() override;
    vsg::ref_ptr<vsg::Geometry> createVSGGeometry() override;
    vsg::ref_ptr<vsg::StateGroup> createVSGStateGroup() override;
    vsg::ref_ptr<vsg::Device> getVSGDevice() override;

    // ========== 同步和性能 ==========
    void waitIdle() override;
    RenderStatistics getRenderStatistics() const override;
    void resetRenderStatistics() override;

    // ========== 调试和诊断 ==========
    void setDebugCallback(DebugCallback callback) override;
    void beginDebugMarker(const std::string& name) override;
    void endDebugMarker() override;

    // ========== 扩展接口 ==========
    void* getNativeHandle() const override;
    void executeCustomCommand(std::function<void(void*)> command) override;
    bool supportsFeature(const std::string& feature) const override;

    // ========== VSG特定接口 ==========
    
    /**
     * @brief 获取VSG实例
     */
    vsg::ref_ptr<vsg::Instance> getVSGInstance() const;
    
    /**
     * @brief 获取VSG物理设备
     */
    vsg::ref_ptr<vsg::PhysicalDevice> getVSGPhysicalDevice() const;
    
    /**
     * @brief 设置VSG对象（用于现有VSG应用的集成）
     * @param instance VSG实例
     * @param device VSG设备
     */
    void setVSGObjects(vsg::ref_ptr<vsg::Instance> instance, vsg::ref_ptr<vsg::Device> device);
    
    /**
     * @brief 检查是否使用现有VSG对象
     */
    bool isUsingExistingVSGObjects() const;

    // ========== 适配器接口 ==========
    
    /**
     * @brief 从VSG节点创建抽象节点
     * @param vsgNode VSG节点
     * @return 抽象节点
     */
    ref_ptr<INode> wrapVSGNode(vsg::ref_ptr<vsg::Node> vsgNode);
    
    /**
     * @brief 从抽象节点获取VSG节点
     * @param abstractNode 抽象节点
     * @return VSG节点
     */
    vsg::ref_ptr<vsg::Node> unwrapVSGNode(ref_ptr<INode> abstractNode);

private:
    // 内部初始化方法
    bool _initializeInstance();
    bool _initializeDevice();
    bool _initializeQueues();
    void _setupDebugCallback();
    void _updateStatistics();
    
    // 内部状态
    bool initialized_ = false;
    bool usingExistingVSGObjects_ = false;
    RenderEngineConfig config_;
    
    // VSG对象
    vsg::ref_ptr<vsg::Instance> vsgInstance_;
    vsg::ref_ptr<vsg::Device> vsgDevice_;
    vsg::ref_ptr<vsg::PhysicalDevice> vsgPhysicalDevice_;
    vsg::ref_ptr<vsg::Queue> vsgQueue_;
    vsg::ref_ptr<vsg::CommandPool> vsgCommandPool_;
    
    // 抽象层包装对象
    std::unique_ptr<VSGDevice> device_;
    std::unique_ptr<VSGInstance> instance_;
    std::vector<std::unique_ptr<VSGPhysicalDevice>> physicalDevices_;
    
    // 回调函数
    DebugCallback debugCallback_;
    
    // 统计信息
    mutable RenderStatistics statistics_;
    mutable std::mutex statisticsMutex_;
    
    // 调试标记栈
    std::vector<std::string> debugMarkerStack_;
    
    // 对象缓存
    std::unordered_map<vsg::Node*, std::weak_ptr<INode>> nodeCache_;
    mutable std::mutex cacheMutex_;
};

/**
 * @brief VSG设备包装类
 */
class VSG_ABSTRACTION_DECLSPEC VSGDevice : public IDevice {
public:
    explicit VSGDevice(vsg::ref_ptr<vsg::Device> vsgDevice);
    virtual ~VSGDevice() = default;

    bool isValid() const override;
    uint32_t getDeviceID() const override;
    IQueue* getQueue(uint32_t familyIndex = 0) override;
    ICommandPool* getCommandPool(uint32_t familyIndex = 0) override;
    void waitIdle() override;
    void* getNativeHandle() const override;
    
    // VSG特定接口
    vsg::ref_ptr<vsg::Device> getVSGDevice() const { return vsgDevice_; }

private:
    vsg::ref_ptr<vsg::Device> vsgDevice_;
    std::vector<std::unique_ptr<class VSGQueue>> queues_;
    std::vector<std::unique_ptr<class VSGCommandPool>> commandPools_;
};

/**
 * @brief VSG实例包装类
 */
class VSG_ABSTRACTION_DECLSPEC VSGInstance : public IInstance {
public:
    explicit VSGInstance(vsg::ref_ptr<vsg::Instance> vsgInstance);
    virtual ~VSGInstance() = default;

    bool isValid() const override;
    std::vector<IPhysicalDevice*> getPhysicalDevices() override;
    void* getNativeHandle() const override;
    
    // VSG特定接口
    vsg::ref_ptr<vsg::Instance> getVSGInstance() const { return vsgInstance_; }

private:
    vsg::ref_ptr<vsg::Instance> vsgInstance_;
    std::vector<std::unique_ptr<class VSGPhysicalDevice>> physicalDevices_;
};

/**
 * @brief VSG物理设备包装类
 */
class VSG_ABSTRACTION_DECLSPEC VSGPhysicalDevice : public IPhysicalDevice {
public:
    explicit VSGPhysicalDevice(vsg::ref_ptr<vsg::PhysicalDevice> vsgPhysicalDevice);
    virtual ~VSGPhysicalDevice() = default;

    std::string getDeviceName() const override;
    DeviceCapabilities getCapabilities() const override;
    bool isDiscrete() const override;
    void* getNativeHandle() const override;
    
    // VSG特定接口
    vsg::ref_ptr<vsg::PhysicalDevice> getVSGPhysicalDevice() const { return vsgPhysicalDevice_; }

private:
    vsg::ref_ptr<vsg::PhysicalDevice> vsgPhysicalDevice_;
};

} // namespace vsg_abstraction
