/* <editor-fold desc="MIT License">

Copyright(c) 2024 Rocky Render Engine Refactor

Permission is hereby granted, free of charge, to any person obtaining a copy of this software and associated documentation files (the "Software"), to deal in the Software without restriction, including without limitation the rights to use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of the Software, and to permit persons to whom the Software is furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.

</editor-fold> */

#include "MockRenderEngine.h"
#include "MockWindow.h"
#include "MockSceneNode.h"
#include "MockRenderGraph.h"
#include "MockResources.h"
#include <thread>
#include <random>

namespace rocky {
namespace render {

MockRenderEngine::MockRenderEngine(const void* config) {
    (void)config; // 忽略配置参数
    
    _logDebug("MockRenderEngine created");
    lastRenderTime_ = std::chrono::high_resolution_clock::now();
}

MockRenderEngine::~MockRenderEngine() {
    shutdown();
    _logDebug("MockRenderEngine destroyed");
}

bool MockRenderEngine::initialize() {
    std::lock_guard<std::mutex> lock(mutex_);
    
    if (initialized_) {
        _logDebug("MockRenderEngine already initialized");
        return true;
    }
    
    _logDebug("Initializing MockRenderEngine...");
    
    // 模拟初始化延迟
    std::this_thread::sleep_for(std::chrono::milliseconds(100));
    
    // 模拟初始化错误
    if (_shouldSimulateError()) {
        _logDebug("Simulated initialization error");
        return false;
    }
    
    initialized_ = true;
    resetRenderStatistics();
    
    _logDebug("MockRenderEngine initialized successfully");
    return true;
}

void MockRenderEngine::shutdown() {
    std::lock_guard<std::mutex> lock(mutex_);
    
    if (!initialized_) {
        return;
    }
    
    _logDebug("Shutting down MockRenderEngine...");
    
    // 清理资源
    objectStats_ = {};
    debugMarkerStack_.clear();
    
    initialized_ = false;
    
    _logDebug("MockRenderEngine shutdown completed");
}

bool MockRenderEngine::isInitialized() const {
    std::lock_guard<std::mutex> lock(mutex_);
    return initialized_;
}

RenderBackend MockRenderEngine::getBackendType() const {
    return RenderBackend::Mock;
}

RenderEngineInfo MockRenderEngine::getEngineInfo() const {
    return {
        "Mock Render Engine",
        ROCKY_RENDER_VERSION,
        "Rocky Render Refactor",
        RenderBackend::Mock,
        {"mock_extension_1", "mock_extension_2"}
    };
}

DeviceCapabilities MockRenderEngine::getDeviceCapabilities() const {
    return {
        8192,  // maxTextureSize
        256,   // maxTextureLayers
        65536, // maxUniformBufferSize
        16,    // maxVertexAttributes
        16,    // maxViewports
        true,  // supportsGeometryShader
        true,  // supportsTessellation
        true,  // supportsComputeShader
        true,  // supportsMultisampling
        {      // supportedFormats
            TextureFormat::RGBA8,
            TextureFormat::RGBA16F,
            TextureFormat::RGBA32F,
            TextureFormat::RGB8,
            TextureFormat::Depth24Stencil8
        }
    };
}

std::shared_ptr<IWindow> MockRenderEngine::createWindow(const WindowConfig& config) {
    std::lock_guard<std::mutex> lock(mutex_);
    
    if (!initialized_) {
        _logDebug("Cannot create window: engine not initialized");
        return nullptr;
    }
    
    if (_shouldSimulateError()) {
        _logDebug("Simulated window creation error");
        return nullptr;
    }
    
    auto window = std::make_shared<MockWindow>(config);
    objectStats_.windows++;
    
    _logDebug("Created mock window: " + config.title);
    return window;
}

void MockRenderEngine::destroyWindow(std::shared_ptr<IWindow> window) {
    std::lock_guard<std::mutex> lock(mutex_);
    
    if (window && objectStats_.windows > 0) {
        objectStats_.windows--;
        _logDebug("Destroyed mock window");
    }
}

std::shared_ptr<ISceneNode> MockRenderEngine::createGroup() {
    std::lock_guard<std::mutex> lock(mutex_);
    
    if (!initialized_) {
        return nullptr;
    }
    
    auto node = std::make_shared<MockSceneNode>(NodeType::Group);
    objectStats_.sceneNodes++;
    
    _logDebug("Created mock group node");
    return node;
}

std::shared_ptr<ISceneNode> MockRenderEngine::createTransform() {
    std::lock_guard<std::mutex> lock(mutex_);
    
    if (!initialized_) {
        return nullptr;
    }
    
    auto node = std::make_shared<MockSceneNode>(NodeType::Transform);
    objectStats_.sceneNodes++;
    
    _logDebug("Created mock transform node");
    return node;
}

std::shared_ptr<ISceneNode> MockRenderEngine::createGeometry(const GeometryData& geometry) {
    std::lock_guard<std::mutex> lock(mutex_);
    
    if (!initialized_) {
        return nullptr;
    }
    
    auto node = std::make_shared<MockSceneNode>(NodeType::Geometry);
    // 这里可以存储几何数据
    objectStats_.sceneNodes++;
    
    _logDebug("Created mock geometry node with " + std::to_string(geometry.vertices.size()) + " vertices");
    return node;
}

std::shared_ptr<ISceneNode> MockRenderEngine::createStateGroup() {
    std::lock_guard<std::mutex> lock(mutex_);
    
    if (!initialized_) {
        return nullptr;
    }
    
    auto node = std::make_shared<MockSceneNode>(NodeType::StateGroup);
    objectStats_.sceneNodes++;
    
    _logDebug("Created mock state group node");
    return node;
}

std::shared_ptr<IRenderGraph> MockRenderEngine::createRenderGraph(std::shared_ptr<IWindow> window) {
    std::lock_guard<std::mutex> lock(mutex_);
    
    if (!initialized_ || !window) {
        return nullptr;
    }
    
    if (_shouldSimulateError()) {
        _logDebug("Simulated render graph creation error");
        return nullptr;
    }
    
    auto renderGraph = std::make_shared<MockRenderGraph>(window);
    objectStats_.renderGraphs++;
    
    _logDebug("Created mock render graph");
    return renderGraph;
}

bool MockRenderEngine::render(std::shared_ptr<IRenderGraph> renderGraph) {
    std::lock_guard<std::mutex> lock(mutex_);
    
    if (!initialized_ || !renderGraph) {
        return false;
    }
    
    auto startTime = std::chrono::high_resolution_clock::now();
    
    // 模拟渲染延迟
    _simulateRenderDelay();
    
    // 模拟渲染错误
    if (_shouldSimulateError()) {
        _logDebug("Simulated render error");
        return false;
    }
    
    // 更新统计信息
    _updateStatistics();
    
    auto endTime = std::chrono::high_resolution_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::microseconds>(endTime - startTime);
    statistics_.frameTime = duration.count() / 1000.0; // 转换为毫秒
    
    lastRenderTime_ = endTime;
    
    return true;
}

std::shared_ptr<ITexture> MockRenderEngine::createTexture(const TextureConfig& config) {
    std::lock_guard<std::mutex> lock(mutex_);
    
    if (!initialized_) {
        return nullptr;
    }
    
    auto texture = std::make_shared<MockTexture>(config);
    objectStats_.textures++;
    
    _logDebug("Created mock texture: " + std::to_string(config.width) + "x" + std::to_string(config.height));
    return texture;
}

std::shared_ptr<IBuffer> MockRenderEngine::createBuffer(const BufferConfig& config) {
    std::lock_guard<std::mutex> lock(mutex_);
    
    if (!initialized_) {
        return nullptr;
    }
    
    auto buffer = std::make_shared<MockBuffer>(config);
    objectStats_.buffers++;
    
    _logDebug("Created mock buffer: " + std::to_string(config.size) + " bytes");
    return buffer;
}

std::shared_ptr<IShader> MockRenderEngine::createShader(const ShaderConfig& config) {
    std::lock_guard<std::mutex> lock(mutex_);
    
    if (!initialized_) {
        return nullptr;
    }
    
    auto shader = std::make_shared<MockShader>(config);
    objectStats_.shaders++;
    
    _logDebug("Created mock shader: " + config.entryPoint);
    return shader;
}

std::shared_ptr<IPipeline> MockRenderEngine::createPipeline(const PipelineConfig& config) {
    std::lock_guard<std::mutex> lock(mutex_);
    
    if (!initialized_) {
        return nullptr;
    }
    
    auto pipeline = std::make_shared<MockPipeline>(config);
    objectStats_.pipelines++;
    
    _logDebug("Created mock pipeline with " + std::to_string(config.shaders.size()) + " shaders");
    return pipeline;
}

std::shared_ptr<ICamera> MockRenderEngine::createCamera(const CameraConfig& config) {
    std::lock_guard<std::mutex> lock(mutex_);
    
    if (!initialized_) {
        return nullptr;
    }
    
    auto camera = std::make_shared<MockCamera>(config);
    objectStats_.cameras++;
    
    _logDebug("Created mock camera with FOV: " + std::to_string(config.fov));
    return camera;
}

void MockRenderEngine::waitIdle() {
    std::lock_guard<std::mutex> lock(mutex_);
    
    if (!initialized_) {
        return;
    }
    
    // 模拟等待
    std::this_thread::sleep_for(std::chrono::milliseconds(1));
    _logDebug("Mock wait idle completed");
}

RenderStatistics MockRenderEngine::getRenderStatistics() const {
    std::lock_guard<std::mutex> lock(mutex_);
    return statistics_;
}

void MockRenderEngine::resetRenderStatistics() {
    std::lock_guard<std::mutex> lock(mutex_);
    
    statistics_ = {};
    _logDebug("Reset render statistics");
}

void MockRenderEngine::setDebugCallback(DebugCallback callback) {
    std::lock_guard<std::mutex> lock(mutex_);
    debugCallback_ = callback;
}

void MockRenderEngine::beginDebugMarker(const std::string& name) {
    std::lock_guard<std::mutex> lock(mutex_);
    
    debugMarkerStack_.push_back(name);
    _logDebug("Begin debug marker: " + name);
}

void MockRenderEngine::endDebugMarker() {
    std::lock_guard<std::mutex> lock(mutex_);
    
    if (!debugMarkerStack_.empty()) {
        std::string name = debugMarkerStack_.back();
        debugMarkerStack_.pop_back();
        _logDebug("End debug marker: " + name);
    }
}

void* MockRenderEngine::getNativeHandle() const {
    // 返回this指针作为原生句柄
    return const_cast<MockRenderEngine*>(this);
}

void MockRenderEngine::executeCustomCommand(std::function<void(void*)> command) {
    std::lock_guard<std::mutex> lock(mutex_);
    
    if (command) {
        command(this);
        _logDebug("Executed custom command");
    }
}

void MockRenderEngine::setRenderDelay(uint32_t delayMs) {
    std::lock_guard<std::mutex> lock(mutex_);
    renderDelay_ = delayMs;
}

void MockRenderEngine::setErrorRate(float errorRate) {
    std::lock_guard<std::mutex> lock(mutex_);
    errorRate_ = std::clamp(errorRate, 0.0f, 1.0f);
}

MockRenderEngine::ObjectStats MockRenderEngine::getObjectStats() const {
    std::lock_guard<std::mutex> lock(mutex_);
    return objectStats_;
}

void MockRenderEngine::_updateStatistics() {
    statistics_.frameCount++;
    statistics_.drawCalls += 10; // 模拟绘制调用
    statistics_.triangles += 1000; // 模拟三角形数量
    statistics_.vertices += 3000; // 模拟顶点数量
    statistics_.cpuTime = 2.0; // 模拟CPU时间
    statistics_.gpuTime = 8.0; // 模拟GPU时间
    statistics_.memoryUsed = objectStats_.textures * 1024 * 1024; // 模拟内存使用
}

bool MockRenderEngine::_shouldSimulateError() const {
    if (errorRate_ <= 0.0f) {
        return false;
    }
    
    static std::random_device rd;
    static std::mt19937 gen(rd());
    static std::uniform_real_distribution<float> dis(0.0f, 1.0f);
    
    return dis(gen) < errorRate_;
}

void MockRenderEngine::_simulateRenderDelay() const {
    if (renderDelay_ > 0) {
        std::this_thread::sleep_for(std::chrono::milliseconds(renderDelay_));
    }
}

void MockRenderEngine::_logDebug(const std::string& message) const {
    ROCKY_LOG_DEBUG("[MockRenderEngine] " << message);
    
    if (debugCallback_) {
        debugCallback_(message, 0); // 0 = debug level
    }
}
