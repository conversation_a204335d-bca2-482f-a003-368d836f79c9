/* <editor-fold desc="MIT License">

Copyright(c) 2024 VSG Render Engine Abstraction

Permission is hereby granted, free of charge, to any person obtaining a copy of this software and associated documentation files (the "Software"), to deal in the Software without restriction, including without limitation the rights to use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of the Software, and to permit persons to whom the Software is furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.

</editor-fold> */

/**
 * @file basic_vsg_abstraction.cpp
 * @brief VSG渲染引擎抽象层基础示例
 * 
 * 这个示例展示了如何使用VSG渲染引擎抽象层：
 * 1. 初始化渲染引擎管理器
 * 2. 创建和切换不同的渲染后端
 * 3. 构建场景图
 * 4. 演示VSG兼容性接口
 * 5. 展示渐进式迁移策略
 */

#include <vsg_abstraction/core/RenderEngineManager.h>
#include <vsg_abstraction/core/RenderEngineFactory.h>
#include <vsg_abstraction/nodes/INode.h>
#include <vsg_abstraction/core/Config.h>

#include <iostream>
#include <chrono>
#include <thread>

using namespace vsg_abstraction;

// 演示传统VSG代码的迁移
void demonstrateTraditionalVSGCode() {
    std::cout << "\n=== 演示传统VSG代码迁移 ===" << std::endl;
    
    // 传统VSG代码（注释掉，因为我们没有实际的VSG库）
    /*
    auto vsgGroup = vsg::Group::create();
    auto vsgTransform = vsg::MatrixTransform::create();
    auto vsgGeometry = vsg::Geometry::create();
    
    vsgTransform->addChild(vsgGeometry);
    vsgGroup->addChild(vsgTransform);
    */
    
    // 使用抽象层的等效代码
    auto& manager = getRenderManager();
    
    // 如果有VSG后端，使用VSG；否则使用Mock
    if (RenderEngineFactory::isBackendSupported(RenderBackend::Vulkan)) {
        std::cout << "使用VSG后端创建场景图..." << std::endl;
        manager.switchBackend(RenderBackend::Vulkan);
    } else {
        std::cout << "VSG不可用，使用Mock后端..." << std::endl;
        manager.switchBackend(RenderBackend::Mock);
    }
    
    // 使用抽象接口创建场景图
    auto group = manager.createGroup();
    auto transform = manager.createMatrixTransform();
    auto geometry = manager.createGeometry();
    
    group->setName("RootGroup");
    transform->setName("Transform");
    geometry->setName("Geometry");
    
    // 构建场景层次结构
    transform->addChild(geometry);
    group->addChild(transform);
    
    std::cout << "创建的场景图结构：" << std::endl;
    std::cout << "  " << group->getName() << " (子节点数: " << group->getNumChildren() << ")" << std::endl;
    std::cout << "    " << transform->getName() << " (子节点数: " << transform->getNumChildren() << ")" << std::endl;
    std::cout << "      " << geometry->getName() << std::endl;
    
    // 演示VSG兼容性接口
    std::cout << "\n使用VSG兼容性接口：" << std::endl;
    auto vsgGroup = manager.createVSGGroup();
    auto vsgTransform = manager.createVSGTransform();
    auto vsgGeometry = manager.createVSGGeometry();
    
    if (vsgGroup && vsgTransform && vsgGeometry) {
        std::cout << "成功创建VSG兼容对象" << std::endl;
        // 这些对象可以直接用于现有的VSG代码
    } else {
        std::cout << "VSG兼容对象创建失败（可能是Mock后端）" << std::endl;
    }
}

// 演示多后端支持
void demonstrateMultiBackendSupport() {
    std::cout << "\n=== 演示多后端支持 ===" << std::endl;
    
    auto& manager = getRenderManager();
    
    // 获取所有支持的后端
    auto supportedBackends = RenderEngineFactory::getSupportedBackends();
    std::cout << "支持的渲染后端：" << std::endl;
    for (auto backend : supportedBackends) {
        std::cout << "  - " << getRenderBackendName(backend) << std::endl;
    }
    
    // 获取推荐的后端
    auto recommendedBackend = RenderEngineFactory::getRecommendedBackend();
    std::cout << "\n推荐的后端: " << getRenderBackendName(recommendedBackend) << std::endl;
    
    // 尝试切换到不同的后端
    std::vector<RenderBackend> backendsToTry = {
        RenderBackend::Vulkan,
        RenderBackend::WebGPU,
        RenderBackend::OpenGL,
        RenderBackend::Mock
    };
    
    for (auto backend : backendsToTry) {
        if (RenderEngineFactory::isBackendSupported(backend)) {
            std::cout << "\n尝试切换到 " << getRenderBackendName(backend) << " 后端..." << std::endl;
            
            if (manager.switchBackend(backend)) {
                std::cout << "成功切换到 " << getRenderBackendName(backend) << std::endl;
                
                // 获取引擎信息
                auto engineInfo = manager.getEngineInfo();
                std::cout << "  引擎名称: " << engineInfo.name << std::endl;
                std::cout << "  引擎版本: " << engineInfo.version << std::endl;
                std::cout << "  供应商: " << engineInfo.vendor << std::endl;
                
                // 获取设备能力
                auto capabilities = manager.getDeviceCapabilities();
                std::cout << "  最大纹理尺寸: " << capabilities.maxTextureSize << std::endl;
                std::cout << "  支持几何着色器: " << (capabilities.supportsGeometryShader ? "是" : "否") << std::endl;
                
                // 创建一个简单的场景来测试后端
                auto testGroup = manager.createGroup();
                auto testTransform = manager.createTransform();
                testGroup->addChild(testTransform);
                
                std::cout << "  后端功能测试: 通过" << std::endl;
                
                // 模拟一些渲染操作
                std::this_thread::sleep_for(std::chrono::milliseconds(100));
                
            } else {
                std::cout << "切换到 " << getRenderBackendName(backend) << " 失败" << std::endl;
            }
        } else {
            std::cout << getRenderBackendName(backend) << " 后端不支持" << std::endl;
        }
    }
}

// 演示场景图操作
void demonstrateSceneGraphOperations() {
    std::cout << "\n=== 演示场景图操作 ===" << std::endl;
    
    auto& manager = getRenderManager();
    
    // 确保有可用的引擎
    if (!manager.hasEngine()) {
        manager.switchBackend(RenderBackend::Mock);
    }
    
    // 创建复杂的场景图
    auto root = manager.createGroup();
    root->setName("SceneRoot");
    
    // 创建多个变换节点
    for (int i = 0; i < 3; ++i) {
        auto transform = manager.createMatrixTransform();
        transform->setName("Transform_" + std::to_string(i));
        
        // 设置变换
        vec3 position = {static_cast<float>(i * 2), 0.0f, 0.0f};
        transform->setPosition(position);
        
        vec3 scale = {1.0f + i * 0.2f, 1.0f + i * 0.2f, 1.0f + i * 0.2f};
        transform->setScale(scale);
        
        // 为每个变换添加几何体
        auto geometry = manager.createGeometry();
        geometry->setName("Geometry_" + std::to_string(i));
        
        // 设置几何数据
        std::vector<vec3> vertices = {
            {-1.0f, -1.0f, 0.0f},
            { 1.0f, -1.0f, 0.0f},
            { 0.0f,  1.0f, 0.0f}
        };
        geometry->setVertices(vertices);
        
        std::vector<uint32_t> indices = {0, 1, 2};
        geometry->setIndices(indices);
        
        geometry->setPrimitiveTopology(PrimitiveTopology::TriangleList);
        
        transform->addChild(geometry);
        root->addChild(transform);
    }
    
    std::cout << "创建的场景图：" << std::endl;
    std::cout << "  " << root->getName() << " (子节点数: " << root->getNumChildren() << ")" << std::endl;
    
    for (size_t i = 0; i < root->getNumChildren(); ++i) {
        auto child = root->getChild(i);
        if (auto transform = std::dynamic_pointer_cast<ITransform>(child)) {
            auto pos = transform->getPosition();
            auto scale = transform->getScale();
            std::cout << "    " << child->getName() 
                      << " 位置: (" << pos[0] << ", " << pos[1] << ", " << pos[2] << ")"
                      << " 缩放: (" << scale[0] << ", " << scale[1] << ", " << scale[2] << ")"
                      << " (子节点数: " << transform->getNumChildren() << ")" << std::endl;
            
            for (size_t j = 0; j < transform->getNumChildren(); ++j) {
                auto grandChild = transform->getChild(j);
                if (auto geometry = std::dynamic_pointer_cast<IGeometry>(grandChild)) {
                    auto vertices = geometry->getVertices();
                    auto indices = geometry->getIndices();
                    std::cout << "      " << grandChild->getName() 
                              << " 顶点数: " << vertices.size()
                              << " 索引数: " << indices.size() << std::endl;
                }
            }
        }
    }
}

// 演示性能和统计信息
void demonstratePerformanceAndStatistics() {
    std::cout << "\n=== 演示性能和统计信息 ===" << std::endl;
    
    auto& manager = getRenderManager();
    
    // 确保有可用的引擎
    if (!manager.hasEngine()) {
        manager.switchBackend(RenderBackend::Mock);
    }
    
    // 重置统计信息
    manager.resetRenderStatistics();
    
    // 模拟一些渲染操作
    std::cout << "模拟渲染操作..." << std::endl;
    
    auto window = manager.createWindow();
    auto sceneRoot = manager.createGroup();
    auto renderGraph = manager.createRenderGraph(window, sceneRoot);
    
    // 模拟渲染循环
    for (int frame = 0; frame < 10; ++frame) {
        manager.beginProfileMarker("Frame_" + std::to_string(frame));
        
        // 模拟渲染工作
        std::this_thread::sleep_for(std::chrono::milliseconds(16)); // ~60 FPS
        
        manager.endProfileMarker();
        
        if (frame % 5 == 4) {
            auto stats = manager.getRenderStatistics();
            std::cout << "帧 " << frame + 1 << " 统计信息：" << std::endl;
            std::cout << "  帧数: " << stats.frameCount << std::endl;
            std::cout << "  绘制调用: " << stats.drawCalls << std::endl;
            std::cout << "  三角形数: " << stats.triangles << std::endl;
            std::cout << "  帧时间: " << stats.frameTime << "ms" << std::endl;
            std::cout << "  内存使用: " << (stats.memoryUsed / 1024) << "KB" << std::endl;
        }
    }
    
    // 等待渲染完成
    manager.waitIdle();
    
    // 获取最终统计信息
    auto finalStats = manager.getRenderStatistics();
    std::cout << "\n最终统计信息：" << std::endl;
    std::cout << "  总帧数: " << finalStats.frameCount << std::endl;
    std::cout << "  总绘制调用: " << finalStats.drawCalls << std::endl;
    std::cout << "  总三角形数: " << finalStats.triangles << std::endl;
    std::cout << "  平均帧时间: " << finalStats.frameTime << "ms" << std::endl;
    std::cout << "  总内存使用: " << (finalStats.memoryUsed / 1024) << "KB" << std::endl;
}

// 主函数
int main() {
    std::cout << "=== VSG渲染引擎抽象层基础示例 ===" << std::endl;
    std::cout << "版本: " << VERSION_STRING << std::endl;
    std::cout << "平台: " << PLATFORM_NAME << std::endl;
    std::cout << "编译器: " << COMPILER_NAME << std::endl;
    std::cout << std::endl;
    
    std::cout << "后端支持情况：" << std::endl;
    std::cout << "  VSG (Vulkan): " << (SUPPORT_VSG ? "是" : "否") << std::endl;
    std::cout << "  WebGPU: " << (SUPPORT_WEBGPU ? "是" : "否") << std::endl;
    std::cout << "  OpenGL: " << (SUPPORT_OPENGL ? "是" : "否") << std::endl;
    std::cout << "  Mock: " << (SUPPORT_MOCK ? "是" : "否") << std::endl;
    
    try {
        // 设置错误回调
        auto& manager = getRenderManager();
        manager.setErrorCallback([](const std::string& error) {
            std::cerr << "渲染错误: " << error << std::endl;
        });
        
        // 设置调试回调
        manager.setDebugCallback([](const std::string& message, int severity) {
            std::cout << "调试信息 [" << severity << "]: " << message << std::endl;
        });
        
        // 运行各种演示
        demonstrateTraditionalVSGCode();
        demonstrateMultiBackendSupport();
        demonstrateSceneGraphOperations();
        demonstratePerformanceAndStatistics();
        
        std::cout << "\n🎉 所有演示完成！" << std::endl;
        std::cout << "\n这个示例展示了VSG渲染引擎抽象层的主要功能：" << std::endl;
        std::cout << "1. 多后端支持和运行时切换" << std::endl;
        std::cout << "2. 与现有VSG代码的兼容性" << std::endl;
        std::cout << "3. 统一的场景图API" << std::endl;
        std::cout << "4. 性能监控和统计" << std::endl;
        std::cout << "5. 渐进式迁移支持" << std::endl;
        
        return 0;
    }
    catch (const std::exception& e) {
        std::cerr << "异常: " << e.what() << std::endl;
        return 1;
    }
    catch (...) {
        std::cerr << "未知异常" << std::endl;
        return 1;
    }
}
