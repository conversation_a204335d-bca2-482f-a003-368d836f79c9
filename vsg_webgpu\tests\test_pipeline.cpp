/* <editor-fold desc="MIT License">

Copyright(c) 2024 VSG WebGPU Extension

Permission is hereby granted, free of charge, to any person obtaining a copy of this software and associated documentation files (the "Software"), to deal in the Software without restriction, including without limitation the rights to use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of the Software, and to permit persons to whom the Software is furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.

</editor-fold> */

#include <vsg_webgpu/all.h>
#include <vsg/all.h>
#include <iostream>

using namespace vsg_webgpu;

class PipelineTest
{
public:
    bool runTests()
    {
        std::cout << "=== VSG WebGPU Pipeline Test ===" << std::endl;
        
        bool allPassed = true;
        
        allPassed &= testShaderStageCreation();
        allPassed &= testPipelineStateCreation();
        allPassed &= testGraphicsPipelineCreation();
        
        std::cout << "\n=== Test Results ===" << std::endl;
        std::cout << "Overall: " << (allPassed ? "PASSED" : "FAILED") << std::endl;
        
        return allPassed;
    }

private:
    bool testShaderStageCreation()
    {
        std::cout << "\n--- Test: Shader Stage Creation ---" << std::endl;
        
        try
        {
            std::string vertexShader = R"(
                @vertex
                fn vs_main(@location(0) position: vec3<f32>) -> @builtin(position) vec4<f32> {
                    return vec4<f32>(position, 1.0);
                }
            )";
            
            std::string fragmentShader = R"(
                @fragment
                fn fs_main() -> @location(0) vec4<f32> {
                    return vec4<f32>(1.0, 0.0, 0.0, 1.0);
                }
            )";
            
            auto vertexStage = ShaderStage::create(vertexShader, "vs_main");
            auto fragmentStage = ShaderStage::create(fragmentShader, "fs_main");
            
            if (!vertexStage || !fragmentStage)
            {
                std::cout << "FAILED: Could not create shader stages" << std::endl;
                return false;
            }
            
            std::cout << "PASSED: Shader stages created successfully" << std::endl;
            return true;
        }
        catch (const std::exception& e)
        {
            std::cout << "FAILED: Exception - " << e.what() << std::endl;
            return false;
        }
    }
    
    bool testPipelineStateCreation()
    {
        std::cout << "\n--- Test: Pipeline State Creation ---" << std::endl;
        
        try
        {
            auto vertexInputState = VertexInputState::create();
            auto primitiveState = PrimitiveState::create();
            auto depthStencilState = DepthStencilState::create();
            auto multisampleState = MultisampleState::create();
            auto colorBlendState = ColorBlendState::create();
            
            if (!vertexInputState || !primitiveState || !depthStencilState || 
                !multisampleState || !colorBlendState)
            {
                std::cout << "FAILED: Could not create pipeline states" << std::endl;
                return false;
            }
            
            std::cout << "PASSED: Pipeline states created successfully" << std::endl;
            return true;
        }
        catch (const std::exception& e)
        {
            std::cout << "FAILED: Exception - " << e.what() << std::endl;
            return false;
        }
    }
    
    bool testGraphicsPipelineCreation()
    {
        std::cout << "\n--- Test: Graphics Pipeline Creation ---" << std::endl;
        
        try
        {
            // 创建着色器阶段
            std::string vertexShader = R"(
                @vertex
                fn vs_main(@location(0) position: vec3<f32>) -> @builtin(position) vec4<f32> {
                    return vec4<f32>(position, 1.0);
                }
            )";
            
            std::string fragmentShader = R"(
                @fragment
                fn fs_main() -> @location(0) vec4<f32> {
                    return vec4<f32>(1.0, 0.0, 0.0, 1.0);
                }
            )";
            
            ShaderStages stages = {
                ShaderStage::create(vertexShader, "vs_main"),
                ShaderStage::create(fragmentShader, "fs_main")
            };
            
            // 创建管线状态
            GraphicsPipelineStates pipelineStates = {
                VertexInputState::create(),
                PrimitiveState::create(),
                DepthStencilState::create(),
                MultisampleState::create(),
                ColorBlendState::create()
            };
            
            // 创建图形管线
            auto pipeline = GraphicsPipeline::create(stages, pipelineStates);
            
            if (!pipeline)
            {
                std::cout << "FAILED: Could not create graphics pipeline" << std::endl;
                return false;
            }
            
            // 创建绑定命令
            auto bindPipeline = BindGraphicsPipeline::create(pipeline);
            
            if (!bindPipeline)
            {
                std::cout << "FAILED: Could not create bind pipeline command" << std::endl;
                return false;
            }
            
            std::cout << "PASSED: Graphics pipeline created successfully" << std::endl;
            return true;
        }
        catch (const std::exception& e)
        {
            std::cout << "FAILED: Exception - " << e.what() << std::endl;
            return false;
        }
    }
};

int main()
{
    try
    {
        initializeWebGPU();
        
        PipelineTest test;
        bool success = test.runTests();
        
        shutdownWebGPU();
        
        return success ? 0 : 1;
    }
    catch (const std::exception& e)
    {
        std::cerr << "Fatal error: " << e.what() << std::endl;
        return 1;
    }
}
