cmake_minimum_required(VERSION 3.20)

project(VSGRenderAbstraction
    VERSION 1.0.0
    DESCRIPTION "VSG Render Engine Abstraction Layer - Multi-backend Rendering Support"
    LANGUAGES CXX
)

# 设置项目信息
set(PROJECT_HOMEPAGE_URL "https://github.com/vsg-render-abstraction")
set(PROJECT_CONTACT "<EMAIL>")

# ========== 编译选项 ==========

set(CMAKE_CXX_STANDARD 20)
set(CMAKE_CXX_STANDARD_REQUIRED ON)
set(CMAKE_CXX_EXTENSIONS OFF)

# 设置输出目录
set(CMAKE_RUNTIME_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/bin)
set(CMAKE_LIBRARY_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/lib)
set(CMAKE_ARCHIVE_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/lib)

# Windows特定设置
if(WIN32)
    add_compile_options(/utf-8)
    add_compile_definitions(_CRT_SECURE_NO_WARNINGS)
    # 设置Windows SDK版本
    if(CMAKE_SYSTEM_VERSION)
        set(CMAKE_VS_WINDOWS_TARGET_PLATFORM_VERSION ${CMAKE_SYSTEM_VERSION})
    endif()
    # 设置运行时库
    set(CMAKE_MSVC_RUNTIME_LIBRARY "MultiThreaded$<$<CONFIG:Debug>:Debug>DLL")
endif()

# 设置默认构建类型
if(NOT CMAKE_BUILD_TYPE AND NOT CMAKE_CONFIGURATION_TYPES)
    message(STATUS "Setting build type to 'Release' as none was specified.")
    set(CMAKE_BUILD_TYPE Release CACHE STRING "Choose the type of build." FORCE)
    set_property(CACHE CMAKE_BUILD_TYPE PROPERTY STRINGS "Debug" "Release" "MinSizeRel" "RelWithDebInfo")
endif()

# ========== 构建选项 ==========

option(VSG_ABSTRACTION_BUILD_VSG_BACKEND "Build VSG backend (requires VSG)" ON)
option(VSG_ABSTRACTION_BUILD_WEBGPU_BACKEND "Build WebGPU backend" ON)
option(VSG_ABSTRACTION_BUILD_OPENGL_BACKEND "Build OpenGL backend" OFF)
option(VSG_ABSTRACTION_BUILD_MOCK_BACKEND "Build Mock backend for testing" ON)
option(VSG_ABSTRACTION_BUILD_TESTS "Build test programs" ON)
option(VSG_ABSTRACTION_BUILD_EXAMPLES "Build example programs" ON)
option(VSG_ABSTRACTION_BUILD_DOCS "Build documentation" OFF)
option(VSG_ABSTRACTION_BUILD_SHARED_LIBS "Build shared libraries" ON)

# ========== 依赖查找 ==========

# 查找VSG（用于VSG后端）
if(VSG_ABSTRACTION_BUILD_VSG_BACKEND)
    find_package(vsg QUIET)
    if(vsg_FOUND)
        message(STATUS "Found VSG: ${vsg_DIR}")
        message(STATUS "VSG Version: ${vsg_VERSION}")
        set(VSG_ABSTRACTION_HAS_VSG ON)
    else()
        message(WARNING "VSG not found, VSG backend will be disabled")
        set(VSG_ABSTRACTION_BUILD_VSG_BACKEND OFF)
        set(VSG_ABSTRACTION_HAS_VSG OFF)
    endif()
endif()

# 查找WebGPU（用于WebGPU后端）
if(VSG_ABSTRACTION_BUILD_WEBGPU_BACKEND)
    # 这里可以添加WebGPU的查找逻辑
    # find_package(webgpu QUIET)
    set(VSG_ABSTRACTION_HAS_WEBGPU ON) # 暂时设为ON，实际应该根据查找结果
endif()

# 查找OpenGL（用于OpenGL后端）
if(VSG_ABSTRACTION_BUILD_OPENGL_BACKEND)
    find_package(OpenGL QUIET)
    if(OpenGL_FOUND)
        set(VSG_ABSTRACTION_HAS_OPENGL ON)
    else()
        message(WARNING "OpenGL not found, OpenGL backend will be disabled")
        set(VSG_ABSTRACTION_BUILD_OPENGL_BACKEND OFF)
        set(VSG_ABSTRACTION_HAS_OPENGL OFF)
    endif()
endif()

# 查找线程库
find_package(Threads REQUIRED)

# ========== 配置头文件生成 ==========

configure_file(
    "${CMAKE_CURRENT_SOURCE_DIR}/include/vsg_abstraction/core/Config.h.in"
    "${CMAKE_CURRENT_BINARY_DIR}/include/vsg_abstraction/core/Config.h"
    @ONLY
)

# ========== 包含目录 ==========

include_directories(
    ${CMAKE_CURRENT_SOURCE_DIR}/include
    ${CMAKE_CURRENT_BINARY_DIR}/include
)

# ========== 导出宏定义 ==========

if(VSG_ABSTRACTION_BUILD_SHARED_LIBS)
    set(VSG_ABSTRACTION_LIBRARY_TYPE SHARED)
    add_compile_definitions(VSG_ABSTRACTION_SHARED_LIBRARY)
else()
    set(VSG_ABSTRACTION_LIBRARY_TYPE STATIC)
    add_compile_definitions(VSG_ABSTRACTION_STATIC_LIBRARY)
endif()

# ========== 子目录 ==========

# 核心库
add_subdirectory(src)

# 测试程序
if(VSG_ABSTRACTION_BUILD_TESTS)
    enable_testing()
    add_subdirectory(tests)
endif()

# 示例程序
if(VSG_ABSTRACTION_BUILD_EXAMPLES)
    add_subdirectory(examples)
endif()

# 文档
if(VSG_ABSTRACTION_BUILD_DOCS)
    add_subdirectory(docs)
endif()

# ========== 安装配置 ==========

# 安装头文件
install(DIRECTORY include/vsg_abstraction
    DESTINATION include
    FILES_MATCHING PATTERN "*.h"
)

# 安装生成的配置头文件
install(FILES "${CMAKE_CURRENT_BINARY_DIR}/include/vsg_abstraction/core/Config.h"
    DESTINATION include/vsg_abstraction/core
)

# 创建和安装包配置文件
include(CMakePackageConfigHelpers)

# 生成版本文件
write_basic_package_version_file(
    "${CMAKE_CURRENT_BINARY_DIR}/VSGAbstractionConfigVersion.cmake"
    VERSION ${PROJECT_VERSION}
    COMPATIBILITY SameMajorVersion
)

# 生成配置文件
configure_package_config_file(
    "${CMAKE_CURRENT_SOURCE_DIR}/cmake/VSGAbstractionConfig.cmake.in"
    "${CMAKE_CURRENT_BINARY_DIR}/VSGAbstractionConfig.cmake"
    INSTALL_DESTINATION lib/cmake/VSGAbstraction
)

# 安装配置文件
install(FILES
    "${CMAKE_CURRENT_BINARY_DIR}/VSGAbstractionConfig.cmake"
    "${CMAKE_CURRENT_BINARY_DIR}/VSGAbstractionConfigVersion.cmake"
    DESTINATION lib/cmake/VSGAbstraction
)

# ========== 打包配置 ==========

set(CPACK_PACKAGE_NAME "VSGRenderAbstraction")
set(CPACK_PACKAGE_VERSION ${PROJECT_VERSION})
set(CPACK_PACKAGE_DESCRIPTION_SUMMARY ${PROJECT_DESCRIPTION})
set(CPACK_PACKAGE_VENDOR "VSG Abstraction Project")
set(CPACK_PACKAGE_CONTACT "<EMAIL>")

if(WIN32)
    set(CPACK_GENERATOR "ZIP;NSIS")
    set(CPACK_NSIS_DISPLAY_NAME "VSG Render Abstraction")
    set(CPACK_NSIS_PACKAGE_NAME "VSGRenderAbstraction")
    set(CPACK_NSIS_CONTACT ${CPACK_PACKAGE_CONTACT})
elseif(APPLE)
    set(CPACK_GENERATOR "TGZ;DragNDrop")
else()
    set(CPACK_GENERATOR "TGZ;DEB;RPM")
    set(CPACK_DEBIAN_PACKAGE_MAINTAINER ${CPACK_PACKAGE_CONTACT})
    set(CPACK_RPM_PACKAGE_LICENSE "MIT")
endif()

include(CPack)

# ========== 构建信息输出 ==========

message(STATUS "")
message(STATUS "========== VSG Render Abstraction Configuration ==========")
message(STATUS "Version: ${PROJECT_VERSION}")
message(STATUS "Build Type: ${CMAKE_BUILD_TYPE}")
message(STATUS "C++ Standard: ${CMAKE_CXX_STANDARD}")
message(STATUS "Library Type: ${VSG_ABSTRACTION_LIBRARY_TYPE}")
message(STATUS "")
message(STATUS "Backend Support:")
message(STATUS "  VSG (Vulkan): ${VSG_ABSTRACTION_BUILD_VSG_BACKEND}")
if(VSG_ABSTRACTION_BUILD_VSG_BACKEND)
    message(STATUS "    VSG Found: ${VSG_ABSTRACTION_HAS_VSG}")
    if(VSG_ABSTRACTION_HAS_VSG)
        message(STATUS "    VSG Version: ${vsg_VERSION}")
    endif()
endif()
message(STATUS "  WebGPU: ${VSG_ABSTRACTION_BUILD_WEBGPU_BACKEND}")
message(STATUS "  OpenGL: ${VSG_ABSTRACTION_BUILD_OPENGL_BACKEND}")
message(STATUS "  Mock: ${VSG_ABSTRACTION_BUILD_MOCK_BACKEND}")
message(STATUS "")
message(STATUS "Build Options:")
message(STATUS "  Tests: ${VSG_ABSTRACTION_BUILD_TESTS}")
message(STATUS "  Examples: ${VSG_ABSTRACTION_BUILD_EXAMPLES}")
message(STATUS "  Documentation: ${VSG_ABSTRACTION_BUILD_DOCS}")
message(STATUS "  Shared Libraries: ${VSG_ABSTRACTION_BUILD_SHARED_LIBS}")
message(STATUS "")
message(STATUS "Install Prefix: ${CMAKE_INSTALL_PREFIX}")
message(STATUS "Runtime Output: ${CMAKE_RUNTIME_OUTPUT_DIRECTORY}")
message(STATUS "Library Output: ${CMAKE_LIBRARY_OUTPUT_DIRECTORY}")
message(STATUS "Archive Output: ${CMAKE_ARCHIVE_OUTPUT_DIRECTORY}")
message(STATUS "==========================================================")

# ========== 开发者工具 ==========

# 添加格式化目标（如果有clang-format）
find_program(CLANG_FORMAT_EXECUTABLE clang-format)
if(CLANG_FORMAT_EXECUTABLE)
    file(GLOB_RECURSE ALL_SOURCE_FILES
        ${CMAKE_CURRENT_SOURCE_DIR}/include/*.h
        ${CMAKE_CURRENT_SOURCE_DIR}/src/*.cpp
        ${CMAKE_CURRENT_SOURCE_DIR}/src/*.h
        ${CMAKE_CURRENT_SOURCE_DIR}/tests/*.cpp
        ${CMAKE_CURRENT_SOURCE_DIR}/examples/*.cpp
    )
    
    add_custom_target(format
        COMMAND ${CLANG_FORMAT_EXECUTABLE} -i ${ALL_SOURCE_FILES}
        COMMENT "Formatting source code with clang-format"
    )
endif()

# 添加静态分析目标（如果有clang-tidy）
find_program(CLANG_TIDY_EXECUTABLE clang-tidy)
if(CLANG_TIDY_EXECUTABLE)
    add_custom_target(tidy
        COMMAND ${CLANG_TIDY_EXECUTABLE} ${ALL_SOURCE_FILES} -- -I${CMAKE_CURRENT_SOURCE_DIR}/include
        COMMENT "Running static analysis with clang-tidy"
    )
endif()
