/* <editor-fold desc="MIT License">

Copyright(c) 2024 VSG WebGPU Extension

Permission is hereby granted, free of charge, to any person obtaining a copy of this software and associated documentation files (the "Software"), to deal in the Software without restriction, including without limitation the rights to use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of the Software, and to permit persons to whom the Software is furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.

</editor-fold> */

#include <vsg_webgpu/core/WebGPUHeaders.h>
#include <iostream>

namespace vsg_webgpu
{
    static ErrorCallback g_errorCallback = nullptr;

    void initializeWebGPU()
    {
        VSG_WEBGPU_LOG_INFO("Initializing WebGPU...");

#if VSG_WEBGPU_PLATFORM_EMSCRIPTEN
        // Emscripten WebGPU初始化
        VSG_WEBGPU_LOG_INFO("Using Emscripten WebGPU implementation");
#elif VSG_WEBGPU_USE_MOCK
        // Mock WebGPU初始化
        VSG_WEBGPU_LOG_WARN("Using Mock WebGPU implementation - no actual rendering will occur");
#else
        // Dawn WebGPU初始化
        VSG_WEBGPU_LOG_INFO("Using Dawn WebGPU implementation");
        
        // 设置Dawn的过程表
        DawnProcTable procs = dawn::native::GetProcs();
        dawnProcSetProcs(&procs);
#endif

        VSG_WEBGPU_LOG_INFO("WebGPU initialization complete");
    }

    void shutdownWebGPU()
    {
        VSG_WEBGPU_LOG_INFO("Shutting down WebGPU...");

#if VSG_WEBGPU_PLATFORM_EMSCRIPTEN
        // Emscripten WebGPU清理
#elif VSG_WEBGPU_USE_MOCK
        // Mock WebGPU清理
#else
        // Dawn WebGPU清理
#endif

        VSG_WEBGPU_LOG_INFO("WebGPU shutdown complete");
    }

    void setErrorCallback(ErrorCallback callback)
    {
        g_errorCallback = callback;
    }

    void reportError(const std::string& message)
    {
        VSG_WEBGPU_LOG_ERROR(message);
        
        if (g_errorCallback)
        {
            g_errorCallback(message);
        }
    }

} // namespace vsg_webgpu
