#pragma once

/* <editor-fold desc="MIT License">

Copyright(c) 2024 VSG Render Engine Abstraction

Permission is hereby granted, free of charge, to any person obtaining a copy of this software and associated documentation files (the "Software"), to deal in the Software without restriction, including without limitation the rights to use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of the Software, and to permit persons to whom the Software is furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.

</editor-fold> */

#include <vsg_abstraction/core/INode.h>
#include <vector>

namespace vsg_abstraction {

/**
 * @brief 组节点接口
 */
class VSG_ABSTRACTION_API IGroup : public INode {
public:
    virtual ~IGroup() = default;

    // 子节点管理
    virtual void addChild(ref_ptr<INode> child) = 0;
    virtual void removeChild(ref_ptr<INode> child) = 0;
    virtual void removeChild(size_t index) = 0;
    virtual void removeAllChildren() = 0;

    // 子节点访问
    virtual size_t getNumChildren() const = 0;
    virtual ref_ptr<INode> getChild(size_t index) const = 0;
    virtual std::vector<ref_ptr<INode>> getChildren() const = 0;

    // 查找
    virtual ref_ptr<INode> findChild(const std::string& name) const = 0;
};

} // namespace vsg_abstraction
