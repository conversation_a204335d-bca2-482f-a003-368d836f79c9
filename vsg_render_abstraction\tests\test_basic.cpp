/* <editor-fold desc="MIT License">

Copyright(c) 2024 VSG Render Engine Abstraction

Permission is hereby granted, free of charge, to any person obtaining a copy of this software and associated documentation files (the "Software"), to deal in the Software without restriction, including without limitation the rights to use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of the Software, and to permit persons to whom the Software is furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.

</editor-fold> */

/**
 * @file test_basic.cpp
 * @brief VSG渲染引擎抽象层基础功能测试
 */

#include <vsg_abstraction/core/RenderEngineManager.h>
#include <vsg_abstraction/core/RenderEngineFactory.h>
#include <vsg_abstraction/core/Config.h>

#include <iostream>
#include <cassert>
#include <chrono>

using namespace vsg_abstraction;

// 简单的测试框架
struct TestResult {
    std::string name;
    bool passed;
    std::string error;
    double duration;
};

std::vector<TestResult> results;

#define TEST(name) \
    void test_##name(); \
    void run_test_##name() { \
        auto start = std::chrono::high_resolution_clock::now(); \
        TestResult result; \
        result.name = #name; \
        result.passed = true; \
        try { \
            test_##name(); \
        } catch (const std::exception& e) { \
            result.passed = false; \
            result.error = e.what(); \
        } catch (...) { \
            result.passed = false; \
            result.error = "Unknown exception"; \
        } \
        auto end = std::chrono::high_resolution_clock::now(); \
        result.duration = std::chrono::duration<double, std::milli>(end - start).count(); \
        results.push_back(result); \
        std::cout << "[" << (result.passed ? "PASS" : "FAIL") << "] " \
                  << result.name << " (" << result.duration << "ms)"; \
        if (!result.passed) { \
            std::cout << " - " << result.error; \
        } \
        std::cout << std::endl; \
    } \
    void test_##name()

#define ASSERT(condition) \
    if (!(condition)) { \
        throw std::runtime_error("Assertion failed: " #condition); \
    }

#define ASSERT_EQ(a, b) \
    if ((a) != (b)) { \
        throw std::runtime_error("Assertion failed: " #a " == " #b); \
    }

#define ASSERT_NE(a, b) \
    if ((a) == (b)) { \
        throw std::runtime_error("Assertion failed: " #a " != " #b); \
    }

// ========== 基础功能测试 ==========

TEST(factory_supported_backends) {
    auto backends = RenderEngineFactory::getSupportedBackends();
    ASSERT(!backends.empty());
    
    // Mock后端应该总是支持的
    bool mockSupported = false;
    for (auto backend : backends) {
        if (backend == RenderBackend::Mock) {
            mockSupported = true;
            break;
        }
    }
    ASSERT(mockSupported);
}

TEST(factory_backend_support_check) {
    // Mock后端应该总是支持
    ASSERT(RenderEngineFactory::isBackendSupported(RenderBackend::Mock));
    
    // 检查其他后端的支持状态
    bool vsgSupported = RenderEngineFactory::isBackendSupported(RenderBackend::Vulkan);
    bool webgpuSupported = RenderEngineFactory::isBackendSupported(RenderBackend::WebGPU);
    bool openglSupported = RenderEngineFactory::isBackendSupported(RenderBackend::OpenGL);
    
    std::cout << "  VSG supported: " << (vsgSupported ? "Yes" : "No") << std::endl;
    std::cout << "  WebGPU supported: " << (webgpuSupported ? "Yes" : "No") << std::endl;
    std::cout << "  OpenGL supported: " << (openglSupported ? "Yes" : "No") << std::endl;
}

TEST(factory_recommended_backend) {
    auto recommended = RenderEngineFactory::getRecommendedBackend();
    ASSERT(RenderEngineFactory::isBackendSupported(recommended));
    
    std::cout << "  Recommended backend: " << getRenderBackendName(recommended) << std::endl;
}

TEST(factory_create_mock_engine) {
    auto engine = RenderEngineFactory::create(RenderBackend::Mock);
    ASSERT_NE(engine, nullptr);
    ASSERT_EQ(engine->getBackendType(), RenderBackend::Mock);
    
    // 测试初始化
    ASSERT(engine->initialize());
    ASSERT(engine->isInitialized());
    
    // 测试引擎信息
    auto info = engine->getEngineInfo();
    ASSERT(!info.name.empty());
    ASSERT(!info.version.empty());
    ASSERT_EQ(info.backend, RenderBackend::Mock);
    
    std::cout << "  Engine: " << info.name << " v" << info.version << std::endl;
}

TEST(manager_singleton) {
    auto& manager1 = getRenderManager();
    auto& manager2 = getRenderManager();
    
    // 应该是同一个实例
    ASSERT_EQ(&manager1, &manager2);
}

TEST(manager_backend_switching) {
    auto& manager = getRenderManager();
    
    // 切换到Mock后端
    ASSERT(manager.switchBackend(RenderBackend::Mock));
    ASSERT_EQ(manager.getCurrentBackend(), RenderBackend::Mock);
    ASSERT(manager.hasEngine());
    
    // 获取引擎信息
    auto info = manager.getEngineInfo();
    ASSERT_EQ(info.backend, RenderBackend::Mock);
}

TEST(manager_scene_graph_creation) {
    auto& manager = getRenderManager();
    
    // 确保有可用的引擎
    if (!manager.hasEngine()) {
        ASSERT(manager.switchBackend(RenderBackend::Mock));
    }
    
    // 创建场景图节点
    auto group = manager.createGroup();
    ASSERT_NE(group, nullptr);
    ASSERT_EQ(group->getNodeType(), NodeType::Group);
    
    auto transform = manager.createTransform();
    ASSERT_NE(transform, nullptr);
    ASSERT_EQ(transform->getNodeType(), NodeType::Transform);
    
    auto geometry = manager.createGeometry();
    ASSERT_NE(geometry, nullptr);
    ASSERT_EQ(geometry->getNodeType(), NodeType::Geometry);
    
    // 测试场景图层次结构
    transform->addChild(geometry);
    group->addChild(transform);
    
    ASSERT_EQ(group->getNumChildren(), 1);
    ASSERT_EQ(transform->getNumChildren(), 1);
    ASSERT_EQ(group->getChild(0), transform);
    ASSERT_EQ(transform->getChild(0), geometry);
}

TEST(node_properties) {
    auto& manager = getRenderManager();
    
    if (!manager.hasEngine()) {
        ASSERT(manager.switchBackend(RenderBackend::Mock));
    }
    
    auto group = manager.createGroup();
    ASSERT_NE(group, nullptr);
    
    // 测试名称
    group->setName("TestGroup");
    ASSERT_EQ(group->getName(), "TestGroup");
    
    // 测试用户数据
    int testData = 42;
    group->setUserData("test_key", &testData);
    ASSERT(group->hasUserData("test_key"));
    
    void* retrievedData = group->getUserData("test_key");
    ASSERT_EQ(retrievedData, &testData);
    ASSERT_EQ(*static_cast<int*>(retrievedData), 42);
    
    // 移除用户数据
    group->removeUserData("test_key");
    ASSERT(!group->hasUserData("test_key"));
    ASSERT_EQ(group->getUserData("test_key"), nullptr);
}

TEST(transform_operations) {
    auto& manager = getRenderManager();
    
    if (!manager.hasEngine()) {
        ASSERT(manager.switchBackend(RenderBackend::Mock));
    }
    
    auto transform = manager.createTransform();
    ASSERT_NE(transform, nullptr);
    
    // 测试位置
    vec3 position = {1.0f, 2.0f, 3.0f};
    transform->setPosition(position);
    auto retrievedPos = transform->getPosition();
    ASSERT_EQ(retrievedPos[0], position[0]);
    ASSERT_EQ(retrievedPos[1], position[1]);
    ASSERT_EQ(retrievedPos[2], position[2]);
    
    // 测试缩放
    vec3 scale = {2.0f, 2.0f, 2.0f};
    transform->setScale(scale);
    auto retrievedScale = transform->getScale();
    ASSERT_EQ(retrievedScale[0], scale[0]);
    ASSERT_EQ(retrievedScale[1], scale[1]);
    ASSERT_EQ(retrievedScale[2], scale[2]);
    
    // 测试旋转
    vec4 rotation = {0.0f, 0.0f, 0.0f, 1.0f};
    transform->setRotation(rotation);
    auto retrievedRot = transform->getRotation();
    ASSERT_EQ(retrievedRot[0], rotation[0]);
    ASSERT_EQ(retrievedRot[1], rotation[1]);
    ASSERT_EQ(retrievedRot[2], rotation[2]);
    ASSERT_EQ(retrievedRot[3], rotation[3]);
}

TEST(geometry_data) {
    auto& manager = getRenderManager();
    
    if (!manager.hasEngine()) {
        ASSERT(manager.switchBackend(RenderBackend::Mock));
    }
    
    auto geometry = manager.createGeometry();
    ASSERT_NE(geometry, nullptr);
    
    // 测试顶点数据
    std::vector<vec3> vertices = {
        {-1.0f, -1.0f, 0.0f},
        { 1.0f, -1.0f, 0.0f},
        { 0.0f,  1.0f, 0.0f}
    };
    geometry->setVertices(vertices);
    
    auto retrievedVertices = geometry->getVertices();
    ASSERT_EQ(retrievedVertices.size(), vertices.size());
    for (size_t i = 0; i < vertices.size(); ++i) {
        ASSERT_EQ(retrievedVertices[i][0], vertices[i][0]);
        ASSERT_EQ(retrievedVertices[i][1], vertices[i][1]);
        ASSERT_EQ(retrievedVertices[i][2], vertices[i][2]);
    }
    
    // 测试索引数据
    std::vector<uint32_t> indices = {0, 1, 2};
    geometry->setIndices(indices);
    
    auto retrievedIndices = geometry->getIndices();
    ASSERT_EQ(retrievedIndices.size(), indices.size());
    for (size_t i = 0; i < indices.size(); ++i) {
        ASSERT_EQ(retrievedIndices[i], indices[i]);
    }
    
    // 测试图元拓扑
    geometry->setPrimitiveTopology(PrimitiveTopology::TriangleList);
    ASSERT_EQ(geometry->getPrimitiveTopology(), PrimitiveTopology::TriangleList);
}

TEST(statistics) {
    auto& manager = getRenderManager();
    
    if (!manager.hasEngine()) {
        ASSERT(manager.switchBackend(RenderBackend::Mock));
    }
    
    // 重置统计信息
    manager.resetRenderStatistics();
    
    auto stats = manager.getRenderStatistics();
    ASSERT_EQ(stats.frameCount, 0);
    
    // 创建一些对象来生成统计数据
    for (int i = 0; i < 5; ++i) {
        auto group = manager.createGroup();
        auto transform = manager.createTransform();
        auto geometry = manager.createGeometry();
        
        transform->addChild(geometry);
        group->addChild(transform);
    }
    
    // 获取更新后的统计信息
    auto newStats = manager.getRenderStatistics();
    // Mock引擎应该会更新一些统计信息
    ASSERT(newStats.frameCount >= stats.frameCount);
}

// ========== 主函数 ==========

int main() {
    std::cout << "=== VSG渲染引擎抽象层基础功能测试 ===" << std::endl;
    std::cout << "版本: " << VERSION_STRING << std::endl;
    std::cout << "平台: " << PLATFORM_NAME << std::endl;
    std::cout << "编译器: " << COMPILER_NAME << std::endl;
    std::cout << std::endl;
    
    // 运行所有测试
    run_test_factory_supported_backends();
    run_test_factory_backend_support_check();
    run_test_factory_recommended_backend();
    run_test_factory_create_mock_engine();
    run_test_manager_singleton();
    run_test_manager_backend_switching();
    run_test_manager_scene_graph_creation();
    run_test_node_properties();
    run_test_transform_operations();
    run_test_geometry_data();
    run_test_statistics();
    
    // 统计结果
    int passed = 0;
    int failed = 0;
    double totalTime = 0.0;
    
    for (const auto& result : results) {
        if (result.passed) {
            passed++;
        } else {
            failed++;
        }
        totalTime += result.duration;
    }
    
    std::cout << std::endl;
    std::cout << "=== 测试结果 ===" << std::endl;
    std::cout << "通过: " << passed << std::endl;
    std::cout << "失败: " << failed << std::endl;
    std::cout << "总计: " << (passed + failed) << std::endl;
    std::cout << "总时间: " << totalTime << "ms" << std::endl;
    
    if (failed > 0) {
        std::cout << std::endl << "失败的测试:" << std::endl;
        for (const auto& result : results) {
            if (!result.passed) {
                std::cout << "  " << result.name << ": " << result.error << std::endl;
            }
        }
        return 1;
    }
    
    std::cout << std::endl << "🎉 所有测试通过！" << std::endl;
    return 0;
}
