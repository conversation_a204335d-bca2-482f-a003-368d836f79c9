/* <editor-fold desc="MIT License">

Copyright(c) 2024 VSG WebGPU Extension

Permission is hereby granted, free of charge, to any person obtaining a copy of this software and associated documentation files (the "Software"), to deal in the Software without restriction, including without limitation the rights to use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of the Software, and to permit persons to whom the Software is furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.

</editor-fold> */

#include <vsg_webgpu/vk/Context.h>
#include <vsg_webgpu/vk/Device.h>
#include <vsg_webgpu/vk/CommandPool.h>
#include <vsg_webgpu/vk/Queue.h>

using namespace vsg_webgpu;

Context::Context(Device* device, CommandPool* commandPool) :
    deviceID(device ? device->deviceID : 0),
    device(device),
    commandPool(commandPool)
{
    VSG_WEBGPU_LOG_DEBUG("Context::Context()");
    
    if (device)
    {
        graphicsQueue = device->getQueue().get();
    }
}

Context::~Context()
{
    VSG_WEBGPU_LOG_DEBUG("Context::~Context()");
}

bool Context::isCompiled(vsg::ref_ptr<vsg::Object> object) const
{
    if (!object) return false;
    
    auto it = _compiledObjects.find(object.get());
    return it != _compiledObjects.end() && it->second;
}

void Context::markAsCompiled(vsg::ref_ptr<vsg::Object> object)
{
    if (object)
    {
        _compiledObjects[object.get()] = true;
    }
}

void Context::markAsNotCompiled(vsg::ref_ptr<vsg::Object> object)
{
    if (object)
    {
        _compiledObjects[object.get()] = false;
    }
}

WGPUBuffer Context::createBuffer(const WGPUBufferDescriptor& descriptor, const void* data)
{
#if !VSG_WEBGPU_USE_MOCK
    if (!device) return {};
    
    auto buffer = device->getDevice().CreateBuffer(&descriptor);
    
    if (data && buffer)
    {
        writeBuffer(buffer, 0, data, descriptor.size);
    }
    
    _updateStatistics("buffer", descriptor.size);
    return buffer;
#else
    _updateStatistics("buffer", descriptor.size);
    return {};
#endif
}

WGPUTexture Context::createTexture(const WGPUTextureDescriptor& descriptor)
{
#if !VSG_WEBGPU_USE_MOCK
    if (!device) return {};
    
    auto texture = device->getDevice().CreateTexture(&descriptor);
    _updateStatistics("texture");
    return texture;
#else
    _updateStatistics("texture");
    return {};
#endif
}

WGPUSampler Context::createSampler(const WGPUSamplerDescriptor& descriptor)
{
#if !VSG_WEBGPU_USE_MOCK
    if (!device) return {};
    
    auto sampler = device->getDevice().CreateSampler(&descriptor);
    _updateStatistics("sampler");
    return sampler;
#else
    _updateStatistics("sampler");
    return {};
#endif
}

WGPUShaderModule Context::createShaderModule(const std::string& wgslCode, const std::string& label)
{
#if !VSG_WEBGPU_USE_MOCK
    if (!device) return {};
    
    WGPUShaderModuleWGSLDescriptor wgslDesc = {};
    wgslDesc.chain.sType = WGPUSType_ShaderModuleWGSLDescriptor;
    wgslDesc.code = wgslCode.c_str();
    
    WGPUShaderModuleDescriptor desc = {};
    desc.label = label.empty() ? nullptr : label.c_str();
    desc.nextInChain = &wgslDesc.chain;
    
    auto shaderModule = device->getDevice().CreateShaderModule(&desc);
    _updateStatistics("shader");
    return shaderModule;
#else
    _updateStatistics("shader");
    return {};
#endif
}

WGPUBindGroupLayout Context::createBindGroupLayout(const WGPUBindGroupLayoutDescriptor& descriptor)
{
#if !VSG_WEBGPU_USE_MOCK
    if (!device) return {};
    
    auto layout = device->getDevice().CreateBindGroupLayout(&descriptor);
    _updateStatistics("bind_group_layout");
    return layout;
#else
    _updateStatistics("bind_group_layout");
    return {};
#endif
}

WGPUBindGroup Context::createBindGroup(const WGPUBindGroupDescriptor& descriptor)
{
#if !VSG_WEBGPU_USE_MOCK
    if (!device) return {};
    
    auto bindGroup = device->getDevice().CreateBindGroup(&descriptor);
    _updateStatistics("bind_group");
    return bindGroup;
#else
    _updateStatistics("bind_group");
    return {};
#endif
}

WGPUPipelineLayout Context::createPipelineLayout(const WGPUPipelineLayoutDescriptor& descriptor)
{
#if !VSG_WEBGPU_USE_MOCK
    if (!device) return {};
    
    auto layout = device->getDevice().CreatePipelineLayout(&descriptor);
    _updateStatistics("pipeline_layout");
    return layout;
#else
    _updateStatistics("pipeline_layout");
    return {};
#endif
}

WGPURenderPipeline Context::createRenderPipeline(const WGPURenderPipelineDescriptor& descriptor)
{
#if !VSG_WEBGPU_USE_MOCK
    if (!device) return {};
    
    auto pipeline = device->getDevice().CreateRenderPipeline(&descriptor);
    _updateStatistics("render_pipeline");
    return pipeline;
#else
    _updateStatistics("render_pipeline");
    return {};
#endif
}

WGPUComputePipeline Context::createComputePipeline(const WGPUComputePipelineDescriptor& descriptor)
{
#if !VSG_WEBGPU_USE_MOCK
    if (!device) return {};
    
    auto pipeline = device->getDevice().CreateComputePipeline(&descriptor);
    _updateStatistics("compute_pipeline");
    return pipeline;
#else
    _updateStatistics("compute_pipeline");
    return {};
#endif
}

void Context::writeBuffer(WGPUBuffer buffer, uint64_t offset, const void* data, size_t size)
{
#if !VSG_WEBGPU_USE_MOCK
    if (graphicsQueue && buffer && data)
    {
        graphicsQueue->writeBuffer(buffer, offset, data, size);
    }
#endif
}

void Context::writeTexture(WGPUTexture texture, const void* data, size_t dataSize, 
                          uint32_t width, uint32_t height, uint32_t depth)
{
#if !VSG_WEBGPU_USE_MOCK
    if (graphicsQueue && texture && data)
    {
        WGPUImageCopyTexture destination = {};
        destination.texture = texture;
        destination.mipLevel = 0;
        destination.origin = {0, 0, 0};
        destination.aspect = WGPUTextureAspect_All;
        
        WGPUTextureDataLayout dataLayout = {};
        dataLayout.offset = 0;
        dataLayout.bytesPerRow = width * 4; // 假设RGBA格式
        dataLayout.rowsPerImage = height;
        
        WGPUExtent3D writeSize = {width, height, depth};
        
        graphicsQueue->writeTexture(destination, data, dataSize, dataLayout, writeSize);
    }
#endif
}

void Context::record()
{
    // 在WebGPU中，记录是通过命令编码器完成的
    // 这里主要是为了兼容VSG接口
    VSG_WEBGPU_LOG_DEBUG("Context::record()");
}

void Context::waitForCompletion()
{
    // WebGPU没有直接的等待完成方法
    // 这里主要是为了兼容VSG接口
    VSG_WEBGPU_LOG_DEBUG("Context::waitForCompletion()");
}

void Context::_updateStatistics(const std::string& resourceType, size_t size)
{
    if (resourceType == "buffer")
    {
        _statistics.numBuffersCreated++;
        _statistics.totalMemoryAllocated += size;
    }
    else if (resourceType == "texture")
    {
        _statistics.numTexturesCreated++;
    }
    else if (resourceType == "render_pipeline" || resourceType == "compute_pipeline")
    {
        _statistics.numPipelinesCreated++;
    }
    else if (resourceType == "bind_group")
    {
        _statistics.numBindGroupsCreated++;
    }
}
