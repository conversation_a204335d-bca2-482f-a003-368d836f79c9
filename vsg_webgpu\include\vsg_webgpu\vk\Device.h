#pragma once

/* <editor-fold desc="MIT License">

Copyright(c) 2024 VSG WebGPU Extension

Permission is hereby granted, free of charge, to any person obtaining a copy of this software and associated documentation files (the "Software"), to deal in the Software without restriction, including without limitation the rights to use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of the Software, and to permit persons to whom the Software is furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.

</editor-fold> */

#include <functional>
#include <mutex>
#include <unordered_map>
#include <vector>
#include <vsg/core/Inherit.h>
#include <vsg/core/Object.h>
#include <vsg/core/ref_ptr.h>
#include <vsg/vk/DeviceFeatures.h>
#include <vsg_webgpu/core/Export.h>
#include <vsg_webgpu/core/WebGPUHeaders.h>

namespace vsg_webgpu
{
    // forward declarations
    class Queue;
    class CommandPool;
    class Context;

    /// WebGPU设备管理，对应VSG的Device类
    /// 提供与VSG Device相同的接口，但底层使用WebGPU实现
    class VSG_WEBGPU_DECLSPEC Device : public vsg::Inherit<vsg::Object, Device>
    {
    public:
        Device();
        virtual ~Device();

        // 初始化WebGPU设备
        bool initialize(const vsg::DeviceFeatures* deviceFeatures = nullptr);
        bool initialize(WGPUSurface surface, const vsg::DeviceFeatures* deviceFeatures = nullptr);

        // WebGPU对象访问
        WGPUDevice getDevice() const { return _device; }
        WGPUInstance getInstance() const { return _instance; }
        WGPUAdapter getAdapter() const { return _adapter; }

        // 队列管理 - 对应VSG的Queue系统
        vsg::ref_ptr<Queue> getQueue(uint32_t queueFamilyIndex = 0, uint32_t queueIndex = 0);
        const std::vector<vsg::ref_ptr<Queue>>& getQueues() const { return _queues; }

        // 交换链管理
        WGPUSwapChain getSwapChain() const { return _swapChain; }
        WGPUTextureFormat getSwapChainFormat() const { return _swapChainFormat; }
        void resizeSwapChain(uint32_t width, uint32_t height);

        // 当前帧资源
        WGPUTextureView getCurrentTextureView();
        WGPUCommandEncoder createCommandEncoder();
        void submitCommandBuffer(WGPUCommandBuffer commands);

        // 深度缓冲管理
        void createDepthBuffer(uint32_t width, uint32_t height);
        WGPUTextureView getDepthTextureView() const { return _depthTextureView; }

        // 设备ID管理 - 对应VSG的deviceID系统
        static uint32_t maxNumDevices() { return 1; } // WebGPU通常只有一个设备
        const uint32_t deviceID = 0;

        // 命令池管理 - 对应VSG的CommandPool系统
        vsg::ref_ptr<CommandPool> getCommandPool(uint32_t queueFamilyIndex = 0);

        // 资源缓存系统
        class VSG_WEBGPU_DECLSPEC ResourceCache
        {
        public:
            ResourceCache(Device* device);

            // 渲染管线缓存
            WGPURenderPipeline getOrCreateRenderPipeline(size_t hash,
                                                         const std::function<WGPURenderPipeline()>& creator);

            // 计算管线缓存
            WGPUComputePipeline getOrCreateComputePipeline(size_t hash,
                                                           const std::function<WGPUComputePipeline()>& creator);

            // 绑定组缓存
            WGPUBindGroup getOrCreateBindGroup(size_t hash,
                                               const std::function<WGPUBindGroup()>& creator);

            // 缓冲区缓存
            WGPUBuffer getOrCreateBuffer(const std::string& key,
                                         const WGPUBufferDescriptor& desc,
                                         const std::function<void(WGPUBuffer)>& initializer = nullptr);

            // 纹理缓存
            WGPUTexture getOrCreateTexture(const std::string& key,
                                           const WGPUTextureDescriptor& desc);

            // 采样器缓存
            WGPUSampler getOrCreateSampler(size_t hash,
                                           const WGPUSamplerDescriptor& desc);

            // 着色器模块缓存
            WGPUShaderModule getOrCreateShaderModule(const std::string& key,
                                                     const std::string& wgslCode);

            // 清理资源
            void clear();

        private:
            Device* _device;
            std::mutex _mutex;
            std::unordered_map<size_t, WGPURenderPipeline> _renderPipelines;
            std::unordered_map<size_t, WGPUComputePipeline> _computePipelines;
            std::unordered_map<size_t, WGPUBindGroup> _bindGroups;
            std::unordered_map<std::string, WGPUBuffer> _buffers;
            std::unordered_map<std::string, WGPUTexture> _textures;
            std::unordered_map<size_t, WGPUSampler> _samplers;
            std::unordered_map<std::string, WGPUShaderModule> _shaderModules;
        };

        ResourceCache& getResourceCache() { return _resourceCache; }

        // 错误处理和调试
        void setErrorCallback(const std::function<void(const std::string&)>& callback);
        void logError(const std::string& message);

        // 功能查询 - 对应VSG的功能查询系统
        bool supportsFeature(const std::string& feature) const;
        const std::vector<std::string>& getSupportedFeatures() const { return _supportedFeatures; }

        // 等待设备空闲
        void waitIdle();

    private:
        // WebGPU核心对象
        WGPUInstance _instance;
        WGPUAdapter _adapter;
        WGPUDevice _device;
        WGPUSurface _surface;
        WGPUSwapChain _swapChain;
        WGPUTextureFormat _swapChainFormat;

        // 深度缓冲
        WGPUTexture _depthTexture;
        WGPUTextureView _depthTextureView;

        // VSG兼容的管理对象
        std::vector<vsg::ref_ptr<Queue>> _queues;
        std::vector<vsg::ref_ptr<CommandPool>> _commandPools;

        // 资源缓存
        ResourceCache _resourceCache;

        // 功能支持
        std::vector<std::string> _supportedFeatures;

        // 错误处理
        std::function<void(const std::string&)> _errorCallback;

        // 内部初始化方法
        bool _initializeInstance();
        bool _requestAdapter();
        bool _requestDevice(const vsg::DeviceFeatures* deviceFeatures);
        void _setupFeatures();
        void _setupSwapChain();

        // 错误回调
        static void _deviceErrorCallback(int type, const char* message, void* userdata);
        static void _deviceLostCallback(int reason, const char* message, void* userdata);
    };

    VSG_type_name(vsg_webgpu::Device);

} // namespace vsg_webgpu
