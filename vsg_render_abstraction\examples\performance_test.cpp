/* <editor-fold desc="MIT License">

Copyright(c) 2024 VSG Render Engine Abstraction

Permission is hereby granted, free of charge, to any person obtaining a copy of this software and associated documentation files (the "Software"), to deal in the Software without restriction, including without limitation the rights to use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of the Software, and to permit persons to whom the Software is furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.

</editor-fold> */

#include <vsg_abstraction/core/RenderEngineManager.h>
#include <vsg_abstraction/core/RenderEngineFactory.h>
#include <vsg_abstraction/core/Config.h>
#include <iostream>
#include <chrono>
#include <vector>

using namespace vsg_abstraction;

class PerformanceBenchmark {
public:
    void runBenchmarks() {
        std::cout << "=== Performance Benchmark ===" << std::endl;
        std::cout << "Version: " << VERSION_STRING << std::endl;
        std::cout << "Platform: " << PLATFORM_NAME << std::endl;
        std::cout << std::endl;
        
        auto& manager = getRenderManager();
        auto backends = RenderEngineFactory::getSupportedBackends();
        
        for (auto backend : backends) {
            if (manager.switchBackend(backend)) {
                std::cout << "Benchmarking: " << getRenderBackendName(backend) << std::endl;
                runBackendBenchmark(manager);
                std::cout << std::endl;
            }
        }
    }

private:
    void runBackendBenchmark(RenderEngineManager& manager) {
        // Object creation benchmark
        benchmarkObjectCreation(manager);
        
        // Scene graph construction benchmark
        benchmarkSceneGraphConstruction(manager);
        
        // Memory usage benchmark
        benchmarkMemoryUsage(manager);
    }
    
    void benchmarkObjectCreation(RenderEngineManager& manager) {
        const int numObjects = 1000;
        
        auto startTime = std::chrono::high_resolution_clock::now();
        
        std::vector<ref_ptr<IGroup>> groups;
        std::vector<ref_ptr<ITransform>> transforms;
        std::vector<ref_ptr<IGeometry>> geometries;
        
        groups.reserve(numObjects);
        transforms.reserve(numObjects);
        geometries.reserve(numObjects);
        
        for (int i = 0; i < numObjects; ++i) {
            groups.push_back(manager.createGroup());
            transforms.push_back(manager.createTransform());
            geometries.push_back(manager.createGeometry());
        }
        
        auto endTime = std::chrono::high_resolution_clock::now();
        auto duration = std::chrono::duration<double, std::milli>(endTime - startTime).count();
        
        std::cout << "  Object Creation: " << numObjects * 3 << " objects in " 
                  << duration << "ms (" << (duration / (numObjects * 3)) << "ms per object)" << std::endl;
    }
    
    void benchmarkSceneGraphConstruction(RenderEngineManager& manager) {
        const int numNodes = 100;
        
        auto startTime = std::chrono::high_resolution_clock::now();
        
        auto root = manager.createGroup();
        
        for (int i = 0; i < numNodes; ++i) {
            auto group = manager.createGroup();
            auto transform = manager.createTransform();
            auto geometry = manager.createGeometry();
            
            if (group && transform && geometry) {
                transform->addChild(geometry);
                group->addChild(transform);
                root->addChild(group);
            }
        }
        
        auto endTime = std::chrono::high_resolution_clock::now();
        auto duration = std::chrono::duration<double, std::milli>(endTime - startTime).count();
        
        std::cout << "  Scene Graph Construction: " << numNodes << " hierarchies in " 
                  << duration << "ms (" << (duration / numNodes) << "ms per hierarchy)" << std::endl;
    }
    
    void benchmarkMemoryUsage(RenderEngineManager& manager) {
        auto initialStats = manager.getRenderStatistics();
        
        // Create many objects
        std::vector<ref_ptr<IGroup>> objects;
        for (int i = 0; i < 500; ++i) {
            objects.push_back(manager.createGroup());
        }
        
        auto finalStats = manager.getRenderStatistics();
        
        std::cout << "  Memory Usage: " << (finalStats.memoryUsed / 1024) << "KB total, "
                  << ((finalStats.memoryUsed - initialStats.memoryUsed) / 1024) << "KB for 500 objects" << std::endl;
    }
};

int main() {
    try {
        PerformanceBenchmark benchmark;
        benchmark.runBenchmarks();
        
        std::cout << "🎉 Performance benchmark completed successfully!" << std::endl;
        return 0;
        
    } catch (const std::exception& e) {
        std::cerr << "Error: " << e.what() << std::endl;
        return 1;
    } catch (...) {
        std::cerr << "Unknown error occurred" << std::endl;
        return 1;
    }
}
