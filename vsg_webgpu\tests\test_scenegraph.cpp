/* <editor-fold desc="MIT License">

Copyright(c) 2024 VSG WebGPU Extension

Permission is hereby granted, free of charge, to any person obtaining a copy of this software and associated documentation files (the "Software"), to deal in the Software without restriction, including without limitation the rights to use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of the Software, and to permit persons to whom the Software is furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.

</editor-fold> */

#include <vsg_webgpu/all.h>
#include <vsg/all.h>
#include <iostream>

using namespace vsg_webgpu;

class SceneGraphTest
{
public:
    bool runTests()
    {
        std::cout << "=== VSG WebGPU Scene Graph Test ===" << std::endl;
        
        bool allPassed = true;
        
        allPassed &= testSceneGraphCreation();
        allPassed &= testRecordTraversal();
        allPassed &= testRenderGraph();
        
        std::cout << "\n=== Test Results ===" << std::endl;
        std::cout << "Overall: " << (allPassed ? "PASSED" : "FAILED") << std::endl;
        
        return allPassed;
    }

private:
    bool testSceneGraphCreation()
    {
        std::cout << "\n--- Test: Scene Graph Creation ---" << std::endl;
        
        try
        {
            // 创建根节点
            auto root = vsg::Group::create();
            
            // 创建几何体
            auto vertices = vsg::vec3Array::create({
                {0.0f, 0.5f, 0.0f},
                {-0.5f, -0.5f, 0.0f},
                {0.5f, -0.5f, 0.0f}
            });
            
            auto indices = vsg::ushortArray::create({0, 1, 2});
            
            auto geometry = vsg::Geometry::create();
            geometry->arrays = {vertices};
            geometry->indices = indices;
            geometry->commands = {vsg::DrawIndexed::create(3, 1, 0, 0, 0)};
            
            // 创建状态组
            auto stateGroup = vsg::StateGroup::create();
            stateGroup->addChild(geometry);
            
            // 创建变换节点
            auto transform = vsg::MatrixTransform::create();
            transform->addChild(stateGroup);
            
            root->addChild(transform);
            
            if (root->children.empty())
            {
                std::cout << "FAILED: Scene graph is empty" << std::endl;
                return false;
            }
            
            std::cout << "PASSED: Scene graph created with " << root->children.size() << " children" << std::endl;
            return true;
        }
        catch (const std::exception& e)
        {
            std::cout << "FAILED: Exception - " << e.what() << std::endl;
            return false;
        }
    }
    
    bool testRecordTraversal()
    {
        std::cout << "\n--- Test: Record Traversal ---" << std::endl;
        
        try
        {
            initializeWebGPU();
            
            auto device = Device::create();
            if (!device || !device->initialize())
            {
#if VSG_WEBGPU_USE_MOCK
                std::cout << "Mock mode: Creating record traversal" << std::endl;
#else
                std::cout << "FAILED: Could not initialize device" << std::endl;
                return false;
#endif
            }
            
            auto commandPool = device->getCommandPool();
            auto commandBuffer = commandPool ? commandPool->allocate() : nullptr;
            
            auto recordTraversal = RecordTraversal::create(commandBuffer);
            
            if (!recordTraversal)
            {
                std::cout << "FAILED: Could not create record traversal" << std::endl;
                return false;
            }
            
            // 测试统计信息
            auto stats = recordTraversal->getStatistics();
            if (stats.numDrawCalls != 0 || stats.numTriangles != 0)
            {
                std::cout << "FAILED: Initial statistics should be zero" << std::endl;
                return false;
            }
            
            std::cout << "PASSED: Record traversal created successfully" << std::endl;
            return true;
        }
        catch (const std::exception& e)
        {
            std::cout << "FAILED: Exception - " << e.what() << std::endl;
            return false;
        }
    }
    
    bool testRenderGraph()
    {
        std::cout << "\n--- Test: Render Graph ---" << std::endl;
        
        try
        {
            auto device = Device::create();
            if (!device || !device->initialize())
            {
#if VSG_WEBGPU_USE_MOCK
                std::cout << "Mock mode: Creating render graph" << std::endl;
#else
                std::cout << "FAILED: Could not initialize device" << std::endl;
                return false;
#endif
            }
            
            auto renderGraph = RenderGraph::create(device.get());
            
            if (!renderGraph)
            {
                std::cout << "FAILED: Could not create render graph" << std::endl;
                return false;
            }
            
            // 创建简单场景
            auto sceneGraph = vsg::Group::create();
            auto geometry = vsg::Geometry::create();
            sceneGraph->addChild(geometry);
            
            // 添加渲染通道
            renderGraph->addRenderPass("main", sceneGraph);
            
            if (renderGraph->renderPasses.empty())
            {
                std::cout << "FAILED: Render pass not added" << std::endl;
                return false;
            }
            
            // 测试统计信息
            auto stats = renderGraph->getStatistics();
            if (stats.numRenderPasses != 1)
            {
                std::cout << "FAILED: Render pass count incorrect" << std::endl;
                return false;
            }
            
            std::cout << "PASSED: Render graph created with " << renderGraph->renderPasses.size() << " render passes" << std::endl;
            return true;
        }
        catch (const std::exception& e)
        {
            std::cout << "FAILED: Exception - " << e.what() << std::endl;
            return false;
        }
    }
};

int main()
{
    try
    {
        SceneGraphTest test;
        bool success = test.runTests();
        
        shutdownWebGPU();
        
        return success ? 0 : 1;
    }
    catch (const std::exception& e)
    {
        std::cerr << "Fatal error: " << e.what() << std::endl;
        return 1;
    }
}
