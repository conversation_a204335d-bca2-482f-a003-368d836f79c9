#pragma once

/* <editor-fold desc="MIT License">

Copyright(c) 2024 VSG Render Engine Abstraction

Permission is hereby granted, free of charge, to any person obtaining a copy of this software and associated documentation files (the "Software"), to deal in the Software without restriction, including without limitation the rights to use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of the Software, and to permit persons to whom the Software is furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.

</editor-fold> */

/**
 * @file Config.h
 * @brief VSG Render Engine Abstraction 编译时配置
 * 
 * 这个文件由CMake自动生成，包含了编译时的配置信息。
 */

// ========== 版本信息 ==========

#define VSG_ABSTRACTION_VERSION_MAJOR 1
#define VSG_ABSTRACTION_VERSION_MINOR 0
#define VSG_ABSTRACTION_VERSION_PATCH 0
#define VSG_ABSTRACTION_VERSION "1.0.0"

// ========== 后端支持 ==========

// VSG后端支持
#define VSG_ABSTRACTION_HAS_VSG
#ifdef VSG_ABSTRACTION_HAS_VSG
#    define VSG_ABSTRACTION_SUPPORT_VSG 1
#else
#    define VSG_ABSTRACTION_SUPPORT_VSG 0
#endif

// WebGPU后端支持
#define VSG_ABSTRACTION_HAS_WEBGPU
#ifdef VSG_ABSTRACTION_HAS_WEBGPU
#    define VSG_ABSTRACTION_SUPPORT_WEBGPU 1
#else
#    define VSG_ABSTRACTION_SUPPORT_WEBGPU 0
#endif

// OpenGL后端支持
/* #undef VSG_ABSTRACTION_HAS_OPENGL */
#ifdef VSG_ABSTRACTION_HAS_OPENGL
#    define VSG_ABSTRACTION_SUPPORT_OPENGL 1
#else
#    define VSG_ABSTRACTION_SUPPORT_OPENGL 0
#endif

// Mock后端支持（总是启用）
#define VSG_ABSTRACTION_SUPPORT_MOCK 1

// ========== 平台检测 ==========

// 操作系统检测
#if defined(_WIN32) || defined(_WIN64)
#    define VSG_ABSTRACTION_PLATFORM_WINDOWS 1
#    define VSG_ABSTRACTION_PLATFORM_NAME "Windows"
#elif defined(__linux__)
#    define VSG_ABSTRACTION_PLATFORM_LINUX 1
#    define VSG_ABSTRACTION_PLATFORM_NAME "Linux"
#elif defined(__APPLE__)
#    include <TargetConditionals.h>
#    if TARGET_OS_MAC
#        define VSG_ABSTRACTION_PLATFORM_MACOS 1
#        define VSG_ABSTRACTION_PLATFORM_NAME "macOS"
#    elif TARGET_OS_IPHONE
#        define VSG_ABSTRACTION_PLATFORM_IOS 1
#        define VSG_ABSTRACTION_PLATFORM_NAME "iOS"
#    endif
#elif defined(__EMSCRIPTEN__)
#    define VSG_ABSTRACTION_PLATFORM_EMSCRIPTEN 1
#    define VSG_ABSTRACTION_PLATFORM_NAME "Emscripten"
#elif defined(__ANDROID__)
#    define VSG_ABSTRACTION_PLATFORM_ANDROID 1
#    define VSG_ABSTRACTION_PLATFORM_NAME "Android"
#else
#    define VSG_ABSTRACTION_PLATFORM_UNKNOWN 1
#    define VSG_ABSTRACTION_PLATFORM_NAME "Unknown"
#endif

// 编译器检测
#if defined(_MSC_VER)
#    define VSG_ABSTRACTION_COMPILER_MSVC 1
#    define VSG_ABSTRACTION_COMPILER_NAME "MSVC"
#    define VSG_ABSTRACTION_COMPILER_VERSION _MSC_VER
#elif defined(__clang__)
#    define VSG_ABSTRACTION_COMPILER_CLANG 1
#    define VSG_ABSTRACTION_COMPILER_NAME "Clang"
#    define VSG_ABSTRACTION_COMPILER_VERSION (__clang_major__ * 10000 + __clang_minor__ * 100 + __clang_patchlevel__)
#elif defined(__GNUC__)
#    define VSG_ABSTRACTION_COMPILER_GCC 1
#    define VSG_ABSTRACTION_COMPILER_NAME "GCC"
#    define VSG_ABSTRACTION_COMPILER_VERSION (__GNUC__ * 10000 + __GNUC_MINOR__ * 100 + __GNUC_PATCHLEVEL__)
#else
#    define VSG_ABSTRACTION_COMPILER_UNKNOWN 1
#    define VSG_ABSTRACTION_COMPILER_NAME "Unknown"
#    define VSG_ABSTRACTION_COMPILER_VERSION 0
#endif

// ========== 库类型检测 ==========

/* #undef VSG_ABSTRACTION_SHARED_LIBRARY */
/* #undef VSG_ABSTRACTION_STATIC_LIBRARY */

// ========== 导出宏 ==========

#ifdef VSG_ABSTRACTION_PLATFORM_WINDOWS
#    ifdef VSG_ABSTRACTION_SHARED_LIBRARY
#        ifdef VSG_ABSTRACTION_EXPORTS
#            define VSG_ABSTRACTION_DECLSPEC __declspec(dllexport)
#        else
#            define VSG_ABSTRACTION_DECLSPEC __declspec(dllimport)
#        endif
#    else
#        define VSG_ABSTRACTION_DECLSPEC
#    endif
#    define VSG_ABSTRACTION_LOCAL
#else
#    if defined(__GNUC__) && __GNUC__ >= 4
#        ifdef VSG_ABSTRACTION_SHARED_LIBRARY
#            define VSG_ABSTRACTION_DECLSPEC __attribute__((visibility("default")))
#        else
#            define VSG_ABSTRACTION_DECLSPEC
#        endif
#        define VSG_ABSTRACTION_LOCAL __attribute__((visibility("hidden")))
#    else
#        define VSG_ABSTRACTION_DECLSPEC
#        define VSG_ABSTRACTION_LOCAL
#    endif
#endif

// ========== 调试宏 ==========

#ifdef _DEBUG
#    define VSG_ABSTRACTION_DEBUG 1
#else
#    define VSG_ABSTRACTION_DEBUG 0
#endif

// 断言宏
#if VSG_ABSTRACTION_DEBUG
#    include <cassert>
#    define VSG_ABSTRACTION_ASSERT(condition) assert(condition)
#    define VSG_ABSTRACTION_ASSERT_MSG(condition, message) assert((condition) && (message))
#else
#    define VSG_ABSTRACTION_ASSERT(condition) ((void)0)
#    define VSG_ABSTRACTION_ASSERT_MSG(condition, message) ((void)0)
#endif

// ========== 日志宏 ==========

#include <iostream>

#define VSG_ABSTRACTION_LOG_INFO(msg) std::cout << "[VSG_ABSTRACTION][INFO] " << msg << std::endl
#define VSG_ABSTRACTION_LOG_WARN(msg) std::cout << "[VSG_ABSTRACTION][WARN] " << msg << std::endl
#define VSG_ABSTRACTION_LOG_ERROR(msg) std::cerr << "[VSG_ABSTRACTION][ERROR] " << msg << std::endl

#if VSG_ABSTRACTION_DEBUG
#    define VSG_ABSTRACTION_LOG_DEBUG(msg) std::cout << "[VSG_ABSTRACTION][DEBUG] " << msg << std::endl
#else
#    define VSG_ABSTRACTION_LOG_DEBUG(msg) ((void)0)
#endif

// ========== 性能宏 ==========

// 强制内联
#ifdef VSG_ABSTRACTION_COMPILER_MSVC
#    define VSG_ABSTRACTION_FORCE_INLINE __forceinline
#elif defined(VSG_ABSTRACTION_COMPILER_GCC) || defined(VSG_ABSTRACTION_COMPILER_CLANG)
#    define VSG_ABSTRACTION_FORCE_INLINE __attribute__((always_inline)) inline
#else
#    define VSG_ABSTRACTION_FORCE_INLINE inline
#endif

// 禁用内联
#ifdef VSG_ABSTRACTION_COMPILER_MSVC
#    define VSG_ABSTRACTION_NO_INLINE __declspec(noinline)
#elif defined(VSG_ABSTRACTION_COMPILER_GCC) || defined(VSG_ABSTRACTION_COMPILER_CLANG)
#    define VSG_ABSTRACTION_NO_INLINE __attribute__((noinline))
#else
#    define VSG_ABSTRACTION_NO_INLINE
#endif

// 分支预测提示
#if defined(VSG_ABSTRACTION_COMPILER_GCC) || defined(VSG_ABSTRACTION_COMPILER_CLANG)
#    define VSG_ABSTRACTION_LIKELY(x) __builtin_expect(!!(x), 1)
#    define VSG_ABSTRACTION_UNLIKELY(x) __builtin_expect(!!(x), 0)
#else
#    define VSG_ABSTRACTION_LIKELY(x) (x)
#    define VSG_ABSTRACTION_UNLIKELY(x) (x)
#endif

// ========== 功能检测 ==========

// C++20特性检测
#if __cplusplus >= 202002L
#    define VSG_ABSTRACTION_HAS_CPP20 1
#    define VSG_ABSTRACTION_HAS_CONCEPTS 1
#    define VSG_ABSTRACTION_HAS_MODULES 0 // 模块支持还不完善
#else
#    define VSG_ABSTRACTION_HAS_CPP20 0
#    define VSG_ABSTRACTION_HAS_CONCEPTS 0
#    define VSG_ABSTRACTION_HAS_MODULES 0
#endif

// 线程支持
#include <thread>
#define VSG_ABSTRACTION_HAS_THREADS 1

// ========== VSG集成 ==========

#if VSG_ABSTRACTION_SUPPORT_VSG
// VSG版本检测
#    ifdef VSG_VERSION_MAJOR
#        define VSG_ABSTRACTION_VSG_VERSION_MAJOR VSG_VERSION_MAJOR
#        define VSG_ABSTRACTION_VSG_VERSION_MINOR VSG_VERSION_MINOR
#        define VSG_ABSTRACTION_VSG_VERSION_PATCH VSG_VERSION_PATCH
#    else
#        define VSG_ABSTRACTION_VSG_VERSION_MAJOR 0
#        define VSG_ABSTRACTION_VSG_VERSION_MINOR 0
#        define VSG_ABSTRACTION_VSG_VERSION_PATCH 0
#    endif

// VSG兼容性检查
#    if VSG_ABSTRACTION_VSG_VERSION_MAJOR >= 1
#        define VSG_ABSTRACTION_VSG_COMPATIBLE 1
#    else
#        define VSG_ABSTRACTION_VSG_COMPATIBLE 0
#        pragma message("VSG version may not be fully compatible")
#    endif
#endif

// ========== 常量定义 ==========

namespace vsg_abstraction
{

    // 版本信息
    constexpr int VERSION_MAJOR = VSG_ABSTRACTION_VERSION_MAJOR;
    constexpr int VERSION_MINOR = VSG_ABSTRACTION_VERSION_MINOR;
    constexpr int VERSION_PATCH = VSG_ABSTRACTION_VERSION_PATCH;
    constexpr const char* VERSION_STRING = VSG_ABSTRACTION_VERSION;

    // 平台信息
    constexpr const char* PLATFORM_NAME = VSG_ABSTRACTION_PLATFORM_NAME;
    constexpr const char* COMPILER_NAME = VSG_ABSTRACTION_COMPILER_NAME;
    constexpr int COMPILER_VERSION = VSG_ABSTRACTION_COMPILER_VERSION;

    // 后端支持
    constexpr bool SUPPORT_VSG = VSG_ABSTRACTION_SUPPORT_VSG;
    constexpr bool SUPPORT_WEBGPU = VSG_ABSTRACTION_SUPPORT_WEBGPU;
    constexpr bool SUPPORT_OPENGL = VSG_ABSTRACTION_SUPPORT_OPENGL;
    constexpr bool SUPPORT_MOCK = VSG_ABSTRACTION_SUPPORT_MOCK;

    // 功能支持
    constexpr bool HAS_CPP20 = VSG_ABSTRACTION_HAS_CPP20;
    constexpr bool HAS_CONCEPTS = VSG_ABSTRACTION_HAS_CONCEPTS;
    constexpr bool HAS_THREADS = VSG_ABSTRACTION_HAS_THREADS;

#if VSG_ABSTRACTION_SUPPORT_VSG
    // VSG版本信息
    constexpr int VSG_VERSION_MAJOR = VSG_ABSTRACTION_VSG_VERSION_MAJOR;
    constexpr int VSG_VERSION_MINOR = VSG_ABSTRACTION_VSG_VERSION_MINOR;
    constexpr int VSG_VERSION_PATCH = VSG_ABSTRACTION_VSG_VERSION_PATCH;
    constexpr bool VSG_COMPATIBLE = VSG_ABSTRACTION_VSG_COMPATIBLE;
#endif

} // namespace vsg_abstraction
