/* <editor-fold desc="MIT License">

Copyright(c) 2024 VSG WebGPU Extension

Permission is hereby granted, free of charge, to any person obtaining a copy of this software and associated documentation files (the "Software"), to deal in the Software without restriction, including without limitation the rights to use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of the Software, and to permit persons to whom the Software is furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.

</editor-fold> */

#include <vsg_webgpu/vk/Queue.h>
#include <vsg_webgpu/vk/Device.h>
#include <vsg_webgpu/vk/CommandBuffer.h>

using namespace vsg_webgpu;

Queue::Queue(Device* device, WGPUQueue queue, uint32_t queueFamilyIndex, uint32_t queueIndex) :
    _device(device),
    _queue(queue),
    _queueFamilyIndex(queueFamilyIndex),
    _queueIndex(queueIndex),
    _queueFlags(0) // WebGPU队列支持所有操作
{
    VSG_WEBGPU_LOG_DEBUG("Queue::Queue()");
}

Queue::~Queue()
{
    VSG_WEBGPU_LOG_DEBUG("Queue::~Queue()");
}

void Queue::submit(const std::vector<SubmitInfo>& submitInfos)
{
    std::lock_guard<std::mutex> lock(_submitMutex);
    
    for (const auto& submitInfo : submitInfos)
    {
        submit(submitInfo);
    }
}

void Queue::submit(const SubmitInfo& submitInfo)
{
    std::lock_guard<std::mutex> lock(_submitMutex);
    
    // 处理等待信号量（WebGPU中这通常是空操作）
    _handleSemaphores(submitInfo.waitSemaphores, submitInfo.signalSemaphores);
    
    // 提交命令缓冲区
    _submitCommandBuffers(submitInfo.commandBuffers);
    
    // 处理fence（WebGPU中这通常是空操作）
    if (submitInfo.fence)
    {
        // 在WebGPU中，我们可能需要使用其他机制来模拟fence
        VSG_WEBGPU_LOG_DEBUG("Fence signaling requested (WebGPU implementation pending)");
    }
}

void Queue::submit(vsg::ref_ptr<CommandBuffer> commandBuffer, vsg::ref_ptr<Fence> fence)
{
    SubmitInfo submitInfo;
    submitInfo.commandBuffers.push_back(commandBuffer);
    submitInfo.fence = fence;
    submit(submitInfo);
}

void Queue::submit(const std::vector<vsg::ref_ptr<CommandBuffer>>& commandBuffers, vsg::ref_ptr<Fence> fence)
{
    SubmitInfo submitInfo;
    submitInfo.commandBuffers = commandBuffers;
    submitInfo.fence = fence;
    submit(submitInfo);
}

void Queue::waitIdle()
{
    // WebGPU没有直接的队列等待方法
    // 这里我们可能需要使用其他同步机制
    VSG_WEBGPU_LOG_DEBUG("Queue::waitIdle() - WebGPU implementation pending");
}

void Queue::present(const PresentInfo& presentInfo)
{
    // WebGPU的present是自动的，当交换链纹理被使用后会自动呈现
    // 这里主要处理等待信号量
    _handleSemaphores(presentInfo.waitSemaphores, {});
    
    VSG_WEBGPU_LOG_DEBUG("Queue::present() - WebGPU auto-present");
}

void Queue::writeTimestamp(uint32_t queryIndex)
{
#if !VSG_WEBGPU_USE_MOCK
    // WebGPU时间戳查询需要特定的查询集
    VSG_WEBGPU_LOG_DEBUG("Queue::writeTimestamp() - implementation needed");
#endif
}

void Queue::writeBuffer(WGPUBuffer buffer, uint64_t bufferOffset, const void* data, size_t size)
{
#if !VSG_WEBGPU_USE_MOCK
    _queue.WriteBuffer(buffer, bufferOffset, data, size);
#endif
}

void Queue::writeTexture(const WGPUImageCopyTexture& destination, const void* data, size_t dataSize, 
                        const WGPUTextureDataLayout& dataLayout, const WGPUExtent3D& writeSize)
{
#if !VSG_WEBGPU_USE_MOCK
    _queue.WriteTexture(&destination, data, dataSize, &dataLayout, &writeSize);
#endif
}

void Queue::copyBufferToBuffer(WGPUBuffer source, uint64_t sourceOffset, 
                              WGPUBuffer destination, uint64_t destinationOffset, uint64_t size)
{
    // 这个操作需要通过命令编码器完成，不能直接在队列上执行
    VSG_WEBGPU_LOG_WARN("Queue::copyBufferToBuffer() should be done through CommandBuffer");
}

void Queue::_submitCommandBuffers(const std::vector<vsg::ref_ptr<CommandBuffer>>& commandBuffers)
{
#if !VSG_WEBGPU_USE_MOCK
    std::vector<WGPUCommandBuffer> wgpuCommandBuffers;
    wgpuCommandBuffers.reserve(commandBuffers.size());
    
    for (auto& commandBuffer : commandBuffers)
    {
        if (commandBuffer)
        {
            auto wgpuCmdBuf = commandBuffer->finish();
            if (wgpuCmdBuf)
            {
                wgpuCommandBuffers.push_back(wgpuCmdBuf);
            }
        }
    }
    
    if (!wgpuCommandBuffers.empty())
    {
        _queue.Submit(wgpuCommandBuffers.size(), wgpuCommandBuffers.data());
    }
#endif
}

void Queue::_handleSemaphores(const std::vector<vsg::ref_ptr<Semaphore>>& waitSemaphores,
                             const std::vector<vsg::ref_ptr<Semaphore>>& signalSemaphores)
{
    // WebGPU没有显式的信号量概念
    // 这里我们记录日志，实际的同步可能需要其他机制
    if (!waitSemaphores.empty())
    {
        VSG_WEBGPU_LOG_DEBUG("Wait semaphores requested: ", waitSemaphores.size());
    }
    
    if (!signalSemaphores.empty())
    {
        VSG_WEBGPU_LOG_DEBUG("Signal semaphores requested: ", signalSemaphores.size());
    }
}
