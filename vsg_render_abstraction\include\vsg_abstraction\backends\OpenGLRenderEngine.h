#pragma once

/* <editor-fold desc="MIT License">

Copyright(c) 2024 VSG Render Engine Abstraction

Permission is hereby granted, free of charge, to any person obtaining a copy of this software and associated documentation files (the "Software"), to deal in the Software without restriction, including without limitation the rights to use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of the Software, and to permit persons to whom the Software is furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.

</editor-fold> */

/**
 * @file OpenGLRenderEngine.h
 * @brief OpenGL渲染引擎后端接口
 * 
 * 这个文件定义了OpenGL渲染引擎的接口。
 * 当前是占位符实现，实际的OpenGL支持需要进一步开发。
 */

#include <vsg_abstraction/core/Export.h>
#include <vsg_abstraction/core/IRenderEngine.h>
#include <vsg_abstraction/core/Types.h>

namespace vsg_abstraction
{

    /**
 * @brief OpenGL渲染引擎类（占位符）
 * 
 * 这是OpenGL后端的占位符实现。
 * 实际的OpenGL支持需要集成OpenGL库和上下文管理。
 */
    class OpenGLRenderEngine : public IRenderEngine
    {
    public:
        /**
     * @brief 构造函数
     * @param config 渲染引擎配置
     */
        explicit OpenGLRenderEngine(const RenderEngineConfig* config = nullptr);

        /**
     * @brief 析构函数
     */
        virtual ~OpenGLRenderEngine();

        // ========== 基础接口 ==========
        bool initialize() override;
        void shutdown() override;
        bool isInitialized() const override;
        RenderBackend getBackendType() const override;
        EngineInfo getEngineInfo() const override;

        // ========== 设备管理 ==========
        IDevice* getDevice() override;
        IInstance* getInstance() override;
        std::vector<IPhysicalDevice*> getPhysicalDevices() override;

        // ========== 场景图创建 ==========
        ref_ptr<IGroup> createGroup() override;
        ref_ptr<ITransform> createTransform() override;
        ref_ptr<ITransform> createMatrixTransform() override;
        ref_ptr<IGeometry> createGeometry() override;
        ref_ptr<IStateGroup> createStateGroup() override;

        // ========== 窗口和渲染管理 ==========
        ref_ptr<IWindow> createWindow(const WindowTraits& traits) override;
        ref_ptr<IRenderGraph> createRenderGraph(ref_ptr<IWindow> window, ref_ptr<INode> view) override;
        ref_ptr<IRecordTraversal> createRecordTraversal() override;

        // ========== 资源管理 ==========
        ref_ptr<IBuffer> createBuffer(const BufferInfo& info) override;
        ref_ptr<IImage> createImage(const ImageInfo& info) override;
        ref_ptr<IPipeline> createGraphicsPipeline(const GraphicsPipelineInfo& info) override;
        ref_ptr<IPipeline> createComputePipeline(const ComputePipelineInfo& info) override;

        // ========== 同步和性能 ==========
        void waitIdle() override;
        RenderStatistics getRenderStatistics() const override;
        void resetRenderStatistics() override;

        // ========== 调试和诊断 ==========
        void setErrorCallback(ErrorCallback callback) override;
        void setDebugCallback(DebugCallback callback) override;
        void beginDebugMarker(const std::string& name) override;
        void endDebugMarker() override;

        // ========== 扩展接口 ==========
        void* getNativeHandle() const override;
        void executeCustomCommand(std::function<void(void*)> command) override;
        bool supportsFeature(const std::string& feature) const override;

#ifdef VSG_ABSTRACTION_HAS_VSG
        // ========== VSG兼容性接口 ==========
        vsg::ref_ptr<vsg::Group> createVSGGroup() override;
        vsg::ref_ptr<vsg::MatrixTransform> createVSGTransform() override;
        vsg::ref_ptr<vsg::Geometry> createVSGGeometry() override;
        vsg::ref_ptr<vsg::StateGroup> createVSGStateGroup() override;
#endif

    private:
        // 私有实现
        class Impl;
        std::unique_ptr<Impl> impl_;
    };

} // namespace vsg_abstraction
