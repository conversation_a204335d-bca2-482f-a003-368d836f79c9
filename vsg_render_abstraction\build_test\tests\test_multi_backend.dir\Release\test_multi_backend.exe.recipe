﻿<?xml version="1.0" encoding="utf-8"?>
<Project>
  <ProjectOutputs>
    <ProjectOutput>
      <FullPath>F:\cmo-dev\my_osgearth_web\osgearth_origin\VulkanSceneGraph\vsg_render_abstraction\build_test\x64\Release\ZERO_CHECK</FullPath>
    </ProjectOutput>
    <ProjectOutput>
      <FullPath>F:\cmo-dev\my_osgearth_web\osgearth_origin\VulkanSceneGraph\vsg_render_abstraction\build_test\bin\Release\vsg_abstraction.dll</FullPath>
    </ProjectOutput>
    <ProjectOutput>
      <FullPath>F:\cmo-dev\my_osgearth_web\osgearth_origin\VulkanSceneGraph\vsg_render_abstraction\build_test\tests\Release\test_multi_backend.exe</FullPath>
    </ProjectOutput>
  </ProjectOutputs>
  <ContentFiles />
  <SatelliteDlls />
  <NonRecipeFileRefs />
</Project>