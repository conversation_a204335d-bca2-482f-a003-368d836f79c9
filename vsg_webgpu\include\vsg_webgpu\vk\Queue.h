#pragma once

/* <editor-fold desc="MIT License">

Copyright(c) 2024 VSG WebGPU Extension

Permission is hereby granted, free of charge, to any person obtaining a copy of this software and associated documentation files (the "Software"), to deal in the Software without restriction, including without limitation the rights to use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of the Software, and to permit persons to whom the Software is furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.

</editor-fold> */

#include <vsg_webgpu/core/Export.h>
#include <vsg_webgpu/core/WebGPUHeaders.h>
#include <vsg/core/Inherit.h>
#include <vsg/core/Object.h>
#include <vsg/core/ref_ptr.h>
#include <vector>
#include <mutex>

namespace vsg_webgpu
{
    // forward declarations
    class Device;
    class CommandBuffer;
    class Fence;
    class Semaphore;

    /// WebGPU队列封装，对应VSG的Queue类
    /// 提供与VSG Queue相同的接口，但底层使用WebGPU实现
    class VSG_WEBGPU_DECLSPEC Queue : public vsg::Inherit<vsg::Object, Queue>
    {
    public:
        Queue(Device* device, WGPUQueue queue, uint32_t queueFamilyIndex = 0, uint32_t queueIndex = 0);

        // WebGPU队列访问
        WGPUQueue getQueue() const { return _queue; }
        operator WGPUQueue() const { return _queue; }

        // 队列属性 - 对应VSG的Queue接口
        uint32_t queueFamilyIndex() const { return _queueFamilyIndex; }
        uint32_t queueIndex() const { return _queueIndex; }
        uint32_t queueFlags() const { return _queueFlags; }

        Device* getDevice() { return _device; }
        const Device* getDevice() const { return _device; }

        // 命令提交 - 对应VSG的submit方法
        struct SubmitInfo
        {
            std::vector<vsg::ref_ptr<CommandBuffer>> commandBuffers;
            std::vector<vsg::ref_ptr<Semaphore>> waitSemaphores;
            std::vector<vsg::ref_ptr<Semaphore>> signalSemaphores;
            vsg::ref_ptr<Fence> fence;
        };

        void submit(const std::vector<SubmitInfo>& submitInfos);
        void submit(const SubmitInfo& submitInfo);

        // 简化的提交方法
        void submit(vsg::ref_ptr<CommandBuffer> commandBuffer, vsg::ref_ptr<Fence> fence = {});
        void submit(const std::vector<vsg::ref_ptr<CommandBuffer>>& commandBuffers, vsg::ref_ptr<Fence> fence = {});

        // 队列同步
        void waitIdle();

        // 呈现支持 - 对应VSG的present方法
        struct PresentInfo
        {
            std::vector<vsg::ref_ptr<Semaphore>> waitSemaphores;
            // WebGPU的present是自动的，不需要显式调用
        };

        void present(const PresentInfo& presentInfo);

        // 时间戳查询支持
        void writeTimestamp(uint32_t queryIndex);

        // 缓冲区操作
        void writeBuffer(WGPUBuffer buffer, uint64_t bufferOffset, const void* data, size_t size);

        // 纹理操作
        void writeTexture(const WGPUImageCopyTexture& destination, const void* data, size_t dataSize, 
                         const WGPUTextureDataLayout& dataLayout, const WGPUExtent3D& writeSize);

        // 复制操作
        void copyBufferToBuffer(WGPUBuffer source, uint64_t sourceOffset, 
                               WGPUBuffer destination, uint64_t destinationOffset, uint64_t size);

    protected:
        virtual ~Queue();

    private:
        Device* _device;
        WGPUQueue _queue;
        uint32_t _queueFamilyIndex;
        uint32_t _queueIndex;
        uint32_t _queueFlags;
        
        std::mutex _submitMutex;

        // 内部提交实现
        void _submitCommandBuffers(const std::vector<vsg::ref_ptr<CommandBuffer>>& commandBuffers);
        void _handleSemaphores(const std::vector<vsg::ref_ptr<Semaphore>>& waitSemaphores,
                              const std::vector<vsg::ref_ptr<Semaphore>>& signalSemaphores);
    };

    VSG_type_name(vsg_webgpu::Queue);

} // namespace vsg_webgpu
