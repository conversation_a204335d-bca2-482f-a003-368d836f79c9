#pragma once

/* <editor-fold desc="MIT License">

Copyright(c) 2024 VSG Render Engine Abstraction

Permission is hereby granted, free of charge, to any person obtaining a copy of this software and associated documentation files (the "Software"), to deal in the Software without restriction, including without limitation the rights to use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of the Software, and to permit persons to whom the Software is furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND <PERSON>NINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.

</editor-fold> */

#include <vsg_abstraction/core/Types.h>
#include <vsg_abstraction/core/Export.h>
#include <string>
#include <vector>

namespace vsg_abstraction {

/**
 * @brief 设备接口
 */
class IDevice {
public:
    virtual ~IDevice() = default;

    // 基础信息
    virtual std::string getName() const = 0;
    virtual std::string getVendor() const = 0;
    virtual uint32_t getDeviceId() const = 0;
    virtual uint32_t getVendorId() const = 0;

    // 能力查询
    virtual DeviceCapabilities getCapabilities() const = 0;
    virtual bool supportsFeature(const std::string& feature) const = 0;

    // 原生句柄
    virtual void* getNativeHandle() const = 0;
};

/**
 * @brief 物理设备接口
 */
class IPhysicalDevice {
public:
    virtual ~IPhysicalDevice() = default;

    // 基础信息
    virtual std::string getName() const = 0;
    virtual std::string getVendor() const = 0;
    virtual uint32_t getDeviceId() const = 0;
    virtual uint32_t getVendorId() const = 0;

    // 能力查询
    virtual DeviceCapabilities getCapabilities() const = 0;
    virtual bool supportsFeature(const std::string& feature) const = 0;

    // 原生句柄
    virtual void* getNativeHandle() const = 0;
};

/**
 * @brief 实例接口
 */
class IInstance {
public:
    virtual ~IInstance() = default;

    // 基础信息
    virtual std::string getName() const = 0;
    virtual std::string getVersion() const = 0;

    // 设备管理
    virtual std::vector<IPhysicalDevice*> getPhysicalDevices() = 0;

    // 原生句柄
    virtual void* getNativeHandle() const = 0;
};

} // namespace vsg_abstraction
