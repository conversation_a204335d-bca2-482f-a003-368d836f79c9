/* <editor-fold desc="MIT License">

Copyright(c) 2024 VSG Render Engine Abstraction

Permission is hereby granted, free of charge, to any person obtaining a copy of this software and associated documentation files (the "Software"), to deal in the Software without restriction, including without limitation the rights to use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of the Software, and to permit persons to whom the Software is furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.

</editor-fold> */

#include "MockNodes.h"
#include <iostream>
#include <vsg_abstraction/backends/MockRenderEngine.h>
#include <vsg_abstraction/core/Config.h>

namespace vsg_abstraction
{

    MockRenderEngine::MockRenderEngine(const RenderEngineConfig* config)
    {
        if (config)
        {
            config_ = *config;
        }
        std::cout << "[Mock] MockRenderEngine created at " << this << std::endl;
    }

    MockRenderEngine::~MockRenderEngine()
    {
        shutdown();
        std::cout << "[Mock] MockRenderEngine destroyed" << std::endl;
    }

    bool MockRenderEngine::initialize()
    {
        std::cout << "[Mock] Initialize" << std::endl;
        initialized_ = true;
        return true;
    }

    void MockRenderEngine::shutdown()
    {
        std::cout << "[Mock] Shutdown" << std::endl;
        initialized_ = false;
    }

    bool MockRenderEngine::isInitialized() const
    {
        return initialized_;
    }

    RenderBackend MockRenderEngine::getBackendType() const
    {
        return RenderBackend::Mock;
    }

    EngineInfo MockRenderEngine::getEngineInfo() const
    {
        EngineInfo info;
        info.name = "Mock Render Engine";
        info.version = "1.0.0";
        info.vendor = "VSG Abstraction";
        info.backend = RenderBackend::Mock;
        return info;
    }

    // Device management
    IDevice* MockRenderEngine::getDevice() { return nullptr; }
    IInstance* MockRenderEngine::getInstance() { return nullptr; }
    std::vector<IPhysicalDevice*> MockRenderEngine::getPhysicalDevices() { return {}; }

    // Scene graph creation
    ref_ptr<IGroup> MockRenderEngine::createGroup()
    {
        std::cout << "[Mock] createGroup() ENTRY" << std::endl;
        updateStatistics();
        std::cout << "[Mock] Creating Group" << std::endl;
        auto group = std::make_shared<MockGroup>();
        std::cout << "[Mock] Group created: " << (group ? "success" : "failed") << std::endl;
        return group;
    }

    ref_ptr<ITransform> MockRenderEngine::createTransform()
    {
        updateStatistics();
        std::cout << "[Mock] Creating Transform" << std::endl;
        auto transform = std::make_shared<MockTransform>();
        std::cout << "[Mock] Transform created: " << (transform ? "success" : "failed") << std::endl;
        return transform;
    }

    ref_ptr<ITransform> MockRenderEngine::createMatrixTransform()
    {
        updateStatistics();
        std::cout << "[Mock] Creating MatrixTransform" << std::endl;
        auto transform = std::make_shared<MockTransform>();
        std::cout << "[Mock] MatrixTransform created: " << (transform ? "success" : "failed") << std::endl;
        return transform;
    }

    ref_ptr<IGeometry> MockRenderEngine::createGeometry()
    {
        updateStatistics();
        std::cout << "[Mock] Creating Geometry" << std::endl;
        auto geometry = std::make_shared<MockGeometry>();
        std::cout << "[Mock] Geometry created: " << (geometry ? "success" : "failed") << std::endl;
        return geometry;
    }

    ref_ptr<IStateGroup> MockRenderEngine::createStateGroup()
    {
        updateStatistics();
        return std::make_shared<MockStateGroup>();
    }

    // Window and rendering
    ref_ptr<IWindow> MockRenderEngine::createWindow(const WindowTraits& traits)
    {
        (void)traits;
        return nullptr;
    }

    ref_ptr<IRenderGraph> MockRenderEngine::createRenderGraph(ref_ptr<IWindow> window, ref_ptr<INode> view)
    {
        (void)window;
        (void)view;
        return nullptr;
    }

    ref_ptr<IRecordTraversal> MockRenderEngine::createRecordTraversal() { return nullptr; }

    // Resource management
    ref_ptr<IBuffer> MockRenderEngine::createBuffer(const BufferInfo& info)
    {
        (void)info;
        return nullptr;
    }

    ref_ptr<IImage> MockRenderEngine::createImage(const ImageInfo& info)
    {
        (void)info;
        return nullptr;
    }

    ref_ptr<IPipeline> MockRenderEngine::createGraphicsPipeline(const GraphicsPipelineInfo& info)
    {
        (void)info;
        return nullptr;
    }

    ref_ptr<IPipeline> MockRenderEngine::createComputePipeline(const ComputePipelineInfo& info)
    {
        (void)info;
        return nullptr;
    }

    // Synchronization and performance
    void MockRenderEngine::waitIdle() {}

    RenderStatistics MockRenderEngine::getRenderStatistics() const
    {
        return statistics_;
    }

    void MockRenderEngine::resetRenderStatistics()
    {
        statistics_ = RenderStatistics{};
    }

    // Debug and diagnostics
    void MockRenderEngine::setErrorCallback(ErrorCallback callback)
    {
        errorCallback_ = callback;
    }

    void MockRenderEngine::setDebugCallback(DebugCallback callback)
    {
        debugCallback_ = callback;
    }

    void MockRenderEngine::beginDebugMarker(const std::string& name)
    {
        (void)name;
    }

    void MockRenderEngine::endDebugMarker() {}

    // Extension interface
    void* MockRenderEngine::getNativeHandle() const { return nullptr; }

    void MockRenderEngine::executeCustomCommand(std::function<void(void*)> command)
    {
        (void)command;
    }

    bool MockRenderEngine::supportsFeature(const std::string& feature) const
    {
        (void)feature;
        return false;
    }

#ifdef VSG_ABSTRACTION_HAS_VSG
    // VSG compatibility interface
    vsg::ref_ptr<vsg::Group> MockRenderEngine::createVSGGroup() { return nullptr; }
    vsg::ref_ptr<vsg::MatrixTransform> MockRenderEngine::createVSGTransform() { return nullptr; }
    vsg::ref_ptr<vsg::Geometry> MockRenderEngine::createVSGGeometry() { return nullptr; }
    vsg::ref_ptr<vsg::StateGroup> MockRenderEngine::createVSGStateGroup() { return nullptr; }
#endif

    void MockRenderEngine::updateStatistics()
    {
        statistics_.frameCount++;
        statistics_.memoryUsed += 1024; // Simulate memory usage
    }

} // namespace vsg_abstraction
