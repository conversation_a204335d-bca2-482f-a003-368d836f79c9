/* <editor-fold desc="MIT License">

Copyright(c) 2024 VSG Render Engine Abstraction

Permission is hereby granted, free of charge, to any person obtaining a copy of this software and associated documentation files (the "Software"), to deal in the Software without restriction, including without limitation the rights to use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of the Software, and to permit persons to whom the Software is furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.

</editor-fold> */

#include <vsg_abstraction/backends/MockRenderEngine.h>
#include <vsg_abstraction/core/Config.h>

#include <iostream>
#include <chrono>
#include <thread>

namespace vsg_abstraction {

// ========== Mock节点实现 ==========

class MockNode : public INode {
public:
    MockNode(NodeType type) : type_(type), id_(generateId()) {}
    
    NodeType getNodeType() const override { return type_; }
    const std::string& getName() const override { return name_; }
    void setName(const std::string& name) override { name_ = name; }
    uint64_t getNodeId() const override { return id_; }
    
    void accept(IVisitor& visitor) override { visitor.apply(*this); }
    void traverse(IVisitor& visitor) override { visitor.traverse(*this); }
    
    std::array<double, 6> getLocalBounds() const override { return bounds_; }
    std::array<double, 6> getWorldBounds() const override { return bounds_; }
    void computeBounds() override {}
    
    void setUserData(const std::string& key, void* data) override { userData_[key] = data; }
    void* getUserData(const std::string& key) const override {
        auto it = userData_.find(key);
        return it != userData_.end() ? it->second : nullptr;
    }
    void removeUserData(const std::string& key) override { userData_.erase(key); }
    bool hasUserData(const std::string& key) const override { return userData_.count(key) > 0; }
    
    void* getNativeHandle() const override { return nullptr; }
    
#ifdef VSG_ABSTRACTION_HAS_VSG
    vsg::ref_ptr<vsg::Node> getVSGNode() const override { return nullptr; }
#endif

private:
    static uint64_t generateId() {
        static uint64_t counter = 0;
        return ++counter;
    }
    
    NodeType type_;
    std::string name_;
    uint64_t id_;
    std::array<double, 6> bounds_ = {0, 0, 0, 0, 0, 0};
    std::unordered_map<std::string, void*> userData_;
};

class MockGroup : public MockNode, public IGroup {
public:
    MockGroup() : MockNode(NodeType::Group) {}
    
    void addChild(ref_ptr<INode> child) override {
        if (child) {
            children_.push_back(child);
        }
    }
    
    void removeChild(ref_ptr<INode> child) override {
        auto it = std::find(children_.begin(), children_.end(), child);
        if (it != children_.end()) {
            children_.erase(it);
        }
    }
    
    void removeChild(size_t index) override {
        if (index < children_.size()) {
            children_.erase(children_.begin() + index);
        }
    }
    
    void removeAllChildren() override {
        children_.clear();
    }
    
    size_t getNumChildren() const override {
        return children_.size();
    }
    
    ref_ptr<INode> getChild(size_t index) const override {
        return index < children_.size() ? children_[index] : nullptr;
    }
    
    const std::vector<ref_ptr<INode>>& getChildren() const override {
        return children_;
    }
    
    ref_ptr<INode> findChild(const std::string& name) const override {
        for (auto& child : children_) {
            if (child && child->getName() == name) {
                return child;
            }
        }
        return nullptr;
    }

private:
    std::vector<ref_ptr<INode>> children_;
};

class MockTransform : public MockGroup, public ITransform {
public:
    MockTransform() : MockGroup() {
        setNodeType(NodeType::Transform);
        matrix_ = createIdentityMatrix();
    }
    
    void setMatrix(const mat4& matrix) override { matrix_ = matrix; }
    const mat4& getMatrix() const override { return matrix_; }
    mat4 getWorldMatrix() const override { return matrix_; }
    
    void setPosition(const vec3& position) override {
        matrix_[3][0] = position[0];
        matrix_[3][1] = position[1];
        matrix_[3][2] = position[2];
    }
    
    vec3 getPosition() const override {
        return {matrix_[3][0], matrix_[3][1], matrix_[3][2]};
    }
    
    void setRotation(const vec4& rotation) override {
        rotation_ = rotation;
        // 这里应该更新矩阵，简化实现
    }
    
    vec4 getRotation() const override {
        return rotation_;
    }
    
    void setScale(const vec3& scale) override {
        scale_ = scale;
        // 这里应该更新矩阵，简化实现
    }
    
    vec3 getScale() const override {
        return scale_;
    }

private:
    void setNodeType(NodeType type) {
        // 这是一个hack，因为基类的type_是私有的
        // 在实际实现中应该有更好的设计
    }
    
    mat4 matrix_;
    vec4 rotation_ = {0, 0, 0, 1};
    vec3 scale_ = {1, 1, 1};
};

class MockGeometry : public MockNode, public IGeometry {
public:
    MockGeometry() : MockNode(NodeType::Geometry) {}
    
    void setVertices(const std::vector<vec3>& vertices) override { vertices_ = vertices; }
    const std::vector<vec3>& getVertices() const override { return vertices_; }
    
    void setIndices(const std::vector<uint32_t>& indices) override { indices_ = indices; }
    const std::vector<uint32_t>& getIndices() const override { return indices_; }
    
    void setNormals(const std::vector<vec3>& normals) override { normals_ = normals; }
    const std::vector<vec3>& getNormals() const override { return normals_; }
    
    void setTexCoords(const std::vector<vec2>& texCoords) override { texCoords_ = texCoords; }
    const std::vector<vec2>& getTexCoords() const override { return texCoords_; }
    
    void setColors(const std::vector<vec4>& colors) override { colors_ = colors; }
    const std::vector<vec4>& getColors() const override { return colors_; }
    
    void setPrimitiveTopology(PrimitiveTopology topology) override { topology_ = topology; }
    PrimitiveTopology getPrimitiveTopology() const override { return topology_; }

private:
    std::vector<vec3> vertices_;
    std::vector<uint32_t> indices_;
    std::vector<vec3> normals_;
    std::vector<vec2> texCoords_;
    std::vector<vec4> colors_;
    PrimitiveTopology topology_ = PrimitiveTopology::TriangleList;
};

// ========== MockRenderEngine Implementation ==========

MockRenderEngine::MockRenderEngine(const RenderEngineConfig* config) {
    if (config) {
        config_ = *config;
    }
    
    std::cout << "[MOCK] MockRenderEngine created" << std::endl;
}

MockRenderEngine::~MockRenderEngine() {
    shutdown();
    std::cout << "[MOCK] MockRenderEngine destroyed" << std::endl;
}

bool MockRenderEngine::initialize() {
    if (initialized_) {
        return true;
    }
    
    std::cout << "[MOCK] Initializing MockRenderEngine..." << std::endl;
    
    // 模拟初始化延迟
    std::this_thread::sleep_for(std::chrono::milliseconds(100));
    
    initialized_ = true;
    std::cout << "[MOCK] MockRenderEngine initialized successfully" << std::endl;
    return true;
}

void MockRenderEngine::shutdown() {
    if (!initialized_) {
        return;
    }
    
    std::cout << "[MOCK] Shutting down MockRenderEngine..." << std::endl;
    
    // 模拟关闭延迟
    std::this_thread::sleep_for(std::chrono::milliseconds(50));
    
    initialized_ = false;
    std::cout << "[MOCK] MockRenderEngine shutdown completed" << std::endl;
}

bool MockRenderEngine::isInitialized() const {
    return initialized_;
}

RenderBackend MockRenderEngine::getBackendType() const {
    return RenderBackend::Mock;
}

EngineInfo MockRenderEngine::getEngineInfo() const {
    EngineInfo info;
    info.name = "Mock Render Engine";
    info.version = VSG_ABSTRACTION_VERSION;
    info.vendor = "VSG Abstraction";
    info.backend = RenderBackend::Mock;
    info.extensions = {"mock_extension_1", "mock_extension_2"};
    return info;
}

IDevice* MockRenderEngine::getDevice() {
    return nullptr; // Mock实现不提供设备
}

IInstance* MockRenderEngine::getInstance() {
    return nullptr; // Mock实现不提供实例
}

std::vector<IPhysicalDevice*> MockRenderEngine::getPhysicalDevices() {
    return {}; // Mock实现不提供物理设备
}

// ========== 场景图创建 ==========

ref_ptr<IGroup> MockRenderEngine::createGroup() {
    auto group = std::make_shared<MockGroup>();
    updateStatistics();
    return group;
}

ref_ptr<ITransform> MockRenderEngine::createTransform() {
    auto transform = std::make_shared<MockTransform>();
    updateStatistics();
    return transform;
}

ref_ptr<ITransform> MockRenderEngine::createMatrixTransform() {
    return createTransform();
}

ref_ptr<IGeometry> MockRenderEngine::createGeometry() {
    auto geometry = std::make_shared<MockGeometry>();
    updateStatistics();
    return geometry;
}

ref_ptr<IStateGroup> MockRenderEngine::createStateGroup() {
    auto stateGroup = std::make_shared<MockGroup>(); // 简化实现
    updateStatistics();
    return std::static_pointer_cast<IStateGroup>(stateGroup);
}

// ========== 其他接口的简化实现 ==========

ref_ptr<IWindow> MockRenderEngine::createWindow(const WindowTraits& traits) {
    (void)traits;
    std::cout << "[MOCK] Creating window: " << traits.windowTitle << std::endl;
    return nullptr; // Mock实现不创建真实窗口
}

ref_ptr<IRenderGraph> MockRenderEngine::createRenderGraph(ref_ptr<IWindow> window, ref_ptr<INode> view) {
    (void)window;
    (void)view;
    std::cout << "[MOCK] Creating render graph" << std::endl;
    return nullptr;
}

ref_ptr<IRecordTraversal> MockRenderEngine::createRecordTraversal() {
    std::cout << "[MOCK] Creating record traversal" << std::endl;
    return nullptr;
}

ref_ptr<IBuffer> MockRenderEngine::createBuffer(const BufferInfo& info) {
    std::cout << "[MOCK] Creating buffer: " << info.size << " bytes" << std::endl;
    return nullptr;
}

ref_ptr<IImage> MockRenderEngine::createImage(const ImageInfo& info) {
    std::cout << "[MOCK] Creating image: " << info.width << "x" << info.height << std::endl;
    return nullptr;
}

ref_ptr<IPipeline> MockRenderEngine::createGraphicsPipeline(const GraphicsPipelineInfo& info) {
    std::cout << "[MOCK] Creating graphics pipeline with " << info.shaders.size() << " shaders" << std::endl;
    return nullptr;
}

ref_ptr<IPipeline> MockRenderEngine::createComputePipeline(const ComputePipelineInfo& info) {
    (void)info;
    std::cout << "[MOCK] Creating compute pipeline" << std::endl;
    return nullptr;
}

void MockRenderEngine::waitIdle() {
    std::cout << "[MOCK] Waiting for idle..." << std::endl;
    std::this_thread::sleep_for(std::chrono::milliseconds(10));
}

RenderStatistics MockRenderEngine::getRenderStatistics() const {
    return statistics_;
}

void MockRenderEngine::resetRenderStatistics() {
    statistics_ = RenderStatistics{};
    std::cout << "[MOCK] Render statistics reset" << std::endl;
}

void MockRenderEngine::setErrorCallback(ErrorCallback callback) {
    errorCallback_ = callback;
}

void MockRenderEngine::setDebugCallback(DebugCallback callback) {
    debugCallback_ = callback;
}

void MockRenderEngine::beginDebugMarker(const std::string& name) {
    std::cout << "[MOCK] Begin debug marker: " << name << std::endl;
}

void MockRenderEngine::endDebugMarker() {
    std::cout << "[MOCK] End debug marker" << std::endl;
}

void* MockRenderEngine::getNativeHandle() const {
    return nullptr;
}

void MockRenderEngine::executeCustomCommand(std::function<void(void*)> command) {
    if (command) {
        std::cout << "[MOCK] Executing custom command" << std::endl;
        command(nullptr);
    }
}

bool MockRenderEngine::supportsFeature(const std::string& feature) const {
    std::cout << "[MOCK] Checking feature support: " << feature << std::endl;
    return feature == "mock_feature";
}

#ifdef VSG_ABSTRACTION_HAS_VSG
vsg::ref_ptr<vsg::Group> MockRenderEngine::createVSGGroup() {
    std::cout << "[MOCK] Mock backend cannot create real VSG objects" << std::endl;
    return nullptr;
}

vsg::ref_ptr<vsg::MatrixTransform> MockRenderEngine::createVSGTransform() {
    std::cout << "[MOCK] Mock backend cannot create real VSG objects" << std::endl;
    return nullptr;
}

vsg::ref_ptr<vsg::Geometry> MockRenderEngine::createVSGGeometry() {
    std::cout << "[MOCK] Mock backend cannot create real VSG objects" << std::endl;
    return nullptr;
}

vsg::ref_ptr<vsg::StateGroup> MockRenderEngine::createVSGStateGroup() {
    std::cout << "[MOCK] Mock backend cannot create real VSG objects" << std::endl;
    return nullptr;
}
#endif

void MockRenderEngine::updateStatistics() {
    statistics_.frameCount++;
    statistics_.memoryUsed += 1024; // 模拟内存使用增长
}

} // namespace vsg_abstraction
