#pragma once

/* <editor-fold desc="MIT License">

Copyright(c) 2024 Rocky Render Engine Refactor

Permission is hereby granted, free of charge, to any person obtaining a copy of this software and associated documentation files (the "Software"), to deal in the Software without restriction, including without limitation the rights to use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of the Software, and to permit persons to whom the Software is furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.

</editor-fold> */

#include <rocky/render/IRenderEngine.h>
#include <rocky/render/RenderTypes.h>
#include <memory>
#include <vector>

namespace rocky {
namespace render {

/**
 * @brief 渲染引擎工厂类
 * 
 * 负责创建不同类型的渲染引擎实例。
 * 支持运行时检测可用的渲染后端。
 */
class RenderEngineFactory {
public:
    /**
     * @brief 创建指定类型的渲染引擎
     * @param backend 渲染后端类型
     * @param config 可选的配置参数
     * @return 渲染引擎实例，失败返回nullptr
     */
    static std::unique_ptr<IRenderEngine> create(RenderBackend backend, const void* config = nullptr);
    
    /**
     * @brief 创建默认渲染引擎
     * 按优先级顺序尝试创建可用的渲染引擎
     * @return 渲染引擎实例，失败返回nullptr
     */
    static std::unique_ptr<IRenderEngine> createDefault();
    
    /**
     * @brief 获取所有支持的渲染后端
     * @return 支持的渲染后端列表
     */
    static std::vector<RenderBackend> getSupportedBackends();
    
    /**
     * @brief 检查指定后端是否支持
     * @param backend 渲染后端类型
     * @return 支持返回true
     */
    static bool isBackendSupported(RenderBackend backend);
    
    /**
     * @brief 获取推荐的渲染后端
     * 根据当前平台和硬件能力推荐最佳后端
     * @return 推荐的渲染后端
     */
    static RenderBackend getRecommendedBackend();
    
    /**
     * @brief 获取后端优先级列表
     * 按性能和兼容性排序的后端列表
     * @return 后端优先级列表
     */
    static std::vector<RenderBackend> getBackendPriority();

private:
    // 具体后端创建函数
    static std::unique_ptr<IRenderEngine> createVulkanEngine(const void* config);
    static std::unique_ptr<IRenderEngine> createWebGPUEngine(const void* config);
    static std::unique_ptr<IRenderEngine> createOpenGLEngine(const void* config);
    static std::unique_ptr<IRenderEngine> createMockEngine(const void* config);
    
    // 后端可用性检测
    static bool isVulkanAvailable();
    static bool isWebGPUAvailable();
    static bool isOpenGLAvailable();
    static bool isMockAvailable();
};

} // namespace render
} // namespace rocky
