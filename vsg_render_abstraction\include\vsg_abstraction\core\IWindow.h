#pragma once

/* <editor-fold desc="MIT License">

Copyright(c) 2024 VSG Render Engine Abstraction

Permission is hereby granted, free of charge, to any person obtaining a copy of this software and associated documentation files (the "Software"), to deal in the Software without restriction, including without limitation the rights to use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of the Software, and to permit persons to whom the Software is furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.

</editor-fold> */

#include <vsg_abstraction/core/Types.h>
#include <vsg_abstraction/core/Export.h>
#include <string>

namespace vsg_abstraction {

/**
 * @brief 窗口接口
 */
class IWindow {
public:
    virtual ~IWindow() = default;

    // 窗口属性
    virtual std::string getTitle() const = 0;
    virtual void setTitle(const std::string& title) = 0;
    virtual uint32_t getWidth() const = 0;
    virtual uint32_t getHeight() const = 0;
    virtual void resize(uint32_t width, uint32_t height) = 0;

    // 窗口状态
    virtual bool isVisible() const = 0;
    virtual void show() = 0;
    virtual void hide() = 0;
    virtual bool shouldClose() const = 0;
    virtual void close() = 0;

    // 事件处理
    virtual void pollEvents() = 0;
    virtual void waitEvents() = 0;

    // 原生句柄
    virtual void* getNativeHandle() const = 0;
};

/**
 * @brief 渲染图接口
 */
class IRenderGraph {
public:
    virtual ~IRenderGraph() = default;

    // 渲染控制
    virtual bool render() = 0;
    virtual void present() = 0;
    virtual void waitIdle() = 0;

    // 场景管理
    virtual void setScene(ref_ptr<INode> scene) = 0;
    virtual ref_ptr<INode> getScene() const = 0;

    // 原生句柄
    virtual void* getNativeHandle() const = 0;
};

/**
 * @brief 记录遍历接口
 */
class IRecordTraversal {
public:
    virtual ~IRecordTraversal() = default;

    // 遍历控制
    virtual void traverse(INode& node) = 0;
    virtual void reset() = 0;

    // 原生句柄
    virtual void* getNativeHandle() const = 0;
};

} // namespace vsg_abstraction
