/* <editor-fold desc="MIT License">

Copyright(c) 2024 VSG WebGPU Extension

Permission is hereby granted, free of charge, to any person obtaining a copy of this software and associated documentation files (the "Software"), to deal in the Software without restriction, including without limitation the rights to use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of the Software, and to permit persons to whom the Software is furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.

</editor-fold> */

#include <vsg_webgpu/vk/CommandBuffer.h>
#include <vsg_webgpu/vk/CommandPool.h>
#include <vsg_webgpu/vk/Device.h>

using namespace vsg_webgpu;

CommandBuffer::CommandBuffer(CommandPool* commandPool, WGPUCommandEncoder encoder) :
    deviceID(commandPool->getDevice()->deviceID),
    _device(commandPool->getDevice()),
    _commandPool(commandPool),
    _encoder(encoder)
{
    VSG_WEBGPU_LOG_DEBUG("CommandBuffer::CommandBuffer()");
    
    // 创建临时内存
    scratchMemory = vsg::ScratchMemory::create(1024 * 1024); // 1MB默认大小
}

CommandBuffer::~CommandBuffer()
{
    VSG_WEBGPU_LOG_DEBUG("CommandBuffer::~CommandBuffer()");
    
    // 确保渲染/计算通道已结束
    if (_inRenderPass)
    {
        endRenderPass();
    }
    if (_inComputePass)
    {
        endComputePass();
    }
}

WGPUCommandBuffer CommandBuffer::finish()
{
    if (_isFinished)
    {
        return _commandBuffer;
    }
    
    // 确保所有通道都已结束
    if (_inRenderPass)
    {
        endRenderPass();
    }
    if (_inComputePass)
    {
        endComputePass();
    }
    
#if !VSG_WEBGPU_USE_MOCK
    if (_encoder)
    {
        WGPUCommandBufferDescriptor desc = {};
        desc.label = "VSG WebGPU Command Buffer";
        _commandBuffer = _encoder.Finish(&desc);
        _isFinished = true;
    }
#endif
    
    return _commandBuffer;
}

void CommandBuffer::begin()
{
    if (_isRecording)
    {
        VSG_WEBGPU_LOG_WARN("CommandBuffer::begin() called while already recording");
        return;
    }
    
    _isRecording = true;
    _isFinished = false;
    
    VSG_WEBGPU_LOG_DEBUG("CommandBuffer::begin()");
}

void CommandBuffer::end()
{
    if (!_isRecording)
    {
        VSG_WEBGPU_LOG_WARN("CommandBuffer::end() called while not recording");
        return;
    }
    
    // 确保所有通道都已结束
    if (_inRenderPass)
    {
        endRenderPass();
    }
    if (_inComputePass)
    {
        endComputePass();
    }
    
    _isRecording = false;
    
    VSG_WEBGPU_LOG_DEBUG("CommandBuffer::end()");
}

void CommandBuffer::reset()
{
    if (_isRecording)
    {
        VSG_WEBGPU_LOG_WARN("CommandBuffer::reset() called while recording");
        return;
    }
    
    // 重置状态
    _isFinished = false;
    _inRenderPass = false;
    _inComputePass = false;
    _currentRenderPass = {};
    _currentComputePass = {};
    
    // 重置临时内存
    if (scratchMemory)
    {
        scratchMemory->reset();
    }
    
    VSG_WEBGPU_LOG_DEBUG("CommandBuffer::reset()");
}

WGPURenderPassEncoder CommandBuffer::beginRenderPass(const WGPURenderPassDescriptor& descriptor)
{
    if (_inRenderPass)
    {
        VSG_WEBGPU_LOG_ERROR("CommandBuffer::beginRenderPass() called while already in render pass");
        return {};
    }
    
    if (_inComputePass)
    {
        endComputePass();
    }
    
#if !VSG_WEBGPU_USE_MOCK
    _currentRenderPass = _encoder.BeginRenderPass(&descriptor);
    _inRenderPass = true;
#endif
    
    VSG_WEBGPU_LOG_DEBUG("CommandBuffer::beginRenderPass()");
    return _currentRenderPass;
}

void CommandBuffer::endRenderPass()
{
    if (!_inRenderPass)
    {
        VSG_WEBGPU_LOG_WARN("CommandBuffer::endRenderPass() called while not in render pass");
        return;
    }
    
#if !VSG_WEBGPU_USE_MOCK
    if (_currentRenderPass)
    {
        _currentRenderPass.End();
    }
#endif
    
    _currentRenderPass = {};
    _inRenderPass = false;
    
    VSG_WEBGPU_LOG_DEBUG("CommandBuffer::endRenderPass()");
}

WGPUComputePassEncoder CommandBuffer::beginComputePass(const WGPUComputePassDescriptor& descriptor)
{
    if (_inComputePass)
    {
        VSG_WEBGPU_LOG_ERROR("CommandBuffer::beginComputePass() called while already in compute pass");
        return {};
    }
    
    if (_inRenderPass)
    {
        endRenderPass();
    }
    
#if !VSG_WEBGPU_USE_MOCK
    _currentComputePass = _encoder.BeginComputePass(&descriptor);
    _inComputePass = true;
#endif
    
    VSG_WEBGPU_LOG_DEBUG("CommandBuffer::beginComputePass()");
    return _currentComputePass;
}

void CommandBuffer::endComputePass()
{
    if (!_inComputePass)
    {
        VSG_WEBGPU_LOG_WARN("CommandBuffer::endComputePass() called while not in compute pass");
        return;
    }
    
#if !VSG_WEBGPU_USE_MOCK
    if (_currentComputePass)
    {
        _currentComputePass.End();
    }
#endif
    
    _currentComputePass = {};
    _inComputePass = false;
    
    VSG_WEBGPU_LOG_DEBUG("CommandBuffer::endComputePass()");
}

void CommandBuffer::copyBufferToBuffer(WGPUBuffer source, uint64_t sourceOffset,
                                      WGPUBuffer destination, uint64_t destinationOffset,
                                      uint64_t size)
{
#if !VSG_WEBGPU_USE_MOCK
    _encoder.CopyBufferToBuffer(source, sourceOffset, destination, destinationOffset, size);
#endif
}

void CommandBuffer::copyBufferToTexture(const WGPUImageCopyBuffer& source,
                                       const WGPUImageCopyTexture& destination,
                                       const WGPUExtent3D& copySize)
{
#if !VSG_WEBGPU_USE_MOCK
    _encoder.CopyBufferToTexture(&source, &destination, &copySize);
#endif
}

void CommandBuffer::copyTextureToBuffer(const WGPUImageCopyTexture& source,
                                       const WGPUImageCopyBuffer& destination,
                                       const WGPUExtent3D& copySize)
{
#if !VSG_WEBGPU_USE_MOCK
    _encoder.CopyTextureToBuffer(&source, &destination, &copySize);
#endif
}

void CommandBuffer::copyTextureToTexture(const WGPUImageCopyTexture& source,
                                        const WGPUImageCopyTexture& destination,
                                        const WGPUExtent3D& copySize)
{
#if !VSG_WEBGPU_USE_MOCK
    _encoder.CopyTextureToTexture(&source, &destination, &copySize);
#endif
}

void CommandBuffer::pushDebugGroup(const char* groupLabel)
{
#if !VSG_WEBGPU_USE_MOCK
    _encoder.PushDebugGroup(groupLabel);
#endif
}

void CommandBuffer::popDebugGroup()
{
#if !VSG_WEBGPU_USE_MOCK
    _encoder.PopDebugGroup();
#endif
}

void CommandBuffer::insertDebugMarker(const char* markerLabel)
{
#if !VSG_WEBGPU_USE_MOCK
    _encoder.InsertDebugMarker(markerLabel);
#endif
}

void CommandBuffer::writeTimestamp(WGPUQuerySet querySet, uint32_t queryIndex)
{
#if !VSG_WEBGPU_USE_MOCK
    _encoder.WriteTimestamp(querySet, queryIndex);
#endif
}
