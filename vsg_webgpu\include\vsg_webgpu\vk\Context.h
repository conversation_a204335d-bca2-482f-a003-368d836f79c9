#pragma once

/* <editor-fold desc="MIT License">

Copyright(c) 2024 VSG WebGPU Extension

Permission is hereby granted, free of charge, to any person obtaining a copy of this software and associated documentation files (the "Software"), to deal in the Software without restriction, including without limitation the rights to use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of the Software, and to permit persons to whom the Software is furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.

</editor-fold> */

#include <vsg_webgpu/core/Export.h>
#include <vsg_webgpu/core/WebGPUHeaders.h>
#include <vsg/core/Inherit.h>
#include <vsg/core/Object.h>
#include <vsg/core/ref_ptr.h>
#include <vsg/vk/Context.h>
#include <vector>
#include <unordered_map>

namespace vsg_webgpu
{
    // forward declarations
    class Device;
    class CommandPool;
    class Queue;

    /// WebGPU编译上下文，对应VSG的Context类
    /// 用于管理资源编译和GPU对象创建
    class VSG_WEBGPU_DECLSPEC Context : public vsg::Inherit<vsg::Object, Context>
    {
    public:
        Context(Device* device, CommandPool* commandPool = nullptr);

        // 对应VSG Context的核心接口
        const uint32_t deviceID;
        uint32_t viewID = 0;
        vsg::Mask mask = vsg::MASK_ALL;

        // 设备和资源访问
        Device* device = nullptr;
        CommandPool* commandPool = nullptr;
        Queue* graphicsQueue = nullptr;

        // 编译状态
        enum CompileResult
        {
            COMPILE_NOT_REQUIRED = 0,
            COMPILE_REQUIRED = 1,
            COMPILE_FAILED = 2
        };

        // 资源编译接口
        template<typename T>
        CompileResult compile(vsg::ref_ptr<T> object)
        {
            if (!object) return COMPILE_FAILED;
            
            // 检查是否已编译
            if (isCompiled(object)) return COMPILE_NOT_REQUIRED;
            
            // 执行编译
            if (object->compile(*this))
            {
                markAsCompiled(object);
                return COMPILE_REQUIRED;
            }
            
            return COMPILE_FAILED;
        }

        // 编译状态管理
        bool isCompiled(vsg::ref_ptr<vsg::Object> object) const;
        void markAsCompiled(vsg::ref_ptr<vsg::Object> object);
        void markAsNotCompiled(vsg::ref_ptr<vsg::Object> object);

        // 资源创建辅助方法
        WGPUBuffer createBuffer(const WGPUBufferDescriptor& descriptor, const void* data = nullptr);
        WGPUTexture createTexture(const WGPUTextureDescriptor& descriptor);
        WGPUSampler createSampler(const WGPUSamplerDescriptor& descriptor);
        WGPUShaderModule createShaderModule(const std::string& wgslCode, const std::string& label = "");
        WGPUBindGroupLayout createBindGroupLayout(const WGPUBindGroupLayoutDescriptor& descriptor);
        WGPUBindGroup createBindGroup(const WGPUBindGroupDescriptor& descriptor);
        WGPUPipelineLayout createPipelineLayout(const WGPUPipelineLayoutDescriptor& descriptor);
        WGPURenderPipeline createRenderPipeline(const WGPURenderPipelineDescriptor& descriptor);
        WGPUComputePipeline createComputePipeline(const WGPUComputePipelineDescriptor& descriptor);

        // 数据传输
        void writeBuffer(WGPUBuffer buffer, uint64_t offset, const void* data, size_t size);
        void writeTexture(WGPUTexture texture, const void* data, size_t dataSize, 
                         uint32_t width, uint32_t height, uint32_t depth = 1);

        // 命令录制
        void record();

        // 等待完成
        void waitForCompletion();

        // 统计信息
        struct Statistics
        {
            uint32_t numBuffersCreated = 0;
            uint32_t numTexturesCreated = 0;
            uint32_t numPipelinesCreated = 0;
            uint32_t numBindGroupsCreated = 0;
            size_t totalMemoryAllocated = 0;
        };

        const Statistics& getStatistics() const { return _statistics; }
        void resetStatistics() { _statistics = {}; }

    protected:
        virtual ~Context();

    private:
        std::unordered_map<vsg::Object*, bool> _compiledObjects;
        Statistics _statistics;

        // 内部辅助方法
        void _updateStatistics(const std::string& resourceType, size_t size = 0);
    };

    VSG_type_name(vsg_webgpu::Context);

} // namespace vsg_webgpu
