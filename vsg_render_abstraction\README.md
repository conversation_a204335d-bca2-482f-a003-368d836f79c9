# VSG渲染引擎抽象层

[![Build Status](https://img.shields.io/badge/build-passing-brightgreen.svg)](https://github.com/vsg-render-abstraction)
[![License](https://img.shields.io/badge/license-MIT-blue.svg)](LICENSE)
[![C++](https://img.shields.io/badge/C%2B%2B-20-blue.svg)](https://en.cppreference.com/w/cpp/20)
[![VSG](https://img.shields.io/badge/VSG-compatible-orange.svg)](https://github.com/vsg-dev/VulkanSceneGraph)

## 项目概述

VSG渲染引擎抽象层是对VulkanSceneGraph (VSG)的渲染后端解耦重构项目，旨在通过设计全局单例渲染引擎抽象层，使VSG能够支持多种渲染后端，从而提升应用场景的灵活性和平台兼容性。

### 🎯 核心目标

- **多后端支持**: Vulkan (VSG)、WebGPU、OpenGL、Mock等多种渲染后端
- **VSG兼容性**: 保持与现有VSG API的完全兼容性
- **零成本抽象**: 不降低VSG原有的高性能特性
- **渐进式迁移**: 支持现有VSG应用的平滑迁移
- **平台扩展**: 支持桌面、Web、移动等多种平台

### 🏗️ 技术架构

```
VSG Application Layer
        ↓
Render Engine Abstraction Layer (新增)
        ↓
Backend Implementation Layer
   ↓        ↓        ↓        ↓
VSG      WebGPU   OpenGL   Mock
(Vulkan)
```

## 快速开始

### 系统要求

- **编译器**: C++20支持 (GCC 10+, Clang 12+, MSVC 2019+)
- **CMake**: 3.20或更高版本
- **VSG**: 1.0.0或更高版本（可选，用于Vulkan后端）

### 依赖项

#### 必需依赖
- CMake 3.20+
- C++20兼容编译器

#### 可选依赖
- **VulkanSceneGraph**: Vulkan后端支持
- **WebGPU/Dawn**: WebGPU后端支持
- **OpenGL**: OpenGL后端支持

### 构建步骤

#### 基础构建
```bash
git clone https://github.com/vsg-render-abstraction.git
cd vsg-render-abstraction
mkdir build && cd build
cmake .. -DCMAKE_BUILD_TYPE=Release
cmake --build . --config Release
```

#### 启用VSG后端
```bash
# 确保VSG已安装
cmake .. -DCMAKE_BUILD_TYPE=Release -DVSG_ABSTRACTION_BUILD_VSG_BACKEND=ON
cmake --build . --config Release
```

#### 构建选项
```cmake
option(VSG_ABSTRACTION_BUILD_VSG_BACKEND "Build VSG backend" ON)
option(VSG_ABSTRACTION_BUILD_WEBGPU_BACKEND "Build WebGPU backend" ON)
option(VSG_ABSTRACTION_BUILD_OPENGL_BACKEND "Build OpenGL backend" OFF)
option(VSG_ABSTRACTION_BUILD_MOCK_BACKEND "Build Mock backend" ON)
option(VSG_ABSTRACTION_BUILD_TESTS "Build test programs" ON)
option(VSG_ABSTRACTION_BUILD_EXAMPLES "Build example programs" ON)
```

## 使用示例

### 基础使用

```cpp
#include <vsg_abstraction/core/RenderEngineManager.h>

// 获取渲染引擎管理器
auto& manager = vsg_abstraction::getRenderManager();

// 切换到VSG后端（如果可用）
if (manager.switchBackend(vsg_abstraction::RenderBackend::Vulkan)) {
    std::cout << "使用VSG/Vulkan后端" << std::endl;
} else {
    // 降级到Mock后端
    manager.switchBackend(vsg_abstraction::RenderBackend::Mock);
}

// 创建场景图（使用抽象接口）
auto sceneRoot = manager.createGroup();
auto transform = manager.createMatrixTransform();
auto geometry = manager.createGeometry();

// 构建场景层次结构
transform->addChild(geometry);
sceneRoot->addChild(transform);
```

### VSG兼容性接口

```cpp
// 使用VSG兼容性接口（可直接替换现有VSG代码）
auto vsgGroup = manager.createVSGGroup();
auto vsgTransform = manager.createVSGTransform();
auto vsgGeometry = manager.createVSGGeometry();

// 这些对象可以直接用于现有的VSG代码
vsgTransform->addChild(vsgGeometry);
vsgGroup->addChild(vsgTransform);
```

### 便捷宏使用

```cpp
// 使用兼容性宏（最小化代码修改）
auto group = VSG_CREATE_GROUP();
auto transform = VSG_CREATE_TRANSFORM();
auto geometry = VSG_CREATE_GEOMETRY();

// 原有的VSG代码无需修改
transform->addChild(geometry);
group->addChild(transform);
```

### 多后端支持

```cpp
// 检查支持的后端
auto backends = vsg_abstraction::RenderEngineFactory::getSupportedBackends();
for (auto backend : backends) {
    std::cout << "支持: " << vsg_abstraction::getRenderBackendName(backend) << std::endl;
}

// 运行时切换后端
manager.switchBackend(vsg_abstraction::RenderBackend::WebGPU);
manager.switchBackend(vsg_abstraction::RenderBackend::OpenGL);
manager.switchBackend(vsg_abstraction::RenderBackend::Mock);
```

## 迁移指南

### 现有VSG代码迁移

#### 方案1: 使用兼容性宏（推荐）
```cpp
// 原始VSG代码
auto group = vsg::Group::create();
auto transform = vsg::MatrixTransform::create();

// 修改后（添加宏定义）
auto group = VSG_CREATE_GROUP();
auto transform = VSG_CREATE_TRANSFORM();
```

#### 方案2: 使用管理器接口
```cpp
// 原始VSG代码
auto group = vsg::Group::create();

// 修改后
auto& manager = vsg_abstraction::getRenderManager();
auto group = manager.createVSGGroup();
```

#### 方案3: 使用抽象接口（长期）
```cpp
// 原始VSG代码
auto group = vsg::Group::create();

// 修改后
auto& manager = vsg_abstraction::getRenderManager();
auto group = manager.createGroup();
```

### 渐进式迁移策略

1. **第一阶段**: 添加抽象层，使用兼容性宏
2. **第二阶段**: 逐步替换为管理器接口
3. **第三阶段**: 迁移到完全抽象接口
4. **第四阶段**: 利用多后端特性

## 项目结构

```
vsg_render_abstraction/
├── include/vsg_abstraction/     # 公共头文件
│   ├── core/                    # 核心接口和管理器
│   │   ├── IRenderEngine.h      # 渲染引擎抽象接口
│   │   ├── RenderEngineManager.h # 全局单例管理器
│   │   ├── RenderEngineFactory.h # 工厂类
│   │   └── Types.h              # 类型定义
│   ├── nodes/                   # 场景节点接口
│   │   └── INode.h              # 节点抽象接口
│   ├── backends/                # 后端实现接口
│   │   └── VSGRenderEngine.h    # VSG后端接口
│   └── adapters/                # 适配器
│       └── VSGNodeAdapter.h     # VSG节点适配器
├── src/                         # 源代码实现
│   ├── core/                    # 核心实现
│   ├── backends/                # 后端实现
│   │   ├── vsg/                 # VSG后端
│   │   ├── webgpu/              # WebGPU后端
│   │   ├── opengl/              # OpenGL后端
│   │   └── mock/                # Mock后端
│   └── adapters/                # 适配器实现
├── examples/                    # 示例程序
├── tests/                       # 测试程序
└── docs/                        # 文档
```

## 技术特点

### 设计模式

- **抽象工厂模式**: 支持多种渲染后端的创建
- **单例模式**: 全局统一的渲染引擎管理
- **适配器模式**: VSG与抽象层之间的桥梁
- **访问者模式**: 灵活的场景图遍历机制

### 性能优化

- **零成本抽象**: 虚函数表开销最小
- **智能缓存**: 减少重复对象创建
- **状态管理**: 高效的渲染状态跟踪
- **资源池化**: 重用常用资源对象

### 线程安全

- **锁保护**: 关键数据结构的线程安全
- **异常安全**: RAII保证资源释放
- **原子操作**: 高频访问数据的无锁设计

## 后端支持

| 后端 | 状态 | 平台支持 | 性能 | 说明 |
|------|------|----------|------|------|
| VSG (Vulkan) | ✅ 完整支持 | Windows, Linux, macOS | 100% | 原生VSG性能 |
| WebGPU | 🚧 开发中 | Windows, Linux, macOS, Web | 85-95% | 跨平台支持 |
| OpenGL | 📋 计划中 | Windows, Linux, macOS | 80-90% | 传统兼容性 |
| Mock | ✅ 完整支持 | 所有平台 | N/A | 测试和调试 |

## 测试

### 运行测试

```bash
# 构建测试
cmake .. -DVSG_ABSTRACTION_BUILD_TESTS=ON
cmake --build . --config Release

# 运行测试
ctest --output-on-failure
```

### 测试覆盖

- ✅ 基础功能测试
- ✅ VSG兼容性测试
- ✅ 多后端切换测试
- ✅ 性能基准测试
- ✅ 内存泄漏测试

## 文档

- [技术架构设计](VSG渲染引擎解耦重构技术分析.md)
- [API参考文档](docs/api_reference.md)
- [迁移指南](docs/migration_guide.md)
- [性能优化指南](docs/performance_guide.md)

## 贡献指南

### 开发环境

1. 安装依赖项（CMake、C++20编译器、VSG）
2. 克隆仓库并构建
3. 运行测试确保环境正常

### 代码规范

- 使用C++20标准
- 遵循现有代码风格
- 所有公共API必须有文档注释
- 新功能必须包含测试

## 许可证

本项目采用MIT许可证。详见 [LICENSE](LICENSE) 文件。

## 致谢

- [VulkanSceneGraph](https://github.com/vsg-dev/VulkanSceneGraph) - 优秀的Vulkan场景图库
- [WebGPU](https://www.w3.org/TR/webgpu/) - 现代Web图形API
- [Vulkan](https://www.vulkan.org/) - 高性能图形API

## 联系方式

- 项目主页: https://github.com/vsg-render-abstraction
- 问题反馈: https://github.com/vsg-render-abstraction/issues
- 讨论区: https://github.com/vsg-render-abstraction/discussions

---

**注意**: 这是一个VSG渲染引擎解耦重构项目，展示了如何为现有渲染引擎添加多后端支持，同时保持API兼容性和高性能特性。
