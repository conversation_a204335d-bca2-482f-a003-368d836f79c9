/* <editor-fold desc="MIT License">

Copyright(c) 2024 VSG Render Engine Abstraction

Permission is hereby granted, free of charge, to any person obtaining a copy of this software and associated documentation files (the "Software"), to deal in the Software without restriction, including without limitation the rights to use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of the Software, and to permit persons to whom the Software is furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.

</editor-fold> */

#include <vsg_abstraction/core/Types.h>

namespace vsg_abstraction
{

    // ========== 枚举类型转换函数 ==========
    // 注意：getRenderBackendName函数已在Types.h中定义为内联函数

    std::string getNodeTypeName(NodeType type)
    {
        switch (type)
        {
        case NodeType::Node:
            return "Node";
        case NodeType::Group:
            return "Group";
        case NodeType::Transform:
            return "Transform";
        case NodeType::Geometry:
            return "Geometry";
        case NodeType::StateGroup:
            return "StateGroup";
        case NodeType::Light:
            return "Light";
        case NodeType::Camera:
            return "Camera";
        default:
            return "Unknown";
        }
    }

    std::string getShaderStageName(ShaderStage stage)
    {
        switch (stage)
        {
        case ShaderStage::Vertex:
            return "Vertex";
        case ShaderStage::Fragment:
            return "Fragment";
        case ShaderStage::Geometry:
            return "Geometry";
        case ShaderStage::TessellationControl:
            return "TessellationControl";
        case ShaderStage::TessellationEvaluation:
            return "TessellationEvaluation";
        case ShaderStage::Compute:
            return "Compute";
        default:
            return "Unknown";
        }
    }

    std::string getPrimitiveTopologyName(PrimitiveTopology topology)
    {
        switch (topology)
        {
        case PrimitiveTopology::PointList:
            return "PointList";
        case PrimitiveTopology::LineList:
            return "LineList";
        case PrimitiveTopology::LineStrip:
            return "LineStrip";
        case PrimitiveTopology::TriangleList:
            return "TriangleList";
        case PrimitiveTopology::TriangleStrip:
            return "TriangleStrip";
        case PrimitiveTopology::TriangleFan:
            return "TriangleFan";
        default:
            return "Unknown";
        }
    }

    std::string getBufferTypeName(BufferType type)
    {
        switch (type)
        {
        case BufferType::Vertex:
            return "Vertex";
        case BufferType::Index:
            return "Index";
        case BufferType::Uniform:
            return "Uniform";
        case BufferType::Storage:
            return "Storage";
        case BufferType::Staging:
            return "Staging";
        default:
            return "Unknown";
        }
    }

    std::string getImageFormatName(ImageFormat format)
    {
        switch (format)
        {
        case ImageFormat::RGBA8:
            return "RGBA8";
        case ImageFormat::RGBA16F:
            return "RGBA16F";
        case ImageFormat::RGBA32F:
            return "RGBA32F";
        case ImageFormat::RGB8:
            return "RGB8";
        case ImageFormat::RG8:
            return "RG8";
        case ImageFormat::R8:
            return "R8";
        case ImageFormat::Depth24Stencil8:
            return "Depth24Stencil8";
        case ImageFormat::Depth32F:
            return "Depth32F";
        default:
            return "Unknown";
        }
    }

    // ========== 数学工具函数 ==========

    mat4 createIdentityMatrix()
    {
        mat4 result = {};
        result[0][0] = 1.0f;
        result[1][1] = 1.0f;
        result[2][2] = 1.0f;
        result[3][3] = 1.0f;
        return result;
    }

    mat4 createTranslationMatrix(const vec3& translation)
    {
        mat4 result = createIdentityMatrix();
        result[3][0] = translation[0];
        result[3][1] = translation[1];
        result[3][2] = translation[2];
        return result;
    }

    mat4 createScaleMatrix(const vec3& scale)
    {
        mat4 result = createIdentityMatrix();
        result[0][0] = scale[0];
        result[1][1] = scale[1];
        result[2][2] = scale[2];
        return result;
    }

    mat4 createRotationMatrix(const vec4& rotation)
    {
        // 假设rotation是四元数 (x, y, z, w)
        float x = rotation[0];
        float y = rotation[1];
        float z = rotation[2];
        float w = rotation[3];

        mat4 result = {};

        float xx = x * x;
        float yy = y * y;
        float zz = z * z;
        float xy = x * y;
        float xz = x * z;
        float yz = y * z;
        float wx = w * x;
        float wy = w * y;
        float wz = w * z;

        result[0][0] = 1.0f - 2.0f * (yy + zz);
        result[0][1] = 2.0f * (xy + wz);
        result[0][2] = 2.0f * (xz - wy);
        result[0][3] = 0.0f;

        result[1][0] = 2.0f * (xy - wz);
        result[1][1] = 1.0f - 2.0f * (xx + zz);
        result[1][2] = 2.0f * (yz + wx);
        result[1][3] = 0.0f;

        result[2][0] = 2.0f * (xz + wy);
        result[2][1] = 2.0f * (yz - wx);
        result[2][2] = 1.0f - 2.0f * (xx + yy);
        result[2][3] = 0.0f;

        result[3][0] = 0.0f;
        result[3][1] = 0.0f;
        result[3][2] = 0.0f;
        result[3][3] = 1.0f;

        return result;
    }

    mat4 multiplyMatrices(const mat4& a, const mat4& b)
    {
        mat4 result = {};

        for (int i = 0; i < 4; ++i)
        {
            for (int j = 0; j < 4; ++j)
            {
                result[i][j] = 0.0f;
                for (int k = 0; k < 4; ++k)
                {
                    result[i][j] += a[i][k] * b[k][j];
                }
            }
        }

        return result;
    }

    vec3 transformPoint(const mat4& matrix, const vec3& point)
    {
        vec4 homogeneous = {point[0], point[1], point[2], 1.0f};

        vec4 result = {};
        for (int i = 0; i < 4; ++i)
        {
            result[i] = 0.0f;
            for (int j = 0; j < 4; ++j)
            {
                result[i] += matrix[i][j] * homogeneous[j];
            }
        }

        // 透视除法
        if (result[3] != 0.0f)
        {
            return {result[0] / result[3], result[1] / result[3], result[2] / result[3]};
        }
        else
        {
            return {result[0], result[1], result[2]};
        }
    }

    vec3 transformVector(const mat4& matrix, const vec3& vector)
    {
        vec4 homogeneous = {vector[0], vector[1], vector[2], 0.0f};

        vec4 result = {};
        for (int i = 0; i < 4; ++i)
        {
            result[i] = 0.0f;
            for (int j = 0; j < 4; ++j)
            {
                result[i] += matrix[i][j] * homogeneous[j];
            }
        }

        return {result[0], result[1], result[2]};
    }

    float dotProduct(const vec3& a, const vec3& b)
    {
        return a[0] * b[0] + a[1] * b[1] + a[2] * b[2];
    }

    vec3 crossProduct(const vec3& a, const vec3& b)
    {
        return {
            a[1] * b[2] - a[2] * b[1],
            a[2] * b[0] - a[0] * b[2],
            a[0] * b[1] - a[1] * b[0]};
    }

    float vectorLength(const vec3& v)
    {
        return std::sqrt(v[0] * v[0] + v[1] * v[1] + v[2] * v[2]);
    }

    vec3 normalizeVector(const vec3& v)
    {
        float length = vectorLength(v);
        if (length > 0.0f)
        {
            return {v[0] / length, v[1] / length, v[2] / length};
        }
        else
        {
            return {0.0f, 0.0f, 0.0f};
        }
    }

    // ========== 配置结构体默认值 ==========

    // RenderEngineConfig构造函数已移除，使用默认初始化

    // 构造函数已移除，使用默认初始化或聚合初始化

} // namespace vsg_abstraction
