/* <editor-fold desc="MIT License">

Copyright(c) 2024 VSG Render Engine Abstraction

Permission is hereby granted, free of charge, to any person obtaining a copy of this software and associated documentation files (the "Software"), to deal in the Software without restriction, including without limitation the rights to use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of the Software, and to permit persons to whom the Software is furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.

</editor-fold> */

#include <iostream>
#include <vsg_abstraction/core/Config.h>
#include <vsg_abstraction/core/RenderEngineManager.h>

using namespace vsg_abstraction;

int main()
{
    std::cout << "=== Scene Graph Test ===" << std::endl;

    auto& manager = getRenderManager();

    if (!manager.switchBackend(RenderBackend::Mock))
    {
        std::cout << "Failed to switch to Mock backend" << std::endl;
        return 1;
    }

    // Check if engine exists
    std::cout << "Has engine: " << (manager.hasEngine() ? "yes" : "no") << std::endl;
    std::cout << "Engine pointer: " << manager.getEngine() << std::endl;

    // Create scene graph hierarchy
    std::cout << "Creating Group..." << std::endl;

    // Try direct engine call
    auto engine = manager.getEngine();
    std::cout << "Direct engine call on engine at " << engine << std::endl;
    auto directGroup = engine->createGroup();
    std::cout << "Direct Group created: " << (directGroup ? "success" : "failed") << std::endl;

    auto root = manager.createGroup();
    std::cout << "Group created: " << (root ? "success" : "failed") << std::endl;

    std::cout << "Creating Transform..." << std::endl;
    auto transform = manager.createTransform();
    std::cout << "Transform created: " << (transform ? "success" : "failed") << std::endl;

    std::cout << "Creating Geometry..." << std::endl;
    auto geometry = manager.createGeometry();
    std::cout << "Geometry created: " << (geometry ? "success" : "failed") << std::endl;

    if (!root || !transform || !geometry)
    {
        std::cout << "Failed to create scene graph objects" << std::endl;
        return 1;
    }

    // Build hierarchy
    transform->addChild(geometry);
    root->addChild(transform);

    // Test hierarchy
    if (root->getNumChildren() == 1)
    {
        std::cout << "Root has correct number of children" << std::endl;
    }
    else
    {
        std::cout << "Root has incorrect number of children" << std::endl;
        return 1;
    }

    if (transform->getNumChildren() == 1)
    {
        std::cout << "Transform has correct number of children" << std::endl;
    }
    else
    {
        std::cout << "Transform has incorrect number of children" << std::endl;
        return 1;
    }

    // Test node properties
    root->setName("RootNode");
    if (root->getName() == "RootNode")
    {
        std::cout << "Node naming works correctly" << std::endl;
    }
    else
    {
        std::cout << "Node naming failed" << std::endl;
        return 1;
    }

    std::cout << "Scene graph test completed!" << std::endl;
    return 0;
}
