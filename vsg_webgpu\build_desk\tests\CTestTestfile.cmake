# CMake generated Testfile for 
# Source directory: F:/cmo-dev/my_osgearth_web/osgearth_origin/VulkanSceneGraph/vsg_webgpu/tests
# Build directory: F:/cmo-dev/my_osgearth_web/osgearth_origin/VulkanSceneGraph/vsg_webgpu/build_desk/tests
# 
# This file includes the relevant testing commands required for 
# testing this directory and lists subdirectories to be tested as well.
if(CTEST_CONFIGURATION_TYPE MATCHES "^([Dd][Ee][Bb][Uu][Gg])$")
  add_test([=[DeviceTest]=] "F:/cmo-dev/my_osgearth_web/osgearth_origin/VulkanSceneGraph/vsg_webgpu/build_desk/bin/Debug/test_device.exe")
  set_tests_properties([=[DeviceTest]=] PROPERTIES  TIMEOUT "30" _BACKTRACE_TRIPLES "F:/cmo-dev/my_osgearth_web/osgearth_origin/VulkanSceneGraph/vsg_webgpu/tests/CMakeLists.txt;70;add_test;F:/cmo-dev/my_osgearth_web/osgearth_origin/VulkanSceneGraph/vsg_webgpu/tests/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Rr][Ee][Ll][Ee][Aa][Ss][Ee])$")
  add_test([=[DeviceTest]=] "F:/cmo-dev/my_osgearth_web/osgearth_origin/VulkanSceneGraph/vsg_webgpu/build_desk/bin/Release/test_device.exe")
  set_tests_properties([=[DeviceTest]=] PROPERTIES  TIMEOUT "30" _BACKTRACE_TRIPLES "F:/cmo-dev/my_osgearth_web/osgearth_origin/VulkanSceneGraph/vsg_webgpu/tests/CMakeLists.txt;70;add_test;F:/cmo-dev/my_osgearth_web/osgearth_origin/VulkanSceneGraph/vsg_webgpu/tests/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Mm][Ii][Nn][Ss][Ii][Zz][Ee][Rr][Ee][Ll])$")
  add_test([=[DeviceTest]=] "F:/cmo-dev/my_osgearth_web/osgearth_origin/VulkanSceneGraph/vsg_webgpu/build_desk/bin/MinSizeRel/test_device.exe")
  set_tests_properties([=[DeviceTest]=] PROPERTIES  TIMEOUT "30" _BACKTRACE_TRIPLES "F:/cmo-dev/my_osgearth_web/osgearth_origin/VulkanSceneGraph/vsg_webgpu/tests/CMakeLists.txt;70;add_test;F:/cmo-dev/my_osgearth_web/osgearth_origin/VulkanSceneGraph/vsg_webgpu/tests/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Rr][Ee][Ll][Ww][Ii][Tt][Hh][Dd][Ee][Bb][Ii][Nn][Ff][Oo])$")
  add_test([=[DeviceTest]=] "F:/cmo-dev/my_osgearth_web/osgearth_origin/VulkanSceneGraph/vsg_webgpu/build_desk/bin/RelWithDebInfo/test_device.exe")
  set_tests_properties([=[DeviceTest]=] PROPERTIES  TIMEOUT "30" _BACKTRACE_TRIPLES "F:/cmo-dev/my_osgearth_web/osgearth_origin/VulkanSceneGraph/vsg_webgpu/tests/CMakeLists.txt;70;add_test;F:/cmo-dev/my_osgearth_web/osgearth_origin/VulkanSceneGraph/vsg_webgpu/tests/CMakeLists.txt;0;")
else()
  add_test([=[DeviceTest]=] NOT_AVAILABLE)
endif()
if(CTEST_CONFIGURATION_TYPE MATCHES "^([Dd][Ee][Bb][Uu][Gg])$")
  add_test([=[TriangleTest]=] "F:/cmo-dev/my_osgearth_web/osgearth_origin/VulkanSceneGraph/vsg_webgpu/build_desk/bin/Debug/test_triangle.exe")
  set_tests_properties([=[TriangleTest]=] PROPERTIES  TIMEOUT "60" _BACKTRACE_TRIPLES "F:/cmo-dev/my_osgearth_web/osgearth_origin/VulkanSceneGraph/vsg_webgpu/tests/CMakeLists.txt;71;add_test;F:/cmo-dev/my_osgearth_web/osgearth_origin/VulkanSceneGraph/vsg_webgpu/tests/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Rr][Ee][Ll][Ee][Aa][Ss][Ee])$")
  add_test([=[TriangleTest]=] "F:/cmo-dev/my_osgearth_web/osgearth_origin/VulkanSceneGraph/vsg_webgpu/build_desk/bin/Release/test_triangle.exe")
  set_tests_properties([=[TriangleTest]=] PROPERTIES  TIMEOUT "60" _BACKTRACE_TRIPLES "F:/cmo-dev/my_osgearth_web/osgearth_origin/VulkanSceneGraph/vsg_webgpu/tests/CMakeLists.txt;71;add_test;F:/cmo-dev/my_osgearth_web/osgearth_origin/VulkanSceneGraph/vsg_webgpu/tests/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Mm][Ii][Nn][Ss][Ii][Zz][Ee][Rr][Ee][Ll])$")
  add_test([=[TriangleTest]=] "F:/cmo-dev/my_osgearth_web/osgearth_origin/VulkanSceneGraph/vsg_webgpu/build_desk/bin/MinSizeRel/test_triangle.exe")
  set_tests_properties([=[TriangleTest]=] PROPERTIES  TIMEOUT "60" _BACKTRACE_TRIPLES "F:/cmo-dev/my_osgearth_web/osgearth_origin/VulkanSceneGraph/vsg_webgpu/tests/CMakeLists.txt;71;add_test;F:/cmo-dev/my_osgearth_web/osgearth_origin/VulkanSceneGraph/vsg_webgpu/tests/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Rr][Ee][Ll][Ww][Ii][Tt][Hh][Dd][Ee][Bb][Ii][Nn][Ff][Oo])$")
  add_test([=[TriangleTest]=] "F:/cmo-dev/my_osgearth_web/osgearth_origin/VulkanSceneGraph/vsg_webgpu/build_desk/bin/RelWithDebInfo/test_triangle.exe")
  set_tests_properties([=[TriangleTest]=] PROPERTIES  TIMEOUT "60" _BACKTRACE_TRIPLES "F:/cmo-dev/my_osgearth_web/osgearth_origin/VulkanSceneGraph/vsg_webgpu/tests/CMakeLists.txt;71;add_test;F:/cmo-dev/my_osgearth_web/osgearth_origin/VulkanSceneGraph/vsg_webgpu/tests/CMakeLists.txt;0;")
else()
  add_test([=[TriangleTest]=] NOT_AVAILABLE)
endif()
if(CTEST_CONFIGURATION_TYPE MATCHES "^([Dd][Ee][Bb][Uu][Gg])$")
  add_test([=[StateTest]=] "F:/cmo-dev/my_osgearth_web/osgearth_origin/VulkanSceneGraph/vsg_webgpu/build_desk/bin/Debug/test_state.exe")
  set_tests_properties([=[StateTest]=] PROPERTIES  TIMEOUT "30" _BACKTRACE_TRIPLES "F:/cmo-dev/my_osgearth_web/osgearth_origin/VulkanSceneGraph/vsg_webgpu/tests/CMakeLists.txt;72;add_test;F:/cmo-dev/my_osgearth_web/osgearth_origin/VulkanSceneGraph/vsg_webgpu/tests/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Rr][Ee][Ll][Ee][Aa][Ss][Ee])$")
  add_test([=[StateTest]=] "F:/cmo-dev/my_osgearth_web/osgearth_origin/VulkanSceneGraph/vsg_webgpu/build_desk/bin/Release/test_state.exe")
  set_tests_properties([=[StateTest]=] PROPERTIES  TIMEOUT "30" _BACKTRACE_TRIPLES "F:/cmo-dev/my_osgearth_web/osgearth_origin/VulkanSceneGraph/vsg_webgpu/tests/CMakeLists.txt;72;add_test;F:/cmo-dev/my_osgearth_web/osgearth_origin/VulkanSceneGraph/vsg_webgpu/tests/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Mm][Ii][Nn][Ss][Ii][Zz][Ee][Rr][Ee][Ll])$")
  add_test([=[StateTest]=] "F:/cmo-dev/my_osgearth_web/osgearth_origin/VulkanSceneGraph/vsg_webgpu/build_desk/bin/MinSizeRel/test_state.exe")
  set_tests_properties([=[StateTest]=] PROPERTIES  TIMEOUT "30" _BACKTRACE_TRIPLES "F:/cmo-dev/my_osgearth_web/osgearth_origin/VulkanSceneGraph/vsg_webgpu/tests/CMakeLists.txt;72;add_test;F:/cmo-dev/my_osgearth_web/osgearth_origin/VulkanSceneGraph/vsg_webgpu/tests/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Rr][Ee][Ll][Ww][Ii][Tt][Hh][Dd][Ee][Bb][Ii][Nn][Ff][Oo])$")
  add_test([=[StateTest]=] "F:/cmo-dev/my_osgearth_web/osgearth_origin/VulkanSceneGraph/vsg_webgpu/build_desk/bin/RelWithDebInfo/test_state.exe")
  set_tests_properties([=[StateTest]=] PROPERTIES  TIMEOUT "30" _BACKTRACE_TRIPLES "F:/cmo-dev/my_osgearth_web/osgearth_origin/VulkanSceneGraph/vsg_webgpu/tests/CMakeLists.txt;72;add_test;F:/cmo-dev/my_osgearth_web/osgearth_origin/VulkanSceneGraph/vsg_webgpu/tests/CMakeLists.txt;0;")
else()
  add_test([=[StateTest]=] NOT_AVAILABLE)
endif()
if(CTEST_CONFIGURATION_TYPE MATCHES "^([Dd][Ee][Bb][Uu][Gg])$")
  add_test([=[PipelineTest]=] "F:/cmo-dev/my_osgearth_web/osgearth_origin/VulkanSceneGraph/vsg_webgpu/build_desk/bin/Debug/test_pipeline.exe")
  set_tests_properties([=[PipelineTest]=] PROPERTIES  TIMEOUT "30" _BACKTRACE_TRIPLES "F:/cmo-dev/my_osgearth_web/osgearth_origin/VulkanSceneGraph/vsg_webgpu/tests/CMakeLists.txt;73;add_test;F:/cmo-dev/my_osgearth_web/osgearth_origin/VulkanSceneGraph/vsg_webgpu/tests/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Rr][Ee][Ll][Ee][Aa][Ss][Ee])$")
  add_test([=[PipelineTest]=] "F:/cmo-dev/my_osgearth_web/osgearth_origin/VulkanSceneGraph/vsg_webgpu/build_desk/bin/Release/test_pipeline.exe")
  set_tests_properties([=[PipelineTest]=] PROPERTIES  TIMEOUT "30" _BACKTRACE_TRIPLES "F:/cmo-dev/my_osgearth_web/osgearth_origin/VulkanSceneGraph/vsg_webgpu/tests/CMakeLists.txt;73;add_test;F:/cmo-dev/my_osgearth_web/osgearth_origin/VulkanSceneGraph/vsg_webgpu/tests/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Mm][Ii][Nn][Ss][Ii][Zz][Ee][Rr][Ee][Ll])$")
  add_test([=[PipelineTest]=] "F:/cmo-dev/my_osgearth_web/osgearth_origin/VulkanSceneGraph/vsg_webgpu/build_desk/bin/MinSizeRel/test_pipeline.exe")
  set_tests_properties([=[PipelineTest]=] PROPERTIES  TIMEOUT "30" _BACKTRACE_TRIPLES "F:/cmo-dev/my_osgearth_web/osgearth_origin/VulkanSceneGraph/vsg_webgpu/tests/CMakeLists.txt;73;add_test;F:/cmo-dev/my_osgearth_web/osgearth_origin/VulkanSceneGraph/vsg_webgpu/tests/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Rr][Ee][Ll][Ww][Ii][Tt][Hh][Dd][Ee][Bb][Ii][Nn][Ff][Oo])$")
  add_test([=[PipelineTest]=] "F:/cmo-dev/my_osgearth_web/osgearth_origin/VulkanSceneGraph/vsg_webgpu/build_desk/bin/RelWithDebInfo/test_pipeline.exe")
  set_tests_properties([=[PipelineTest]=] PROPERTIES  TIMEOUT "30" _BACKTRACE_TRIPLES "F:/cmo-dev/my_osgearth_web/osgearth_origin/VulkanSceneGraph/vsg_webgpu/tests/CMakeLists.txt;73;add_test;F:/cmo-dev/my_osgearth_web/osgearth_origin/VulkanSceneGraph/vsg_webgpu/tests/CMakeLists.txt;0;")
else()
  add_test([=[PipelineTest]=] NOT_AVAILABLE)
endif()
if(CTEST_CONFIGURATION_TYPE MATCHES "^([Dd][Ee][Bb][Uu][Gg])$")
  add_test([=[SceneGraphTest]=] "F:/cmo-dev/my_osgearth_web/osgearth_origin/VulkanSceneGraph/vsg_webgpu/build_desk/bin/Debug/test_scenegraph.exe")
  set_tests_properties([=[SceneGraphTest]=] PROPERTIES  TIMEOUT "60" _BACKTRACE_TRIPLES "F:/cmo-dev/my_osgearth_web/osgearth_origin/VulkanSceneGraph/vsg_webgpu/tests/CMakeLists.txt;74;add_test;F:/cmo-dev/my_osgearth_web/osgearth_origin/VulkanSceneGraph/vsg_webgpu/tests/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Rr][Ee][Ll][Ee][Aa][Ss][Ee])$")
  add_test([=[SceneGraphTest]=] "F:/cmo-dev/my_osgearth_web/osgearth_origin/VulkanSceneGraph/vsg_webgpu/build_desk/bin/Release/test_scenegraph.exe")
  set_tests_properties([=[SceneGraphTest]=] PROPERTIES  TIMEOUT "60" _BACKTRACE_TRIPLES "F:/cmo-dev/my_osgearth_web/osgearth_origin/VulkanSceneGraph/vsg_webgpu/tests/CMakeLists.txt;74;add_test;F:/cmo-dev/my_osgearth_web/osgearth_origin/VulkanSceneGraph/vsg_webgpu/tests/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Mm][Ii][Nn][Ss][Ii][Zz][Ee][Rr][Ee][Ll])$")
  add_test([=[SceneGraphTest]=] "F:/cmo-dev/my_osgearth_web/osgearth_origin/VulkanSceneGraph/vsg_webgpu/build_desk/bin/MinSizeRel/test_scenegraph.exe")
  set_tests_properties([=[SceneGraphTest]=] PROPERTIES  TIMEOUT "60" _BACKTRACE_TRIPLES "F:/cmo-dev/my_osgearth_web/osgearth_origin/VulkanSceneGraph/vsg_webgpu/tests/CMakeLists.txt;74;add_test;F:/cmo-dev/my_osgearth_web/osgearth_origin/VulkanSceneGraph/vsg_webgpu/tests/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Rr][Ee][Ll][Ww][Ii][Tt][Hh][Dd][Ee][Bb][Ii][Nn][Ff][Oo])$")
  add_test([=[SceneGraphTest]=] "F:/cmo-dev/my_osgearth_web/osgearth_origin/VulkanSceneGraph/vsg_webgpu/build_desk/bin/RelWithDebInfo/test_scenegraph.exe")
  set_tests_properties([=[SceneGraphTest]=] PROPERTIES  TIMEOUT "60" _BACKTRACE_TRIPLES "F:/cmo-dev/my_osgearth_web/osgearth_origin/VulkanSceneGraph/vsg_webgpu/tests/CMakeLists.txt;74;add_test;F:/cmo-dev/my_osgearth_web/osgearth_origin/VulkanSceneGraph/vsg_webgpu/tests/CMakeLists.txt;0;")
else()
  add_test([=[SceneGraphTest]=] NOT_AVAILABLE)
endif()
if(CTEST_CONFIGURATION_TYPE MATCHES "^([Dd][Ee][Bb][Uu][Gg])$")
  add_test([=[PerformanceTest]=] "F:/cmo-dev/my_osgearth_web/osgearth_origin/VulkanSceneGraph/vsg_webgpu/build_desk/bin/Debug/test_performance.exe")
  set_tests_properties([=[PerformanceTest]=] PROPERTIES  TIMEOUT "120" _BACKTRACE_TRIPLES "F:/cmo-dev/my_osgearth_web/osgearth_origin/VulkanSceneGraph/vsg_webgpu/tests/CMakeLists.txt;75;add_test;F:/cmo-dev/my_osgearth_web/osgearth_origin/VulkanSceneGraph/vsg_webgpu/tests/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Rr][Ee][Ll][Ee][Aa][Ss][Ee])$")
  add_test([=[PerformanceTest]=] "F:/cmo-dev/my_osgearth_web/osgearth_origin/VulkanSceneGraph/vsg_webgpu/build_desk/bin/Release/test_performance.exe")
  set_tests_properties([=[PerformanceTest]=] PROPERTIES  TIMEOUT "120" _BACKTRACE_TRIPLES "F:/cmo-dev/my_osgearth_web/osgearth_origin/VulkanSceneGraph/vsg_webgpu/tests/CMakeLists.txt;75;add_test;F:/cmo-dev/my_osgearth_web/osgearth_origin/VulkanSceneGraph/vsg_webgpu/tests/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Mm][Ii][Nn][Ss][Ii][Zz][Ee][Rr][Ee][Ll])$")
  add_test([=[PerformanceTest]=] "F:/cmo-dev/my_osgearth_web/osgearth_origin/VulkanSceneGraph/vsg_webgpu/build_desk/bin/MinSizeRel/test_performance.exe")
  set_tests_properties([=[PerformanceTest]=] PROPERTIES  TIMEOUT "120" _BACKTRACE_TRIPLES "F:/cmo-dev/my_osgearth_web/osgearth_origin/VulkanSceneGraph/vsg_webgpu/tests/CMakeLists.txt;75;add_test;F:/cmo-dev/my_osgearth_web/osgearth_origin/VulkanSceneGraph/vsg_webgpu/tests/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Rr][Ee][Ll][Ww][Ii][Tt][Hh][Dd][Ee][Bb][Ii][Nn][Ff][Oo])$")
  add_test([=[PerformanceTest]=] "F:/cmo-dev/my_osgearth_web/osgearth_origin/VulkanSceneGraph/vsg_webgpu/build_desk/bin/RelWithDebInfo/test_performance.exe")
  set_tests_properties([=[PerformanceTest]=] PROPERTIES  TIMEOUT "120" _BACKTRACE_TRIPLES "F:/cmo-dev/my_osgearth_web/osgearth_origin/VulkanSceneGraph/vsg_webgpu/tests/CMakeLists.txt;75;add_test;F:/cmo-dev/my_osgearth_web/osgearth_origin/VulkanSceneGraph/vsg_webgpu/tests/CMakeLists.txt;0;")
else()
  add_test([=[PerformanceTest]=] NOT_AVAILABLE)
endif()
