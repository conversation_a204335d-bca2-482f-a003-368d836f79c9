# VSG Render Engine Abstraction Build Script
# PowerShell version for Windows

param(
    [switch]$Debug,
    [switch]$Clean,
    [switch]$Help
)

if ($Help) {
    Write-Host "Usage: .\build.ps1 [options]"
    Write-Host ""
    Write-Host "Options:"
    Write-Host "  -Debug      Build Debug version (default: Release)"
    Write-Host "  -Clean      Clean build directory first"
    Write-Host "  -Help       Show this help"
    Write-Host ""
    exit 0
}

$BuildType = if ($Debug) { "Debug" } else { "Release" }
$BuildDir = "build_new"

Write-Host "========================================" -ForegroundColor Blue
Write-Host "VSG Render Engine Abstraction Build" -ForegroundColor Blue
Write-Host "========================================" -ForegroundColor Blue
Write-Host ""

Write-Host "Checking build environment..." -ForegroundColor Yellow

# Check CMake
try {
    $cmakeVersion = cmake --version 2>$null
    if ($LASTEXITCODE -eq 0) {
        Write-Host "[OK] CMake found" -ForegroundColor Green
    } else {
        throw "CMake not found"
    }
} catch {
    Write-Host "[ERROR] CMake not found" -ForegroundColor Red
    exit 1
}

# Check compiler
try {
    $clPath = where.exe cl 2>$null
    if ($LASTEXITCODE -eq 0) {
        Write-Host "[OK] MSVC compiler found" -ForegroundColor Green
    } else {
        throw "MSVC not found"
    }
} catch {
    Write-Host "[ERROR] MSVC compiler not found" -ForegroundColor Red
    Write-Host "Please run this script in Visual Studio Developer Command Prompt" -ForegroundColor Yellow
    exit 1
}

Write-Host ""
Write-Host "Build configuration:" -ForegroundColor Yellow
Write-Host "  Build type: $BuildType"
Write-Host "  Build directory: $BuildDir"
Write-Host ""

# Clean build directory
if ($Clean) {
    Write-Host "Cleaning build directory..." -ForegroundColor Yellow
    if (Test-Path $BuildDir) {
        Remove-Item -Recurse -Force $BuildDir
    }
    Write-Host "[OK] Clean completed" -ForegroundColor Green
    Write-Host ""
}

# Create build directory
if (!(Test-Path $BuildDir)) {
    New-Item -ItemType Directory -Path $BuildDir | Out-Null
}

Set-Location $BuildDir

Write-Host "Step 1/5: Configuring CMake..." -ForegroundColor Blue
$cmakeArgs = @(
    ".."
    "-G", "Visual Studio 17 2022"
    "-A", "x64"
    "-DCMAKE_BUILD_TYPE=$BuildType"
    "-DVSG_ABSTRACTION_BUILD_VSG_BACKEND=OFF"
    "-DVSG_ABSTRACTION_BUILD_WEBGPU_BACKEND=OFF"
    "-DVSG_ABSTRACTION_BUILD_OPENGL_BACKEND=OFF"
    "-DVSG_ABSTRACTION_BUILD_MOCK_BACKEND=ON"
    "-DVSG_ABSTRACTION_BUILD_TESTS=ON"
    "-DVSG_ABSTRACTION_BUILD_EXAMPLES=ON"
    "-DVSG_ABSTRACTION_BUILD_SHARED_LIBS=OFF"
)

& cmake @cmakeArgs

if ($LASTEXITCODE -ne 0) {
    Write-Host "[FAIL] CMake configuration failed" -ForegroundColor Red
    Set-Location ..
    exit 1
}

Write-Host "[OK] CMake configuration successful" -ForegroundColor Green
Write-Host ""

Write-Host "Step 2/5: Building project..." -ForegroundColor Blue
& cmake --build . --config $BuildType --parallel

if ($LASTEXITCODE -ne 0) {
    Write-Host "[FAIL] Build failed" -ForegroundColor Red
    Set-Location ..
    exit 1
}

Write-Host "[OK] Build successful" -ForegroundColor Green
Write-Host ""

Write-Host "Step 3/5: Running basic tests..." -ForegroundColor Blue
$testBasicPath = "tests\$BuildType\test_basic.exe"
if (Test-Path $testBasicPath) {
    Write-Host "Running basic functionality test..."
    & $testBasicPath
    if ($LASTEXITCODE -eq 0) {
        Write-Host "[OK] Basic test passed" -ForegroundColor Green
    } else {
        Write-Host "[FAIL] Basic test failed" -ForegroundColor Red
    }
} else {
    Write-Host "[WARN] Basic test program not found" -ForegroundColor Yellow
}

Write-Host ""

Write-Host "Step 4/5: Running example programs..." -ForegroundColor Blue
$basicExamplePath = "examples\$BuildType\basic_example.exe"
if (Test-Path $basicExamplePath) {
    Write-Host "Running basic example..."
    & $basicExamplePath
    if ($LASTEXITCODE -eq 0) {
        Write-Host "[OK] Basic example successful" -ForegroundColor Green
    } else {
        Write-Host "[FAIL] Basic example failed" -ForegroundColor Red
    }
} else {
    Write-Host "[WARN] Basic example program not found" -ForegroundColor Yellow
}

Write-Host ""

Write-Host "Step 5/5: Running complete test suite..." -ForegroundColor Blue
& ctest --output-on-failure --build-config $BuildType

if ($LASTEXITCODE -eq 0) {
    Write-Host "[OK] All tests passed" -ForegroundColor Green
} else {
    Write-Host "[WARN] Some tests failed" -ForegroundColor Yellow
}

Set-Location ..

Write-Host ""
Write-Host "========================================" -ForegroundColor Blue
Write-Host "Build and test completed!" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Blue
Write-Host ""

Write-Host "Build output:" -ForegroundColor Yellow
Write-Host "  Libraries: $BuildDir\lib\$BuildType\"
Write-Host "  Executables: $BuildDir\bin\$BuildType\"
Write-Host "  Tests: $BuildDir\tests\$BuildType\"
Write-Host "  Examples: $BuildDir\examples\$BuildType\"
Write-Host ""

Write-Host "Available programs:" -ForegroundColor Yellow
$examplePath = "$BuildDir\examples\$BuildType\basic_example.exe"
$testPath = "$BuildDir\tests\$BuildType\test_basic.exe"

if (Test-Path $examplePath) {
    Write-Host "  $examplePath"
}
if (Test-Path $testPath) {
    Write-Host "  $testPath"
}

Write-Host ""
Write-Host "Quick run commands:" -ForegroundColor Yellow
Write-Host "  cd $BuildDir\examples\$BuildType"
Write-Host "  .\basic_example.exe"
Write-Host ""

Write-Host "SUCCESS: VSG Render Engine Abstraction build completed!" -ForegroundColor Green
