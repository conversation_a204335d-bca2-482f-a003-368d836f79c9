# VSG WebGPU示例程序

# 基础三角形示例
add_executable(example_triangle
    triangle.cpp
)

target_link_libraries(example_triangle
    PRIVATE
        vsg_webgpu
        vsg::vsg
)

# 纹理四边形示例
add_executable(example_textured_quad
    textured_quad.cpp
)

target_link_libraries(example_textured_quad
    PRIVATE
        vsg_webgpu
        vsg::vsg
)

# 场景图示例
add_executable(example_scenegraph
    scenegraph.cpp
)

target_link_libraries(example_scenegraph
    PRIVATE
        vsg_webgpu
        vsg::vsg
)

# 性能测试示例
add_executable(example_performance
    performance.cpp
)

target_link_libraries(example_performance
    PRIVATE
        vsg_webgpu
        vsg::vsg
)

# Emscripten特定配置
if(VSG_WEBGPU_USE_EMSCRIPTEN)
    # 为Emscripten构建配置HTML文件
    foreach(target example_triangle example_textured_quad example_scenegraph example_performance)
        set_target_properties(${target} PROPERTIES
            SUFFIX ".html"
            LINK_FLAGS "-sUSE_WEBGPU=1 -sASYNCIFY -sALLOW_MEMORY_GROWTH=1 --shell-file ${CMAKE_CURRENT_SOURCE_DIR}/shell.html"
        )
    endforeach()
endif()

# 复制示例资源
if(EXISTS ${CMAKE_CURRENT_SOURCE_DIR}/data)
    file(COPY ${CMAKE_CURRENT_SOURCE_DIR}/data 
         DESTINATION ${CMAKE_CURRENT_BINARY_DIR})
endif()

message(STATUS "VSG WebGPU Examples configured:")
message(STATUS "  Triangle: example_triangle")
message(STATUS "  Textured Quad: example_textured_quad")
message(STATUS "  Scene Graph: example_scenegraph")
message(STATUS "  Performance: example_performance")
