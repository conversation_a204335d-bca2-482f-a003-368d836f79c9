#pragma once

/* <editor-fold desc="MIT License">

Copyright(c) 2024 VSG WebGPU Extension

Permission is hereby granted, free of charge, to any person obtaining a copy of this software and associated documentation files (the "Software"), to deal in the Software without restriction, including without limitation the rights to use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of the Software, and to permit persons to whom the Software is furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.

</editor-fold> */

#include <vsg_webgpu/core/Export.h>
#include <vsg_webgpu/core/WebGPUHeaders.h>
#include <vsg_webgpu/vk/Device.h>
#include <vsg_webgpu/vk/CommandBuffer.h>
#include <vsg/vk/State.h>
#include <vsg/maths/mat4.h>
#include <vsg/state/StateCommand.h>
#include <vsg/state/StateGroup.h>
#include <vsg/core/Mask.h>
#include <stack>
#include <vector>

namespace vsg_webgpu
{
    // forward declarations
    class RenderPassEncoder;

    /// WebGPU状态管理，对应VSG的State类
    /// 提供与VSG State相同的接口，但底层使用WebGPU实现
    class VSG_WEBGPU_DECLSPEC State : public vsg::Inherit<vsg::Object, State>
    {
    public:
        State(Device* device, const vsg::Slots& maxSlots = {});

        // 对应VSG State的核心接口
        using StateCommandStack = vsg::StateStack<vsg::StateCommand>;
        using StateStacks = std::vector<StateCommandStack>;

        vsg::ref_ptr<CommandBuffer> _commandBuffer;

        // 视锥体管理
        vsg::Frustum _frustumUnit;
        vsg::Frustum _frustumProjected;
        using FrustumStack = std::stack<vsg::Frustum>;
        FrustumStack _frustumStack;

        bool dirty = true;

        // LOD缩放的继承视图
        bool inheritViewForLODScaling = false;
        vsg::dmat4 inheritedProjectionMatrix;
        vsg::dmat4 inheritedViewMatrix;
        vsg::dmat4 inheritedViewTransform;

        // 状态槽管理
        vsg::Slots maxSlots;
        uint32_t activeMaxStateSlot = 0;
        StateStacks stateStacks;

        // 视口状态提示
        uint32_t viewportStateHint = 0;

        // 矩阵栈
        vsg::MatrixStack projectionMatrixStack{0};
        vsg::MatrixStack modelviewMatrixStack{64};

        // 状态管理方法 - 对应VSG State接口
        void reserve(const vsg::Slots& in_maxSlots);
        void connect(vsg::ref_ptr<CommandBuffer> commandBuffer);
        void reset();

        // 状态栈操作
        template<typename Iterator>
        void push(Iterator begin, Iterator end)
        {
            for (auto itr = begin; itr != end; ++itr)
            {
                auto& stateCommand = *itr;
                stateStacks[stateCommand->slot].push(stateCommand);
            }
            activeMaxStateSlot = maxSlots.max();
        }

        template<typename Iterator>
        void pop(Iterator begin, Iterator end)
        {
            for (auto itr = begin; itr != end; ++itr)
            {
                auto& stateCommand = *itr;
                stateStacks[stateCommand->slot].pop();
            }
        }

        void pushView(vsg::ref_ptr<vsg::StateCommand> command);
        void popView(vsg::ref_ptr<vsg::StateCommand> command);

        // 状态录制
        void record();

        // 状态应用
        void apply(const vsg::StateCommand* command);
        void apply(const vsg::StateGroup* stateGroup);

        // WebGPU特定接口
        void setRenderPassEncoder(WGPURenderPassEncoder encoder)
        {
            _renderPassEncoder = encoder;
        }
        WGPURenderPassEncoder getRenderPassEncoder() const
        {
            return _renderPassEncoder;
        }

        void setComputePassEncoder(WGPUComputePassEncoder encoder)
        {
            _computePassEncoder = encoder;
        }
        WGPUComputePassEncoder getComputePassEncoder() const
        {
            return _computePassEncoder;
        }

        Device* getDevice() const { return _device.get(); }

        // 当前绑定的资源状态
        struct BoundResources
        {
            WGPURenderPipeline renderPipeline;
            WGPUComputePipeline computePipeline;
            std::array<WGPUBindGroup, 4> bindGroups;
            std::array<WGPUBuffer, 8> vertexBuffers;
            std::array<uint64_t, 8> vertexBufferOffsets;
            WGPUBuffer indexBuffer;
            uint64_t indexBufferOffset = 0;
            WGPUIndexFormat indexFormat = WGPUIndexFormat_Uint16;
        };

        BoundResources& getBoundResources() { return _boundResources; }

        // 矩阵操作 - 兼容性接口
        void pushProjectionMatrix(const vsg::mat4& matrix);
        void popProjectionMatrix();
        void pushViewMatrix(const vsg::mat4& matrix);
        void popViewMatrix();
        void pushModelMatrix(const vsg::mat4& matrix);
        void popModelMatrix();

        vsg::mat4 getProjectionMatrix() const;
        vsg::mat4 getViewMatrix() const;
        vsg::mat4 getModelMatrix() const;
        vsg::mat4 getMVPMatrix() const;

        // 继承视图设置
        void setInheritedViewProjectionAndViewMatrix(const vsg::dmat4& projMatrix, const vsg::dmat4& viewMatrix);

    private:
        vsg::ref_ptr<Device> _device;
        WGPURenderPassEncoder _renderPassEncoder;
        WGPUComputePassEncoder _computePassEncoder;

        BoundResources _boundResources;

        // 内部状态管理
        void _dirtyStateStacks();
    };

    VSG_type_name(vsg_webgpu::State);

} // namespace vsg_webgpu
