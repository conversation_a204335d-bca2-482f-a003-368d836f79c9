@echo off
REM VSG渲染引擎抽象层自动化构建和测试脚本
REM 完整的构建、编译、测试流程

setlocal enabledelayedexpansion

echo ========================================
echo VSG渲染引擎抽象层自动化构建测试
echo ========================================
echo.

REM 设置颜色输出
for /f %%A in ('"prompt $H &echo on &for %%B in (1) do rem"') do set BS=%%A

REM 颜色定义
set "GREEN=[92m"
set "RED=[91m"
set "YELLOW=[93m"
set "BLUE=[94m"
set "RESET=[0m"

REM 设置构建参数
set BUILD_TYPE=Release
set BUILD_DIR=build
set INSTALL_DIR=install
set GENERATOR="Visual Studio 17 2022"
set PLATFORM=x64
set CLEAN_BUILD=0
set RUN_TESTS=1
set RUN_EXAMPLES=1
set VERBOSE=0

REM 解析命令行参数
:parse_args
if "%1"=="" goto :start_build
if "%1"=="--debug" (
    set BUILD_TYPE=Debug
    shift
    goto :parse_args
)
if "%1"=="--clean" (
    set CLEAN_BUILD=1
    shift
    goto :parse_args
)
if "%1"=="--no-tests" (
    set RUN_TESTS=0
    shift
    goto :parse_args
)
if "%1"=="--no-examples" (
    set RUN_EXAMPLES=0
    shift
    goto :parse_args
)
if "%1"=="--verbose" (
    set VERBOSE=1
    shift
    goto :parse_args
)
if "%1"=="--help" (
    goto :show_help
)
shift
goto :parse_args

:show_help
echo 用法: auto_build_test.bat [选项]
echo.
echo 选项:
echo   --debug       构建Debug版本 (默认: Release)
echo   --clean       清理后重新构建
echo   --no-tests    跳过测试运行
echo   --no-examples 跳过示例运行
echo   --verbose     详细输出
echo   --help        显示此帮助信息
echo.
echo 示例:
echo   auto_build_test.bat                完整构建和测试
echo   auto_build_test.bat --debug        Debug版本构建和测试
echo   auto_build_test.bat --clean        清理后完整构建和测试
echo.
goto :end

:start_build

REM 记录开始时间
set START_TIME=%time%

echo %BLUE%检查构建环境...%RESET%

REM 检查CMake
cmake --version >nul 2>&1
if errorlevel 1 (
    echo %RED%错误: 未找到CMake，请确保CMake已安装并在PATH中%RESET%
    goto :error
)

REM 检查编译器
where cl >nul 2>&1
if errorlevel 1 (
    echo %RED%错误: 未找到MSVC编译器，请运行Developer Command Prompt%RESET%
    goto :error
)

REM 检查VSG库
echo 检查VSG库...
set VSG_FOUND=0
if exist "C:\dev\vcpkg\installed\x64-windows\include\vsg" (
    set VSG_FOUND=1
    echo %GREEN%✓ 找到VSG库 (vcpkg)%RESET%
) else (
    echo %YELLOW%⚠ 未找到VSG库，将禁用VSG后端%RESET%
)

echo %GREEN%✓ 构建环境检查完成%RESET%
echo.

echo %BLUE%构建配置:%RESET%
echo   构建类型: %BUILD_TYPE%
echo   构建目录: %BUILD_DIR%
echo   安装目录: %INSTALL_DIR%
echo   生成器: %GENERATOR%
echo   平台: %PLATFORM%
echo   VSG支持: %VSG_FOUND%
echo   运行测试: %RUN_TESTS%
echo   运行示例: %RUN_EXAMPLES%
echo.

REM 清理构建目录（如果需要）
if %CLEAN_BUILD%==1 (
    echo %YELLOW%清理构建目录...%RESET%
    if exist %BUILD_DIR% (
        rmdir /s /q %BUILD_DIR%
    )
    if exist %INSTALL_DIR% (
        rmdir /s /q %INSTALL_DIR%
    )
    echo %GREEN%✓ 清理完成%RESET%
    echo.
)

REM 创建构建目录
if not exist %BUILD_DIR% (
    mkdir %BUILD_DIR%
)

REM 进入构建目录
cd %BUILD_DIR%

echo %BLUE%步骤 1/6: 配置CMake...%RESET%

REM 设置CMake参数
set CMAKE_ARGS=-G %GENERATOR% -A %PLATFORM%
set CMAKE_ARGS=%CMAKE_ARGS% -DCMAKE_BUILD_TYPE=%BUILD_TYPE%
set CMAKE_ARGS=%CMAKE_ARGS% -DCMAKE_INSTALL_PREFIX=../%INSTALL_DIR%
set CMAKE_ARGS=%CMAKE_ARGS% -DVSG_ABSTRACTION_BUILD_MOCK_BACKEND=ON
set CMAKE_ARGS=%CMAKE_ARGS% -DVSG_ABSTRACTION_BUILD_TESTS=ON
set CMAKE_ARGS=%CMAKE_ARGS% -DVSG_ABSTRACTION_BUILD_EXAMPLES=ON
set CMAKE_ARGS=%CMAKE_ARGS% -DVSG_ABSTRACTION_BUILD_SHARED_LIBS=ON

if %VSG_FOUND%==1 (
    set CMAKE_ARGS=%CMAKE_ARGS% -DVSG_ABSTRACTION_BUILD_VSG_BACKEND=ON
    set CMAKE_ARGS=%CMAKE_ARGS% -DCMAKE_TOOLCHAIN_FILE=C:/dev/vcpkg/scripts/buildsystems/vcpkg.cmake
) else (
    set CMAKE_ARGS=%CMAKE_ARGS% -DVSG_ABSTRACTION_BUILD_VSG_BACKEND=OFF
)

if %VERBOSE%==1 (
    echo 执行: cmake .. %CMAKE_ARGS%
)

cmake .. %CMAKE_ARGS%

if errorlevel 1 (
    echo %RED%✗ CMake配置失败%RESET%
    goto :error
)

echo %GREEN%✓ CMake配置成功%RESET%
echo.

echo %BLUE%步骤 2/6: 编译项目...%RESET%
cmake --build . --config %BUILD_TYPE% --parallel

if errorlevel 1 (
    echo %RED%✗ 编译失败%RESET%
    goto :error
)

echo %GREEN%✓ 编译成功%RESET%
echo.

echo %BLUE%步骤 3/6: 安装库文件...%RESET%
cmake --install . --config %BUILD_TYPE%

if errorlevel 1 (
    echo %RED%✗ 安装失败%RESET%
    goto :error
)

echo %GREEN%✓ 安装成功%RESET%
echo.

REM 运行测试
if %RUN_TESTS%==1 (
    echo %BLUE%步骤 4/6: 运行测试...%RESET%
    
    REM 基础测试
    if exist tests\%BUILD_TYPE%\test_basic.exe (
        echo 运行基础功能测试...
        tests\%BUILD_TYPE%\test_basic.exe
        if errorlevel 1 (
            echo %RED%✗ 基础测试失败%RESET%
            set TEST_FAILED=1
        ) else (
            echo %GREEN%✓ 基础测试通过%RESET%
        )
    )
    
    REM VSG集成测试
    if exist tests\%BUILD_TYPE%\test_vsg_integration.exe (
        echo 运行VSG集成测试...
        tests\%BUILD_TYPE%\test_vsg_integration.exe
        if errorlevel 1 (
            echo %YELLOW%⚠ VSG集成测试失败（可能是VSG不可用）%RESET%
        ) else (
            echo %GREEN%✓ VSG集成测试通过%RESET%
        )
    )
    
    REM 使用CTest运行所有测试
    echo 运行完整测试套件...
    ctest --output-on-failure --build-config %BUILD_TYPE%
    
    if errorlevel 1 (
        echo %YELLOW%⚠ 部分测试失败%RESET%
    ) else (
        echo %GREEN%✓ 所有测试通过%RESET%
    )
    echo.
) else (
    echo %YELLOW%步骤 4/6: 跳过测试%RESET%
    echo.
)

REM 运行示例
if %RUN_EXAMPLES%==1 (
    echo %BLUE%步骤 5/6: 运行示例程序...%RESET%
    
    REM 基础示例
    if exist examples\%BUILD_TYPE%\basic_example.exe (
        echo 运行基础示例...
        examples\%BUILD_TYPE%\basic_example.exe
        if errorlevel 1 (
            echo %RED%✗ 基础示例运行失败%RESET%
        ) else (
            echo %GREEN%✓ 基础示例运行成功%RESET%
        )
    )
    
    REM VSG渲染示例
    if exist examples\%BUILD_TYPE%\vsg_rendering_example.exe (
        echo 运行VSG渲染示例...
        examples\%BUILD_TYPE%\vsg_rendering_example.exe
        if errorlevel 1 (
            echo %YELLOW%⚠ VSG渲染示例运行失败（可能是VSG不可用）%RESET%
        ) else (
            echo %GREEN%✓ VSG渲染示例运行成功%RESET%
        )
    )
    
    echo.
) else (
    echo %YELLOW%步骤 5/6: 跳过示例运行%RESET%
    echo.
)

echo %BLUE%步骤 6/6: 生成构建报告...%RESET%

REM 返回根目录
cd ..

REM 计算构建时间
set END_TIME=%time%

echo.
echo ========================================
echo %GREEN%构建和测试完成!%RESET%
echo ========================================
echo.
echo %BLUE%构建输出:%RESET%
echo   库文件: %BUILD_DIR%\lib\%BUILD_TYPE%\
echo   可执行文件: %BUILD_DIR%\bin\%BUILD_TYPE%\
echo   测试程序: %BUILD_DIR%\tests\%BUILD_TYPE%\
echo   示例程序: %BUILD_DIR%\examples\%BUILD_TYPE%\
echo.
echo %BLUE%安装输出:%RESET%
echo   头文件: %INSTALL_DIR%\include\
echo   库文件: %INSTALL_DIR%\lib\
echo   可执行文件: %INSTALL_DIR%\bin\
echo.

REM 显示可用的程序
echo %BLUE%可用的程序:%RESET%
if exist %BUILD_DIR%\examples\%BUILD_TYPE%\basic_example.exe (
    echo   %BUILD_DIR%\examples\%BUILD_TYPE%\basic_example.exe
)
if exist %BUILD_DIR%\examples\%BUILD_TYPE%\vsg_rendering_example.exe (
    echo   %BUILD_DIR%\examples\%BUILD_TYPE%\vsg_rendering_example.exe
)
if exist %BUILD_DIR%\examples\%BUILD_TYPE%\multi_backend_example.exe (
    echo   %BUILD_DIR%\examples\%BUILD_TYPE%\multi_backend_example.exe
)

echo.
echo %BLUE%快速运行命令:%RESET%
echo   cd %BUILD_DIR%\examples\%BUILD_TYPE%
echo   basic_example.exe
echo.

echo %GREEN%🎉 VSG渲染引擎抽象层构建测试成功完成！%RESET%
echo.

goto :end

:error
echo.
echo ========================================
echo %RED%构建或测试失败!%RESET%
echo ========================================
echo.
echo 请检查上面的错误信息并解决问题后重试。
echo.
echo 常见问题解决方案:
echo 1. 确保已安装Visual Studio 2022和CMake
echo 2. 在Developer Command Prompt中运行此脚本
echo 3. 如果VSG测试失败，请安装VSG库或使用 --no-tests 跳过
echo.
cd ..
exit /b 1

:end
endlocal
