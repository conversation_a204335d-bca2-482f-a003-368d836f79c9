#pragma once

/* <editor-fold desc="MIT License">

Copyright(c) 2024 VSG Render Engine Abstraction

Permission is hereby granted, free of charge, to any person obtaining a copy of this software and associated documentation files (the "Software"), to deal in the Software without restriction, including without limitation the rights to use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of the Software, and to permit persons to whom the Software is furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND <PERSON>NINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.

</editor-fold> */

#include <functional>
#include <memory>
#include <string>
#include <vector>
#include <vsg_abstraction/core/Export.h>
#include <vsg_abstraction/core/Types.h>

// 前向声明抽象接口
namespace vsg_abstraction
{
    class INode;
    class IGroup;
    class ITransform;
    class IGeometry;
    class IStateGroup;
    class IVisitor;
} // namespace vsg_abstraction

// 前向声明VSG类型
namespace vsg
{
    class Object;
    class Node;
    class Group;
    class Transform;
    class MatrixTransform;
    class Geometry;
    class StateGroup;
    class Device;
    class Instance;
    class PhysicalDevice;
    class Window;
    class RenderGraph;
    class RecordTraversal;
    class CommandBuffer;
    class Queue;
    class Buffer;
    class Image;
    class GraphicsPipeline;
    class ComputePipeline;
    template<typename T>
    class ref_ptr;
} // namespace vsg

namespace vsg_abstraction
{

    // 前向声明抽象接口
    class IDevice;
    class IPhysicalDevice;
    class IInstance;
    class INode;
    class IGroup;
    class ITransform;
    class IGeometry;
    class IStateGroup;
    class IWindow;
    class IRenderGraph;
    class IRecordTraversal;
    class ICommandBuffer;
    class IQueue;
    class IBuffer;
    class IImage;
    class IPipeline;
    class IVisitor;

    /**
 * @brief 渲染引擎抽象接口
 * 
 * 这是整个渲染引擎抽象层的核心接口，定义了所有渲染相关的操作。
 * 不同的渲染后端（Vulkan、WebGPU、OpenGL等）都需要实现这个接口。
 * 
 * 设计原则：
 * 1. 保持与VSG API的高度兼容性
 * 2. 支持多种渲染后端
 * 3. 提供零成本抽象
 * 4. 支持渐进式迁移
 */
    class VSG_ABSTRACTION_DECLSPEC IRenderEngine
    {
    public:
        virtual ~IRenderEngine() = default;

        // ========== 生命周期管理 ==========

        /**
     * @brief 初始化渲染引擎
     * @return 成功返回true，失败返回false
     */
        virtual bool initialize() = 0;

        /**
     * @brief 关闭渲染引擎，释放所有资源
     */
        virtual void shutdown() = 0;

        /**
     * @brief 检查渲染引擎是否已初始化
     */
        virtual bool isInitialized() const = 0;

        // ========== 后端信息 ==========

        /**
     * @brief 获取渲染后端类型
     */
        virtual RenderBackend getBackendType() const = 0;

        /**
     * @brief 获取渲染引擎信息
     */
        virtual EngineInfo getEngineInfo() const = 0;

        // ========== 设备管理 ==========

        /**
     * @brief 获取主设备
     */
        virtual IDevice* getDevice() = 0;

        /**
     * @brief 获取实例
     */
        virtual IInstance* getInstance() = 0;

        /**
     * @brief 获取所有物理设备
     */
        virtual std::vector<IPhysicalDevice*> getPhysicalDevices() = 0;

        // ========== 场景图创建 ==========

        /**
     * @brief 创建组节点
     * @return 组节点指针
     */
        virtual ref_ptr<IGroup> createGroup() = 0;

        /**
     * @brief 创建变换节点
     * @return 变换节点指针
     */
        virtual ref_ptr<ITransform> createTransform() = 0;

        /**
     * @brief 创建矩阵变换节点
     * @return 矩阵变换节点指针
     */
        virtual ref_ptr<ITransform> createMatrixTransform() = 0;

        /**
     * @brief 创建几何节点
     * @return 几何节点指针
     */
        virtual ref_ptr<IGeometry> createGeometry() = 0;

        /**
     * @brief 创建状态组节点
     * @return 状态组节点指针
     */
        virtual ref_ptr<IStateGroup> createStateGroup() = 0;

        // ========== 窗口和渲染管理 ==========

        /**
     * @brief 创建窗口
     * @param traits 窗口特性
     * @return 窗口指针
     */
        virtual ref_ptr<IWindow> createWindow(const WindowTraits& traits) = 0;

        /**
     * @brief 创建渲染图
     * @param window 目标窗口
     * @param view 视图
     * @return 渲染图指针
     */
        virtual ref_ptr<IRenderGraph> createRenderGraph(ref_ptr<IWindow> window, ref_ptr<INode> view = {}) = 0;

        /**
     * @brief 创建记录遍历器
     * @return 记录遍历器指针
     */
        virtual ref_ptr<IRecordTraversal> createRecordTraversal() = 0;

        // ========== 资源管理 ==========

        /**
     * @brief 创建缓冲区
     * @param info 缓冲区信息
     * @return 缓冲区指针
     */
        virtual ref_ptr<IBuffer> createBuffer(const BufferInfo& info) = 0;

        /**
     * @brief 创建图像
     * @param info 图像信息
     * @return 图像指针
     */
        virtual ref_ptr<IImage> createImage(const ImageInfo& info) = 0;

        /**
     * @brief 创建图形管线
     * @param info 管线信息
     * @return 管线指针
     */
        virtual ref_ptr<IPipeline> createGraphicsPipeline(const GraphicsPipelineInfo& info) = 0;

        /**
     * @brief 创建计算管线
     * @param info 管线信息
     * @return 管线指针
     */
        virtual ref_ptr<IPipeline> createComputePipeline(const ComputePipelineInfo& info) = 0;

#ifdef VSG_ABSTRACTION_HAS_VSG
        // ========== VSG兼容性接口 ==========

        /**
     * @brief 创建VSG组节点（兼容性接口）
     * @return VSG组节点
     */
        virtual vsg::ref_ptr<vsg::Group> createVSGGroup() = 0;

        /**
     * @brief 创建VSG变换节点（兼容性接口）
     * @return VSG变换节点
     */
        virtual vsg::ref_ptr<vsg::MatrixTransform> createVSGTransform() = 0;

        /**
     * @brief 创建VSG几何节点（兼容性接口）
     * @return VSG几何节点
     */
        virtual vsg::ref_ptr<vsg::Geometry> createVSGGeometry() = 0;

        /**
     * @brief 创建VSG状态组节点（兼容性接口）
     * @return VSG状态组节点
     */
        virtual vsg::ref_ptr<vsg::StateGroup> createVSGStateGroup() = 0;

        /**
     * @brief 获取VSG设备（兼容性接口）
     * @return VSG设备
     */
        virtual vsg::ref_ptr<vsg::Device> getVSGDevice() = 0;
#endif

        // ========== 同步和性能 ==========

        /**
     * @brief 等待设备空闲
     */
        virtual void waitIdle() = 0;

        /**
     * @brief 获取渲染统计信息
     */
        virtual RenderStatistics getRenderStatistics() const = 0;

        /**
     * @brief 重置渲染统计信息
     */
        virtual void resetRenderStatistics() = 0;

        // ========== 调试和诊断 ==========

        /**
     * @brief 设置调试回调
     * @param callback 调试回调函数
     */
        virtual void setDebugCallback(DebugCallback callback) = 0;

        /**
     * @brief 开始调试标记
     * @param name 标记名称
     */
        virtual void beginDebugMarker(const std::string& name) = 0;

        /**
     * @brief 结束调试标记
     */
        virtual void endDebugMarker() = 0;

        // ========== 扩展接口 ==========

        /**
     * @brief 获取原生渲染引擎对象（用于高级用法）
     * @return 原生对象指针，类型取决于具体实现
     */
        virtual void* getNativeHandle() const = 0;

        /**
     * @brief 执行自定义渲染命令
     * @param command 自定义命令函数
     */
        virtual void executeCustomCommand(std::function<void(void*)> command) = 0;

        /**
     * @brief 检查功能支持
     * @param feature 功能名称
     * @return 支持返回true
     */
        virtual bool supportsFeature(const std::string& feature) const = 0;
    };

} // namespace vsg_abstraction
