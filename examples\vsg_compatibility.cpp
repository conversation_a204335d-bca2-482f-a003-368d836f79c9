/* <editor-fold desc="MIT License">

Copyright(c) 2024 VSG Render Engine Abstraction

Permission is hereby granted, free of charge, to any person obtaining a copy of this software and associated documentation files (the "Software"), to deal in the Software without restriction, including without limitation the rights to use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of the Software, and to permit persons to whom the Software is furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.

</editor-fold> */

/**
 * @file vsg_compatibility.cpp
 * @brief VSG兼容性示例程序
 * 
 * 这个示例展示了如何使用VSG渲染引擎抽象层来创建和管理VSG对象，
 * 同时保持与原生VSG API的完全兼容性。
 */

#include <vsg_abstraction/core/RenderEngineManager.h>
#include <vsg_abstraction/core/RenderEngineFactory.h>
#include <vsg_abstraction/backends/VSGRenderEngine.h>

#include <iostream>
#include <memory>

using namespace vsg_abstraction;

void demonstrateBasicVSGUsage()
{
    std::cout << "=== Basic VSG Usage Demonstration ===" << std::endl;
    
    // 创建VSG渲染引擎
    auto engine = RenderEngineFactory::create(RenderBackend::Vulkan);
    if (!engine)
    {
        std::cerr << "Failed to create VSG render engine" << std::endl;
        return;
    }
    
    std::cout << "✅ VSG Render Engine created successfully" << std::endl;
    
    // 初始化引擎
    if (!engine->initialize())
    {
        std::cerr << "Failed to initialize VSG render engine" << std::endl;
        return;
    }
    
    std::cout << "✅ VSG Render Engine initialized successfully" << std::endl;
    
    // 获取引擎信息
    auto info = engine->getEngineInfo();
    std::cout << "Engine Information:" << std::endl;
    std::cout << "  Name: " << info.name << std::endl;
    std::cout << "  Version: " << info.version << std::endl;
    std::cout << "  Vendor: " << info.vendor << std::endl;
    std::cout << "  Backend: " << static_cast<int>(info.backend) << std::endl;
    
    // 关闭引擎
    engine->shutdown();
    std::cout << "✅ VSG Render Engine shutdown successfully" << std::endl;
}

void demonstrateSceneGraphCreation()
{
    std::cout << "\n=== Scene Graph Creation Demonstration ===" << std::endl;
    
    auto engine = RenderEngineFactory::create(RenderBackend::Vulkan);
    if (!engine || !engine->initialize())
    {
        std::cerr << "Failed to initialize VSG render engine" << std::endl;
        return;
    }
    
    // 创建场景图节点
    std::cout << "Creating scene graph nodes..." << std::endl;
    
    // 创建根组节点
    auto rootGroup = engine->createGroup();
    if (rootGroup)
    {
        std::cout << "✅ Root group created" << std::endl;
    }
    
    // 创建变换节点
    auto transform = engine->createTransform();
    if (transform)
    {
        std::cout << "✅ Transform node created" << std::endl;
        
        // 设置变换矩阵
        mat4 matrix = {{
            {{1.0f, 0.0f, 0.0f, 0.0f}},
            {{0.0f, 1.0f, 0.0f, 0.0f}},
            {{0.0f, 0.0f, 1.0f, 0.0f}},
            {{0.0f, 0.0f, 0.0f, 1.0f}}
        }};
        transform->setMatrix(matrix);
        std::cout << "✅ Transform matrix set" << std::endl;
    }
    
    // 创建几何节点
    auto geometry = engine->createGeometry();
    if (geometry)
    {
        std::cout << "✅ Geometry node created" << std::endl;
        
        // 设置简单的三角形顶点
        std::vector<vec3> vertices = {
            {{0.0f, 0.5f, 0.0f}},
            {{-0.5f, -0.5f, 0.0f}},
            {{0.5f, -0.5f, 0.0f}}
        };
        geometry->setVertices(vertices);
        std::cout << "✅ Triangle vertices set" << std::endl;
    }
    
    // 创建状态组
    auto stateGroup = engine->createStateGroup();
    if (stateGroup)
    {
        std::cout << "✅ State group created" << std::endl;
    }
    
    engine->shutdown();
    std::cout << "✅ Scene graph demonstration completed" << std::endl;
}

void demonstrateVSGCompatibility()
{
    std::cout << "\n=== VSG Compatibility Demonstration ===" << std::endl;
    
    auto engine = RenderEngineFactory::create(RenderBackend::Vulkan);
    if (!engine || !engine->initialize())
    {
        std::cerr << "Failed to initialize VSG render engine" << std::endl;
        return;
    }
    
    // 使用VSG兼容性接口创建原生VSG对象
    std::cout << "Creating native VSG objects..." << std::endl;
    
    auto vsgGroup = engine->createVSGGroup();
    if (vsgGroup)
    {
        std::cout << "✅ Native VSG Group created" << std::endl;
        std::cout << "  VSG Group type: " << vsgGroup->className() << std::endl;
    }
    
    auto vsgTransform = engine->createVSGTransform();
    if (vsgTransform)
    {
        std::cout << "✅ Native VSG Transform created" << std::endl;
        std::cout << "  VSG Transform type: " << vsgTransform->className() << std::endl;
    }
    
    auto vsgGeometry = engine->createVSGGeometry();
    if (vsgGeometry)
    {
        std::cout << "✅ Native VSG Geometry created" << std::endl;
        std::cout << "  VSG Geometry type: " << vsgGeometry->className() << std::endl;
    }
    
    auto vsgStateGroup = engine->createVSGStateGroup();
    if (vsgStateGroup)
    {
        std::cout << "✅ Native VSG StateGroup created" << std::endl;
        std::cout << "  VSG StateGroup type: " << vsgStateGroup->className() << std::endl;
    }
    
    // 获取VSG设备对象
    auto vsgDevice = engine->getVSGDevice();
    if (vsgDevice)
    {
        std::cout << "✅ Native VSG Device obtained" << std::endl;
        std::cout << "  Device ID: " << vsgDevice->deviceID << std::endl;
    }
    
    engine->shutdown();
    std::cout << "✅ VSG compatibility demonstration completed" << std::endl;
}

void demonstrateRenderStatistics()
{
    std::cout << "\n=== Render Statistics Demonstration ===" << std::endl;
    
    auto engine = RenderEngineFactory::create(RenderBackend::Vulkan);
    if (!engine || !engine->initialize())
    {
        std::cerr << "Failed to initialize VSG render engine" << std::endl;
        return;
    }
    
    // 获取初始统计信息
    auto stats = engine->getRenderStatistics();
    std::cout << "Initial Statistics:" << std::endl;
    std::cout << "  Frame count: " << stats.frameCount << std::endl;
    std::cout << "  Draw calls: " << stats.drawCalls << std::endl;
    std::cout << "  Triangles: " << stats.triangles << std::endl;
    std::cout << "  Vertices: " << stats.vertices << std::endl;
    std::cout << "  Memory used: " << stats.memoryUsed << " bytes" << std::endl;
    std::cout << "  Memory allocated: " << stats.memoryAllocated << " bytes" << std::endl;
    
    // 创建一些对象来更新统计信息
    auto group = engine->createGroup();
    auto transform = engine->createTransform();
    auto geometry = engine->createGeometry();
    
    // 获取更新后的统计信息
    auto updatedStats = engine->getRenderStatistics();
    std::cout << "\nUpdated Statistics:" << std::endl;
    std::cout << "  Frame count: " << updatedStats.frameCount << std::endl;
    std::cout << "  Memory used: " << updatedStats.memoryUsed << " bytes" << std::endl;
    
    // 重置统计信息
    engine->resetRenderStatistics();
    auto resetStats = engine->getRenderStatistics();
    std::cout << "\nReset Statistics:" << std::endl;
    std::cout << "  Frame count: " << resetStats.frameCount << std::endl;
    std::cout << "  Memory used: " << resetStats.memoryUsed << " bytes" << std::endl;
    
    engine->shutdown();
    std::cout << "✅ Render statistics demonstration completed" << std::endl;
}

int main()
{
    std::cout << "VSG Compatibility Example" << std::endl;
    std::cout << "=========================" << std::endl;
    std::cout << "This example demonstrates the VSG render engine abstraction layer" << std::endl;
    std::cout << "and its compatibility with native VSG objects." << std::endl;
    
    try
    {
        demonstrateBasicVSGUsage();
        demonstrateSceneGraphCreation();
        demonstrateVSGCompatibility();
        demonstrateRenderStatistics();
        
        std::cout << "\n🎉 VSG Compatibility Example completed successfully!" << std::endl;
        std::cout << "\nKey Features Demonstrated:" << std::endl;
        std::cout << "  ✓ VSG render engine creation and initialization" << std::endl;
        std::cout << "  ✓ Abstract scene graph node creation" << std::endl;
        std::cout << "  ✓ Native VSG object creation through compatibility interface" << std::endl;
        std::cout << "  ✓ Render statistics monitoring" << std::endl;
        std::cout << "  ✓ Proper resource cleanup" << std::endl;
        
        return 0;
    }
    catch (const std::exception& e)
    {
        std::cerr << "❌ Example failed with exception: " << e.what() << std::endl;
        return 1;
    }
    catch (...)
    {
        std::cerr << "❌ Example failed with unknown exception" << std::endl;
        return 1;
    }
}
