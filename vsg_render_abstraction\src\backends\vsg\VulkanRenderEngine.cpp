/* <editor-fold desc="MIT License">

Copyright(c) 2024 VSG Render Engine Abstraction

Permission is hereby granted, free of charge, to any person obtaining a copy of this software and associated documentation files (the "Software"), to deal in the Software without restriction, including without limitation the rights to use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of the Software, and to permit persons to whom the Software is furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.

</editor-fold> */

#include <vsg_abstraction/adapters/VSGNodeAdapter.h>
#include <vsg_abstraction/backends/VSGRenderEngine.h>
#include <vsg_abstraction/core/Config.h>

// VSG includes
#include <vsg/all.h>

namespace vsg_abstraction
{

    // ========== VSGRenderEngine Implementation ==========

    VSGRenderEngine::VSGRenderEngine(const RenderEngineConfig* config)
    {
        if (config)
        {
            config_ = *config;
        }

        // 检查是否使用现有VSG对象
        if (config && config->vulkan.useExistingVSGObjects)
        {
            vsgInstance_ = config->vulkan.instance;
            vsgDevice_ = config->vulkan.device;
            vsgPhysicalDevice_ = config->vulkan.physicalDevice;
            usingExistingVSGObjects_ = true;
        }

        VSG_ABSTRACTION_LOG_DEBUG("VSGRenderEngine created");
    }

    VSGRenderEngine::VSGRenderEngine(vsg::ref_ptr<vsg::Instance> instance,
                                     vsg::ref_ptr<vsg::Device> device,
                                     const RenderEngineConfig* config)
    {
        if (config)
        {
            config_ = *config;
        }

        vsgInstance_ = instance;
        vsgDevice_ = device;
        usingExistingVSGObjects_ = true;

        if (device)
        {
            vsgPhysicalDevice_ = device->getPhysicalDevice();
        }

        VSG_ABSTRACTION_LOG_DEBUG("VSGRenderEngine created from existing VSG objects");
    }

    VSGRenderEngine::~VSGRenderEngine()
    {
        shutdown();
        VSG_ABSTRACTION_LOG_DEBUG("VSGRenderEngine destroyed");
    }

    bool VSGRenderEngine::initialize()
    {
        std::lock_guard<std::mutex> lock(statisticsMutex_);

        if (initialized_)
        {
            VSG_ABSTRACTION_LOG_WARN("VSGRenderEngine already initialized");
            return true;
        }

        VSG_ABSTRACTION_LOG_INFO("Initializing VSGRenderEngine...");

        try
        {
            // 初始化VSG实例
            if (!_initializeInstance())
            {
                VSG_ABSTRACTION_LOG_ERROR("Failed to initialize VSG instance");
                return false;
            }

            // 初始化设备
            if (!_initializeDevice())
            {
                VSG_ABSTRACTION_LOG_ERROR("Failed to initialize VSG device");
                return false;
            }

            // 初始化队列
            if (!_initializeQueues())
            {
                VSG_ABSTRACTION_LOG_ERROR("Failed to initialize VSG queues");
                return false;
            }

            // 设置调试回调
            _setupDebugCallback();

            // 创建包装对象
            if (vsgDevice_)
            {
                device_ = std::make_unique<VSGDevice>(vsgDevice_);
            }

            if (vsgInstance_)
            {
                instance_ = std::make_unique<VSGInstance>(vsgInstance_);
            }

            // 重置统计信息
            resetRenderStatistics();

            initialized_ = true;
            VSG_ABSTRACTION_LOG_INFO("VSGRenderEngine initialized successfully");
            return true;
        }
        catch (const std::exception& e)
        {
            VSG_ABSTRACTION_LOG_ERROR("Exception during VSGRenderEngine initialization: " << e.what());
            return false;
        }
    }

    void VSGRenderEngine::shutdown()
    {
        std::lock_guard<std::mutex> lock(statisticsMutex_);

        if (!initialized_)
        {
            return;
        }

        VSG_ABSTRACTION_LOG_INFO("Shutting down VSGRenderEngine...");

        // 等待设备空闲
        if (vsgDevice_)
        {
            vsgDevice_->waitIdle();
        }

        // 清理包装对象
        device_.reset();
        instance_.reset();
        physicalDevices_.clear();

        // 清理缓存
        {
            std::lock_guard<std::mutex> cacheLock(cacheMutex_);
            nodeCache_.clear();
        }

        // 如果不是使用现有对象，则清理VSG对象
        if (!usingExistingVSGObjects_)
        {
            vsgQueue_ = nullptr;
            vsgCommandPool_ = nullptr;
            vsgDevice_ = nullptr;
            vsgPhysicalDevice_ = nullptr;
            vsgInstance_ = nullptr;
        }

        initialized_ = false;
        VSG_ABSTRACTION_LOG_INFO("VSGRenderEngine shutdown completed");
    }

    bool VSGRenderEngine::isInitialized() const
    {
        std::lock_guard<std::mutex> lock(statisticsMutex_);
        return initialized_;
    }

    RenderBackend VSGRenderEngine::getBackendType() const
    {
        return RenderBackend::Vulkan;
    }

    EngineInfo VSGRenderEngine::getEngineInfo() const
    {
        EngineInfo info;
        info.name = "VSG Render Engine";
        info.version = VSG_ABSTRACTION_VERSION;
        info.vendor = "VulkanSceneGraph";
        info.backend = RenderBackend::Vulkan;

// 添加VSG版本信息
#if VSG_ABSTRACTION_SUPPORT_VSG
        info.extensions.push_back("VSG_VERSION_" + std::to_string(VSG_VERSION_MAJOR) +
                                  "_" + std::to_string(VSG_VERSION_MINOR) +
                                  "_" + std::to_string(VSG_VERSION_PATCH));
#endif

        // 添加Vulkan扩展信息
        if (vsgInstance_)
        {
            auto extensions = vsgInstance_->getExtensions();
            for (const auto& ext : extensions)
            {
                info.extensions.push_back(ext);
            }
        }

        return info;
    }

    IDevice* VSGRenderEngine::getDevice()
    {
        return device_.get();
    }

    IInstance* VSGRenderEngine::getInstance()
    {
        return instance_.get();
    }

    std::vector<IPhysicalDevice*> VSGRenderEngine::getPhysicalDevices()
    {
        std::vector<IPhysicalDevice*> devices;
        for (auto& device : physicalDevices_)
        {
            devices.push_back(device.get());
        }
        return devices;
    }

    // ========== 场景图创建 ==========

    ref_ptr<IGroup> VSGRenderEngine::createGroup()
    {
        auto vsgGroup = vsg::Group::create();
        auto adapter = std::make_shared<VSGGroupAdapter>(vsgGroup);

        // 缓存适配器
        {
            std::lock_guard<std::mutex> lock(cacheMutex_);
            nodeCache_[vsgGroup.get()] = adapter;
        }

        _updateStatistics();
        return adapter;
    }

    ref_ptr<ITransform> VSGRenderEngine::createTransform()
    {
        auto vsgTransform = vsg::MatrixTransform::create();
        auto adapter = std::make_shared<VSGTransformAdapter>(vsgTransform);

        // 缓存适配器
        {
            std::lock_guard<std::mutex> lock(cacheMutex_);
            nodeCache_[vsgTransform.get()] = adapter;
        }

        _updateStatistics();
        return adapter;
    }

    ref_ptr<ITransform> VSGRenderEngine::createMatrixTransform()
    {
        return createTransform(); // VSG中MatrixTransform就是主要的Transform实现
    }

    ref_ptr<IGeometry> VSGRenderEngine::createGeometry()
    {
        auto vsgGeometry = vsg::Geometry::create();
        auto adapter = std::make_shared<VSGGeometryAdapter>(vsgGeometry);

        // 缓存适配器
        {
            std::lock_guard<std::mutex> lock(cacheMutex_);
            nodeCache_[vsgGeometry.get()] = adapter;
        }

        _updateStatistics();
        return adapter;
    }

    ref_ptr<IStateGroup> VSGRenderEngine::createStateGroup()
    {
        auto vsgStateGroup = vsg::StateGroup::create();
        auto adapter = std::make_shared<VSGStateGroupAdapter>(vsgStateGroup);

        // 缓存适配器
        {
            std::lock_guard<std::mutex> lock(cacheMutex_);
            nodeCache_[vsgStateGroup.get()] = adapter;
        }

        _updateStatistics();
        return adapter;
    }

    // ========== 窗口和渲染管理 ==========

    ref_ptr<IWindow> VSGRenderEngine::createWindow(const WindowTraits& traits)
    {
        if (!vsgInstance_)
        {
            VSG_ABSTRACTION_LOG_ERROR("Cannot create window: VSG instance not initialized");
            return nullptr;
        }

        // 创建VSG窗口特性
        auto vsgTraits = vsg::WindowTraits::create();
        vsgTraits->windowTitle = traits.windowTitle;
        vsgTraits->x = traits.x;
        vsgTraits->y = traits.y;
        vsgTraits->width = traits.width;
        vsgTraits->height = traits.height;
        vsgTraits->decoration = traits.decoration;
        vsgTraits->fullscreen = traits.fullscreen;
        vsgTraits->resizable = traits.resizable;
        vsgTraits->hdpi = traits.hdpi;
        vsgTraits->samples = static_cast<VkSampleCountFlagBits>(traits.samples);
        vsgTraits->swapchainPreferences.presentMode = traits.vsync ? VK_PRESENT_MODE_FIFO_KHR : VK_PRESENT_MODE_IMMEDIATE_KHR;
        vsgTraits->debugLayer = traits.debugLayer;
        vsgTraits->apiDumpLayer = traits.apiDumpLayer;

        // 添加扩展
        for (const auto& ext : traits.instanceExtensionNames)
        {
            vsgTraits->instanceExtensionNames.push_back(ext.c_str());
        }
        for (const auto& ext : traits.deviceExtensionNames)
        {
            vsgTraits->deviceExtensionNames.push_back(ext.c_str());
        }

        // 创建VSG窗口
        auto vsgWindow = vsg::Window::create(vsgTraits);
        if (!vsgWindow)
        {
            VSG_ABSTRACTION_LOG_ERROR("Failed to create VSG window");
            return nullptr;
        }

        // 创建窗口适配器
        auto windowAdapter = std::make_shared<VSGWindowAdapter>(vsgWindow);

        _updateStatistics();
        return windowAdapter;
    }

    ref_ptr<IRenderGraph> VSGRenderEngine::createRenderGraph(ref_ptr<IWindow> window, ref_ptr<INode> view)
    {
        if (!window)
        {
            VSG_ABSTRACTION_LOG_ERROR("Cannot create render graph: window is null");
            return nullptr;
        }

        // 获取VSG窗口
        auto vsgWindowAdapter = std::dynamic_pointer_cast<VSGWindowAdapter>(window);
        if (!vsgWindowAdapter)
        {
            VSG_ABSTRACTION_LOG_ERROR("Cannot create render graph: invalid window type");
            return nullptr;
        }

        auto vsgWindow = vsgWindowAdapter->getVSGWindow();
        if (!vsgWindow)
        {
            VSG_ABSTRACTION_LOG_ERROR("Cannot create render graph: VSG window is null");
            return nullptr;
        }

        // 创建VSG渲染图
        auto vsgRenderGraph = vsg::RenderGraph::create(vsgWindow);
        if (!vsgRenderGraph)
        {
            VSG_ABSTRACTION_LOG_ERROR("Failed to create VSG render graph");
            return nullptr;
        }

        // 如果提供了视图，设置场景图
        if (view)
        {
            auto vsgNodeAdapter = std::dynamic_pointer_cast<VSGNodeAdapter>(view);
            if (vsgNodeAdapter)
            {
                auto vsgNode = vsgNodeAdapter->getVSGNode();
                if (vsgNode)
                {
                    // 创建视图并设置场景图
                    auto vsgView = vsg::View::create();
                    vsgView->sceneGraph = vsgNode;
                    vsgRenderGraph->addChild(vsgView);
                }
            }
        }

        // 创建渲染图适配器
        auto renderGraphAdapter = std::make_shared<VSGRenderGraphAdapter>(vsgRenderGraph);

        _updateStatistics();
        return renderGraphAdapter;
    }

    ref_ptr<IRecordTraversal> VSGRenderEngine::createRecordTraversal()
    {
        // 创建VSG记录遍历器
        auto vsgRecordTraversal = vsg::RecordTraversal::create();
        if (!vsgRecordTraversal)
        {
            VSG_ABSTRACTION_LOG_ERROR("Failed to create VSG record traversal");
            return nullptr;
        }

        // 创建记录遍历适配器
        auto recordTraversalAdapter = std::make_shared<VSGRecordTraversalAdapter>(vsgRecordTraversal);

        _updateStatistics();
        return recordTraversalAdapter;
    }

    // ========== 资源管理 ==========

    ref_ptr<IBuffer> VSGRenderEngine::createBuffer(const BufferInfo& info)
    {
        if (!vsgDevice_)
        {
            VSG_ABSTRACTION_LOG_ERROR("Cannot create buffer: VSG device not initialized");
            return nullptr;
        }

        // 转换缓冲区类型
        VkBufferUsageFlags usage = 0;
        switch (info.type)
        {
        case BufferType::Vertex:
            usage = VK_BUFFER_USAGE_VERTEX_BUFFER_BIT;
            break;
        case BufferType::Index:
            usage = VK_BUFFER_USAGE_INDEX_BUFFER_BIT;
            break;
        case BufferType::Uniform:
            usage = VK_BUFFER_USAGE_UNIFORM_BUFFER_BIT;
            break;
        case BufferType::Storage:
            usage = VK_BUFFER_USAGE_STORAGE_BUFFER_BIT;
            break;
        case BufferType::Staging:
            usage = VK_BUFFER_USAGE_TRANSFER_SRC_BIT;
            break;
        }

        if (info.usage != 0)
        {
            usage |= info.usage;
        }

        // 创建VSG缓冲区
        auto vsgBuffer = vsg::createBufferAndMemory(vsgDevice_, info.size, usage,
                                                    VK_SHARING_MODE_EXCLUSIVE);
        if (!vsgBuffer)
        {
            VSG_ABSTRACTION_LOG_ERROR("Failed to create VSG buffer");
            return nullptr;
        }

        // 如果有初始数据，复制数据
        if (info.data && info.size > 0)
        {
            auto stagingBuffer = vsg::createBufferAndMemory(vsgDevice_, info.size,
                                                            VK_BUFFER_USAGE_TRANSFER_SRC_BIT,
                                                            VK_SHARING_MODE_EXCLUSIVE,
                                                            VK_MEMORY_PROPERTY_HOST_VISIBLE_BIT | VK_MEMORY_PROPERTY_HOST_COHERENT_BIT);

            // 映射内存并复制数据
            void* mappedMemory;
            vkMapMemory(vsgDevice_->vk(), stagingBuffer.memory->vk(), 0, info.size, 0, &mappedMemory);
            memcpy(mappedMemory, info.data, info.size);
            vkUnmapMemory(vsgDevice_->vk(), stagingBuffer.memory->vk());

            // 复制到目标缓冲区
            auto commandBuffer = vsgDevice_->getCommandPool(vsgDevice_->getQueueFamily(VK_QUEUE_TRANSFER_BIT))->allocate();
            commandBuffer->begin();

            VkBufferCopy copyRegion = {};
            copyRegion.size = info.size;
            vkCmdCopyBuffer(commandBuffer->vk(), stagingBuffer.buffer->vk(), vsgBuffer.buffer->vk(), 1, &copyRegion);

            commandBuffer->end();

            auto queue = vsgDevice_->getQueue(vsgDevice_->getQueueFamily(VK_QUEUE_TRANSFER_BIT));
            queue->submit(commandBuffer);
            queue->waitIdle();
        }

        // 创建缓冲区适配器
        auto bufferAdapter = std::make_shared<VSGBufferAdapter>(vsgBuffer.buffer, vsgBuffer.memory);

        _updateStatistics();
        return bufferAdapter;
    }

    ref_ptr<IImage> VSGRenderEngine::createImage(const ImageInfo& info)
    {
        if (!vsgDevice_)
        {
            VSG_ABSTRACTION_LOG_ERROR("Cannot create image: VSG device not initialized");
            return nullptr;
        }

        // 转换图像格式
        VkFormat vkFormat = VK_FORMAT_UNDEFINED;
        switch (info.format)
        {
        case ImageFormat::RGBA8:
            vkFormat = VK_FORMAT_R8G8B8A8_UNORM;
            break;
        case ImageFormat::RGBA16F:
            vkFormat = VK_FORMAT_R16G16B16A16_SFLOAT;
            break;
        case ImageFormat::RGBA32F:
            vkFormat = VK_FORMAT_R32G32B32A32_SFLOAT;
            break;
        case ImageFormat::RGB8:
            vkFormat = VK_FORMAT_R8G8B8_UNORM;
            break;
        case ImageFormat::Depth24Stencil8:
            vkFormat = VK_FORMAT_D24_UNORM_S8_UINT;
            break;
        default:
            VSG_ABSTRACTION_LOG_ERROR("Unsupported image format");
            return nullptr;
        }

        // 创建VSG图像
        auto vsgImage = vsg::Image::create();
        vsgImage->imageType = VK_IMAGE_TYPE_2D;
        vsgImage->format = vkFormat;
        vsgImage->extent = VkExtent3D{info.width, info.height, info.depth};
        vsgImage->mipLevels = info.mipLevels;
        vsgImage->arrayLayers = info.arrayLayers;
        vsgImage->samples = static_cast<VkSampleCountFlagBits>(info.samples);
        vsgImage->usage = info.usage != 0 ? info.usage : VK_IMAGE_USAGE_SAMPLED_BIT;

        if (!vsgImage->compile(vsgDevice_))
        {
            VSG_ABSTRACTION_LOG_ERROR("Failed to compile VSG image");
            return nullptr;
        }

        // 创建图像适配器
        auto imageAdapter = std::make_shared<VSGImageAdapter>(vsgImage);

        _updateStatistics();
        return imageAdapter;
    }

    ref_ptr<IPipeline> VSGRenderEngine::createGraphicsPipeline(const GraphicsPipelineInfo& info)
    {
        if (!vsgDevice_)
        {
            VSG_ABSTRACTION_LOG_ERROR("Cannot create graphics pipeline: VSG device not initialized");
            return nullptr;
        }

        // 创建VSG图形管线
        auto vsgPipeline = vsg::GraphicsPipeline::create();

        // 设置着色器
        for (const auto& shaderInfo : info.shaders)
        {
            auto vsgShader = vsg::ShaderStage::create();

            switch (shaderInfo.stage)
            {
            case ShaderStage::Vertex:
                vsgShader->stage = VK_SHADER_STAGE_VERTEX_BIT;
                break;
            case ShaderStage::Fragment:
                vsgShader->stage = VK_SHADER_STAGE_FRAGMENT_BIT;
                break;
            case ShaderStage::Geometry:
                vsgShader->stage = VK_SHADER_STAGE_GEOMETRY_BIT;
                break;
            case ShaderStage::Compute:
                VSG_ABSTRACTION_LOG_ERROR("Compute shader not supported in graphics pipeline");
                return nullptr;
            }

            if (!shaderInfo.spirv.empty())
            {
                vsgShader->module = vsg::ShaderModule::create(shaderInfo.spirv);
            }
            else if (!shaderInfo.source.empty())
            {
                // 编译GLSL源码到SPIR-V
                auto spirv = vsg::compileGLSL(shaderInfo.source, vsgShader->stage);
                if (spirv.empty())
                {
                    VSG_ABSTRACTION_LOG_ERROR("Failed to compile shader source");
                    return nullptr;
                }
                vsgShader->module = vsg::ShaderModule::create(spirv);
            }

            if (!vsgShader->module)
            {
                VSG_ABSTRACTION_LOG_ERROR("Failed to create shader module");
                return nullptr;
            }

            vsgPipeline->shaderStages.push_back(vsgShader);
        }

        // 设置图元类型
        auto inputAssembly = vsg::InputAssemblyState::create();
        switch (info.topology)
        {
        case PrimitiveTopology::PointList:
            inputAssembly->topology = VK_PRIMITIVE_TOPOLOGY_POINT_LIST;
            break;
        case PrimitiveTopology::LineList:
            inputAssembly->topology = VK_PRIMITIVE_TOPOLOGY_LINE_LIST;
            break;
        case PrimitiveTopology::LineStrip:
            inputAssembly->topology = VK_PRIMITIVE_TOPOLOGY_LINE_STRIP;
            break;
        case PrimitiveTopology::TriangleList:
            inputAssembly->topology = VK_PRIMITIVE_TOPOLOGY_TRIANGLE_LIST;
            break;
        case PrimitiveTopology::TriangleStrip:
            inputAssembly->topology = VK_PRIMITIVE_TOPOLOGY_TRIANGLE_STRIP;
            break;
        case PrimitiveTopology::TriangleFan:
            inputAssembly->topology = VK_PRIMITIVE_TOPOLOGY_TRIANGLE_FAN;
            break;
        }
        vsgPipeline->pipelineStates.push_back(inputAssembly);

        // 设置深度测试
        if (info.depthTest)
        {
            auto depthStencil = vsg::DepthStencilState::create();
            depthStencil->depthTestEnable = VK_TRUE;
            depthStencil->depthWriteEnable = info.depthWrite ? VK_TRUE : VK_FALSE;
            depthStencil->depthCompareOp = VK_COMPARE_OP_LESS;
            vsgPipeline->pipelineStates.push_back(depthStencil);
        }

        // 设置混合
        if (info.blending)
        {
            auto colorBlend = vsg::ColorBlendState::create();
            auto attachment = vsg::ColorBlendAttachmentState::create();
            attachment->blendEnable = VK_TRUE;
            attachment->srcColorBlendFactor = VK_BLEND_FACTOR_SRC_ALPHA;
            attachment->dstColorBlendFactor = VK_BLEND_FACTOR_ONE_MINUS_SRC_ALPHA;
            attachment->colorBlendOp = VK_BLEND_OP_ADD;
            attachment->srcAlphaBlendFactor = VK_BLEND_FACTOR_ONE;
            attachment->dstAlphaBlendFactor = VK_BLEND_FACTOR_ZERO;
            attachment->alphaBlendOp = VK_BLEND_OP_ADD;
            colorBlend->attachments.push_back(attachment);
            vsgPipeline->pipelineStates.push_back(colorBlend);
        }

        // 编译管线
        if (!vsgPipeline->compile(vsgDevice_))
        {
            VSG_ABSTRACTION_LOG_ERROR("Failed to compile graphics pipeline");
            return nullptr;
        }

        // 创建管线适配器
        auto pipelineAdapter = std::make_shared<VSGPipelineAdapter>(vsgPipeline);

        _updateStatistics();
        return pipelineAdapter;
    }

    ref_ptr<IPipeline> VSGRenderEngine::createComputePipeline(const ComputePipelineInfo& info)
    {
        if (!vsgDevice_)
        {
            VSG_ABSTRACTION_LOG_ERROR("Cannot create compute pipeline: VSG device not initialized");
            return nullptr;
        }

        // 创建VSG计算管线
        auto vsgPipeline = vsg::ComputePipeline::create();

        // 设置计算着色器
        auto vsgShader = vsg::ShaderStage::create();
        vsgShader->stage = VK_SHADER_STAGE_COMPUTE_BIT;

        if (!info.computeShader.spirv.empty())
        {
            vsgShader->module = vsg::ShaderModule::create(info.computeShader.spirv);
        }
        else if (!info.computeShader.source.empty())
        {
            // 编译GLSL源码到SPIR-V
            auto spirv = vsg::compileGLSL(info.computeShader.source, VK_SHADER_STAGE_COMPUTE_BIT);
            if (spirv.empty())
            {
                VSG_ABSTRACTION_LOG_ERROR("Failed to compile compute shader source");
                return nullptr;
            }
            vsgShader->module = vsg::ShaderModule::create(spirv);
        }

        if (!vsgShader->module)
        {
            VSG_ABSTRACTION_LOG_ERROR("Failed to create compute shader module");
            return nullptr;
        }

        vsgPipeline->shaderStage = vsgShader;

        // 编译管线
        if (!vsgPipeline->compile(vsgDevice_))
        {
            VSG_ABSTRACTION_LOG_ERROR("Failed to compile compute pipeline");
            return nullptr;
        }

        // 创建管线适配器
        auto pipelineAdapter = std::make_shared<VSGPipelineAdapter>(vsgPipeline);

        _updateStatistics();
        return pipelineAdapter;
    }

    // ========== VSG兼容性接口 ==========

    vsg::ref_ptr<vsg::Group> VSGRenderEngine::createVSGGroup()
    {
        auto vsgGroup = vsg::Group::create();
        _updateStatistics();
        return vsgGroup;
    }

    vsg::ref_ptr<vsg::MatrixTransform> VSGRenderEngine::createVSGTransform()
    {
        auto vsgTransform = vsg::MatrixTransform::create();
        _updateStatistics();
        return vsgTransform;
    }

    vsg::ref_ptr<vsg::Geometry> VSGRenderEngine::createVSGGeometry()
    {
        auto vsgGeometry = vsg::Geometry::create();
        _updateStatistics();
        return vsgGeometry;
    }

    vsg::ref_ptr<vsg::StateGroup> VSGRenderEngine::createVSGStateGroup()
    {
        auto vsgStateGroup = vsg::StateGroup::create();
        _updateStatistics();
        return vsgStateGroup;
    }

    vsg::ref_ptr<vsg::Device> VSGRenderEngine::getVSGDevice()
    {
        return vsgDevice_;
    }

    vsg::ref_ptr<vsg::Instance> VSGRenderEngine::getVSGInstance() const
    {
        return vsgInstance_;
    }

    vsg::ref_ptr<vsg::PhysicalDevice> VSGRenderEngine::getVSGPhysicalDevice() const
    {
        return vsgPhysicalDevice_;
    }

    void VSGRenderEngine::setVSGObjects(vsg::ref_ptr<vsg::Instance> instance, vsg::ref_ptr<vsg::Device> device)
    {
        if (initialized_)
        {
            VSG_ABSTRACTION_LOG_WARN("Cannot set VSG objects: engine already initialized");
            return;
        }

        vsgInstance_ = instance;
        vsgDevice_ = device;
        if (device)
        {
            vsgPhysicalDevice_ = device->getPhysicalDevice();
        }
        usingExistingVSGObjects_ = true;

        VSG_ABSTRACTION_LOG_INFO("VSG objects set successfully");
    }

    bool VSGRenderEngine::isUsingExistingVSGObjects() const
    {
        return usingExistingVSGObjects_;
    }

    // ========== 适配器接口 ==========

    ref_ptr<INode> VSGRenderEngine::wrapVSGNode(vsg::ref_ptr<vsg::Node> vsgNode)
    {
        if (!vsgNode)
        {
            return nullptr;
        }

        // 检查缓存
        {
            std::lock_guard<std::mutex> lock(cacheMutex_);
            auto it = nodeCache_.find(vsgNode.get());
            if (it != nodeCache_.end())
            {
                if (auto adapter = it->second.lock())
                {
                    return adapter;
                }
                else
                {
                    nodeCache_.erase(it);
                }
            }
        }

        // 创建适配器
        ref_ptr<INode> adapter = VSGNodeAdapter::create(vsgNode);

        // 缓存适配器
        {
            std::lock_guard<std::mutex> lock(cacheMutex_);
            nodeCache_[vsgNode.get()] = adapter;
        }

        return adapter;
    }

    vsg::ref_ptr<vsg::Node> VSGRenderEngine::unwrapVSGNode(ref_ptr<INode> abstractNode)
    {
        if (!abstractNode)
        {
            return nullptr;
        }

        return abstractNode->getVSGNode();
    }

    // ========== 同步和性能 ==========

    void VSGRenderEngine::waitIdle()
    {
        if (vsgDevice_)
        {
            vsgDevice_->waitIdle();
        }
    }

    RenderStatistics VSGRenderEngine::getRenderStatistics() const
    {
        std::lock_guard<std::mutex> lock(statisticsMutex_);
        return statistics_;
    }

    void VSGRenderEngine::resetRenderStatistics()
    {
        std::lock_guard<std::mutex> lock(statisticsMutex_);
        statistics_ = RenderStatistics{};
        statistics_.frameCount = 0;
        statistics_.drawCalls = 0;
        statistics_.triangles = 0;
        statistics_.vertices = 0;
        statistics_.frameTime = 0.0;
        statistics_.cpuTime = 0.0;
        statistics_.gpuTime = 0.0;
        statistics_.memoryUsed = 0;
        statistics_.memoryAllocated = 0;
    }

    // ========== 调试和诊断 ==========

    void VSGRenderEngine::setDebugCallback(DebugCallback callback)
    {
        debugCallback_ = callback;
        VSG_ABSTRACTION_LOG_DEBUG("Debug callback set");
    }

    void VSGRenderEngine::beginDebugMarker(const std::string& name)
    {
        debugMarkerStack_.push_back(name);

        if (debugCallback_)
        {
            debugCallback_("Begin marker: " + name, 0);
        }

        // 如果支持Vulkan调试标记，添加到命令缓冲区
        if (vsgDevice_ && vsgDevice_->supportsDeviceExtension(VK_EXT_DEBUG_UTILS_EXTENSION_NAME))
        {
            // 这里可以添加Vulkan调试标记
            VSG_ABSTRACTION_LOG_DEBUG("Debug marker: " << name);
        }
    }

    void VSGRenderEngine::endDebugMarker()
    {
        if (!debugMarkerStack_.empty())
        {
            std::string name = debugMarkerStack_.back();
            debugMarkerStack_.pop_back();

            if (debugCallback_)
            {
                debugCallback_("End marker: " + name, 0);
            }

            VSG_ABSTRACTION_LOG_DEBUG("End debug marker: " << name);
        }
    }

    // ========== 扩展接口 ==========

    void* VSGRenderEngine::getNativeHandle() const
    {
        return vsgDevice_.get();
    }

    void VSGRenderEngine::executeCustomCommand(std::function<void(void*)> command)
    {
        if (command && vsgDevice_)
        {
            command(vsgDevice_.get());
        }
    }

    bool VSGRenderEngine::supportsFeature(const std::string& feature) const
    {
        if (!vsgDevice_)
        {
            return false;
        }

        // 检查常见功能
        if (feature == "geometry_shader")
        {
            return vsgPhysicalDevice_->getFeatures().geometryShader;
        }
        else if (feature == "tessellation_shader")
        {
            return vsgPhysicalDevice_->getFeatures().tessellationShader;
        }
        else if (feature == "compute_shader")
        {
            return true; // VSG总是支持计算着色器
        }
        else if (feature == "multisampling")
        {
            return true; // VSG支持多重采样
        }
        else if (feature.find("VK_") == 0)
        {
            // 检查Vulkan扩展
            return vsgDevice_->supportsDeviceExtension(feature.c_str());
        }

        return false;
    }

    // ========== 内部方法 ==========

    bool VSGRenderEngine::_initializeInstance()
    {
        if (vsgInstance_)
        {
            VSG_ABSTRACTION_LOG_DEBUG("Using existing VSG instance");
            return true;
        }

        VSG_ABSTRACTION_LOG_INFO("Creating new VSG instance...");

        // 创建VSG实例
        auto instanceTraits = vsg::InstanceTraits::create();
        instanceTraits->debugLayer = config_.enableDebugLayers;
        instanceTraits->apiDumpLayer = false;

        // 添加扩展
        for (const auto& ext : config_.vulkan.instanceExtensions)
        {
            instanceTraits->instanceExtensionNames.push_back(ext);
        }

        // 添加验证层
        if (config_.enableValidation)
        {
            for (const auto& layer : config_.vulkan.validationLayers)
            {
                instanceTraits->layers.push_back(layer);
            }
        }

        vsgInstance_ = vsg::Instance::create(instanceTraits);
        if (!vsgInstance_)
        {
            VSG_ABSTRACTION_LOG_ERROR("Failed to create VSG instance");
            return false;
        }

        VSG_ABSTRACTION_LOG_INFO("VSG instance created successfully");
        return true;
    }

    bool VSGRenderEngine::_initializeDevice()
    {
        if (vsgDevice_)
        {
            VSG_ABSTRACTION_LOG_DEBUG("Using existing VSG device");
            return true;
        }

        if (!vsgInstance_)
        {
            VSG_ABSTRACTION_LOG_ERROR("Cannot initialize device: instance not available");
            return false;
        }

        VSG_ABSTRACTION_LOG_INFO("Creating VSG device...");

        // 获取物理设备
        auto physicalDevices = vsgInstance_->getPhysicalDevices();
        if (physicalDevices.empty())
        {
            VSG_ABSTRACTION_LOG_ERROR("No physical devices found");
            return false;
        }

        // 选择最佳物理设备（通常是第一个离散GPU）
        vsgPhysicalDevice_ = physicalDevices[0];
        for (auto& pd : physicalDevices)
        {
            if (pd->getProperties().deviceType == VK_PHYSICAL_DEVICE_TYPE_DISCRETE_GPU)
            {
                vsgPhysicalDevice_ = pd;
                break;
            }
        }

        VSG_ABSTRACTION_LOG_INFO("Selected physical device: " << vsgPhysicalDevice_->getProperties().deviceName);

        // 创建设备
        auto deviceTraits = vsg::DeviceTraits::create(vsgPhysicalDevice_);

        // 添加设备扩展
        for (const auto& ext : config_.vulkan.deviceExtensions)
        {
            deviceTraits->deviceExtensionNames.push_back(ext);
        }

        vsgDevice_ = vsg::Device::create(deviceTraits);
        if (!vsgDevice_)
        {
            VSG_ABSTRACTION_LOG_ERROR("Failed to create VSG device");
            return false;
        }

        VSG_ABSTRACTION_LOG_INFO("VSG device created successfully");
        return true;
    }

    bool VSGRenderEngine::_initializeQueues()
    {
        if (!vsgDevice_)
        {
            VSG_ABSTRACTION_LOG_ERROR("Cannot initialize queues: device not available");
            return false;
        }

        VSG_ABSTRACTION_LOG_INFO("Initializing VSG queues...");

        // 获取图形队列
        vsgQueue_ = vsgDevice_->getQueue(vsgDevice_->getQueueFamily(VK_QUEUE_GRAPHICS_BIT));
        if (!vsgQueue_)
        {
            VSG_ABSTRACTION_LOG_ERROR("Failed to get graphics queue");
            return false;
        }

        // 获取命令池
        vsgCommandPool_ = vsgDevice_->getCommandPool(vsgDevice_->getQueueFamily(VK_QUEUE_GRAPHICS_BIT));
        if (!vsgCommandPool_)
        {
            VSG_ABSTRACTION_LOG_ERROR("Failed to get command pool");
            return false;
        }

        VSG_ABSTRACTION_LOG_INFO("VSG queues initialized successfully");
        return true;
    }

    void VSGRenderEngine::_setupDebugCallback()
    {
        if (config_.enableDebugLayers && vsgInstance_)
        {
            VSG_ABSTRACTION_LOG_DEBUG("Setting up debug callback");

            // 这里可以设置VSG的调试回调
            // VSG会自动处理Vulkan验证层的消息
        }
    }

    void VSGRenderEngine::_updateStatistics()
    {
        std::lock_guard<std::mutex> lock(statisticsMutex_);

        // 更新基本统计信息
        statistics_.frameCount++;

        // 更新内存使用情况（如果可用）
        if (vsgDevice_)
        {
            // 这里可以查询Vulkan内存使用情况
            // 但VSG没有直接提供这个接口，所以我们使用估算值
            statistics_.memoryUsed += 1024; // 简单估算
        }

        // 更新时间信息
        auto now = std::chrono::high_resolution_clock::now();
        static auto lastUpdate = now;
        auto duration = std::chrono::duration_cast<std::chrono::microseconds>(now - lastUpdate);
        statistics_.frameTime = duration.count() / 1000.0; // 转换为毫秒
        lastUpdate = now;
    }

    // ========== VSGDevice Implementation ==========

    VSGDevice::VSGDevice(vsg::ref_ptr<vsg::Device> vsgDevice) :
        vsgDevice_(vsgDevice)
    {
        VSG_ABSTRACTION_LOG_DEBUG("VSGDevice created");
    }

    bool VSGDevice::isValid() const
    {
        return vsgDevice_ != nullptr;
    }

    uint32_t VSGDevice::getDeviceID() const
    {
        if (vsgDevice_)
        {
            return vsgDevice_->deviceID;
        }
        return 0;
    }

    IQueue* VSGDevice::getQueue(uint32_t familyIndex)
    {
        if (!vsgDevice_)
        {
            return nullptr;
        }

        // 确保队列向量足够大
        if (familyIndex >= queues_.size())
        {
            queues_.resize(familyIndex + 1);
        }

        // 如果队列不存在，创建它
        if (!queues_[familyIndex])
        {
            auto vsgQueue = vsgDevice_->getQueue(familyIndex);
            if (vsgQueue)
            {
                queues_[familyIndex] = std::make_unique<VSGQueue>(vsgQueue);
            }
        }

        return queues_[familyIndex].get();
    }

    ICommandPool* VSGDevice::getCommandPool(uint32_t familyIndex)
    {
        if (!vsgDevice_)
        {
            return nullptr;
        }

        // 确保命令池向量足够大
        if (familyIndex >= commandPools_.size())
        {
            commandPools_.resize(familyIndex + 1);
        }

        // 如果命令池不存在，创建它
        if (!commandPools_[familyIndex])
        {
            auto vsgCommandPool = vsgDevice_->getCommandPool(familyIndex);
            if (vsgCommandPool)
            {
                commandPools_[familyIndex] = std::make_unique<VSGCommandPool>(vsgCommandPool);
            }
        }

        return commandPools_[familyIndex].get();
    }

    void VSGDevice::waitIdle()
    {
        if (vsgDevice_)
        {
            vsgDevice_->waitIdle();
        }
    }

    void* VSGDevice::getNativeHandle() const
    {
        return vsgDevice_.get();
    }

    // ========== VSGInstance Implementation ==========

    VSGInstance::VSGInstance(vsg::ref_ptr<vsg::Instance> vsgInstance) :
        vsgInstance_(vsgInstance)
    {
        VSG_ABSTRACTION_LOG_DEBUG("VSGInstance created");
    }

    bool VSGInstance::isValid() const
    {
        return vsgInstance_ != nullptr;
    }

    std::vector<IPhysicalDevice*> VSGInstance::getPhysicalDevices()
    {
        std::vector<IPhysicalDevice*> devices;

        if (vsgInstance_)
        {
            auto vsgPhysicalDevices = vsgInstance_->getPhysicalDevices();

            // 确保物理设备向量足够大
            physicalDevices_.clear();
            physicalDevices_.reserve(vsgPhysicalDevices.size());

            for (auto& vsgPD : vsgPhysicalDevices)
            {
                physicalDevices_.push_back(std::make_unique<VSGPhysicalDevice>(vsgPD));
                devices.push_back(physicalDevices_.back().get());
            }
        }

        return devices;
    }

    void* VSGInstance::getNativeHandle() const
    {
        return vsgInstance_.get();
    }

    // ========== VSGPhysicalDevice Implementation ==========

    VSGPhysicalDevice::VSGPhysicalDevice(vsg::ref_ptr<vsg::PhysicalDevice> vsgPhysicalDevice) :
        vsgPhysicalDevice_(vsgPhysicalDevice)
    {
        VSG_ABSTRACTION_LOG_DEBUG("VSGPhysicalDevice created");
    }

    std::string VSGPhysicalDevice::getDeviceName() const
    {
        if (vsgPhysicalDevice_)
        {
            return vsgPhysicalDevice_->getProperties().deviceName;
        }
        return "Unknown Device";
    }

    DeviceCapabilities VSGPhysicalDevice::getCapabilities() const
    {
        DeviceCapabilities caps;

        if (vsgPhysicalDevice_)
        {
            auto properties = vsgPhysicalDevice_->getProperties();
            auto features = vsgPhysicalDevice_->getFeatures();

            caps.maxTextureSize = properties.limits.maxImageDimension2D;
            caps.maxTextureLayers = properties.limits.maxImageArrayLayers;
            caps.maxUniformBufferSize = properties.limits.maxUniformBufferRange;
            caps.maxVertexAttributes = properties.limits.maxVertexInputAttributes;
            caps.maxViewports = properties.limits.maxViewports;

            caps.supportsGeometryShader = features.geometryShader;
            caps.supportsTessellation = features.tessellationShader;
            caps.supportsComputeShader = true; // Vulkan总是支持计算着色器
            caps.supportsMultisampling = true;

            // 添加支持的格式
            caps.supportedFormats = {
                ImageFormat::RGBA8, ImageFormat::RGBA16F, ImageFormat::RGBA32F,
                ImageFormat::RGB8, ImageFormat::RG8, ImageFormat::R8,
                ImageFormat::Depth24Stencil8};
        }

        return caps;
    }

    bool VSGPhysicalDevice::isDiscrete() const
    {
        if (vsgPhysicalDevice_)
        {
            return vsgPhysicalDevice_->getProperties().deviceType == VK_PHYSICAL_DEVICE_TYPE_DISCRETE_GPU;
        }
        return false;
    }

    void* VSGPhysicalDevice::getNativeHandle() const
    {
        return vsgPhysicalDevice_.get();
    }

} // namespace vsg_abstraction
