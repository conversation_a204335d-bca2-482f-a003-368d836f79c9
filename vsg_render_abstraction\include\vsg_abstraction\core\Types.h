#pragma once

/* <editor-fold desc="MIT License">

Copyright(c) 2024 VSG Render Engine Abstraction

Permission is hereby granted, free of charge, to any person obtaining a copy of this software and associated documentation files (the "Software"), to deal in the Software without restriction, including without limitation the rights to use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of the Software, and to permit persons to whom the Software is furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND <PERSON>NINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.

</editor-fold> */

#include <array>
#include <cstdint>
#include <functional>
#include <memory>
#include <string>
#include <vector>

namespace vsg_abstraction
{

    // ========== 智能指针类型 ==========

    template<typename T>
    using ref_ptr = std::shared_ptr<T>;

    template<typename T, typename... Args>
    ref_ptr<T> make_ref(Args&&... args)
    {
        return std::make_shared<T>(std::forward<Args>(args)...);
    }

    // ========== 基础数学类型 ==========

    using vec2 = std::array<float, 2>;
    using vec3 = std::array<float, 3>;
    using vec4 = std::array<float, 4>;
    using dvec2 = std::array<double, 2>;
    using dvec3 = std::array<double, 3>;
    using dvec4 = std::array<double, 4>;
    using ivec2 = std::array<int32_t, 2>;
    using ivec3 = std::array<int32_t, 3>;
    using ivec4 = std::array<int32_t, 4>;
    using uvec2 = std::array<uint32_t, 2>;
    using uvec3 = std::array<uint32_t, 3>;
    using uvec4 = std::array<uint32_t, 4>;

    using mat3 = std::array<std::array<float, 3>, 3>;
    using mat4 = std::array<std::array<float, 4>, 4>;
    using dmat3 = std::array<std::array<double, 3>, 3>;
    using dmat4 = std::array<std::array<double, 4>, 4>;

    // ========== 枚举类型 ==========

    /**
 * @brief 渲染后端类型
 */
    enum class RenderBackend
    {
        Vulkan,    ///< Vulkan API (原生VSG)
        WebGPU,    ///< WebGPU API
        OpenGL,    ///< OpenGL API
        DirectX12, ///< DirectX 12 API
        Metal,     ///< Metal API (macOS/iOS)
        Mock       ///< Mock实现（用于测试）
    };

    /**
 * @brief 节点类型
 */
    enum class NodeType
    {
        Node,       ///< 基础节点
        Group,      ///< 组节点
        Transform,  ///< 变换节点
        Geometry,   ///< 几何节点
        StateGroup, ///< 状态组节点
        Camera,     ///< 相机节点
        Light       ///< 光源节点
    };

    /**
 * @brief 缓冲区类型
 */
    enum class BufferType
    {
        Vertex,  ///< 顶点缓冲区
        Index,   ///< 索引缓冲区
        Uniform, ///< 统一缓冲区
        Storage, ///< 存储缓冲区
        Staging  ///< 暂存缓冲区
    };

    /**
 * @brief 图像格式
 */
    enum class ImageFormat
    {
        RGBA8,
        RGBA16F,
        RGBA32F,
        RGB8,
        RGB16F,
        RGB32F,
        RG8,
        RG16F,
        RG32F,
        R8,
        R16F,
        R32F,
        Depth16,
        Depth24,
        Depth32F,
        Depth24Stencil8
    };

    /**
 * @brief 着色器阶段
 */
    enum class ShaderStage
    {
        Vertex,
        Fragment,
        Geometry,
        TessellationControl,
        TessellationEvaluation,
        Compute
    };

    /**
 * @brief 图元类型
 */
    enum class PrimitiveTopology
    {
        PointList,
        LineList,
        LineStrip,
        TriangleList,
        TriangleStrip,
        TriangleFan
    };

    /**
 * @brief 缓冲区使用方式
 */
    enum class BufferUsage
    {
        VertexBuffer,
        IndexBuffer,
        UniformBuffer,
        StorageBuffer,
        TransferSrc,
        TransferDst
    };

    /**
 * @brief 图像使用方式
 */
    enum class ImageUsage
    {
        Sampled,
        Storage,
        ColorAttachment,
        DepthStencilAttachment,
        TransferSrc,
        TransferDst
    };

    /**
 * @brief 管线类型
 */
    enum class PipelineType
    {
        Graphics,
        Compute,
        RayTracing
    };

    // ========== 配置结构体 ==========

    /**
 * @brief 窗口特性
 */
    struct WindowTraits
    {
        std::string windowTitle = "VSG Application";
        uint32_t x = 0;
        uint32_t y = 0;
        uint32_t width = 1920;
        uint32_t height = 1080;
        bool fullscreen = false;
        bool decoration = true;
        bool resizable = true;
        bool hdpi = true;
        uint32_t samples = 1;
        bool vsync = true;
        bool debugLayer = false;
        bool apiDumpLayer = false;
        std::vector<std::string> instanceExtensionNames;
        std::vector<std::string> deviceExtensionNames;
    };

    /**
 * @brief 缓冲区信息
 */
    struct BufferInfo
    {
        BufferType type = BufferType::Vertex;
        size_t size = 0;
        const void* data = nullptr;
        bool dynamic = false;
        uint32_t usage = 0;
    };

    /**
 * @brief 图像信息
 */
    struct ImageInfo
    {
        uint32_t width = 1;
        uint32_t height = 1;
        uint32_t depth = 1;
        uint32_t mipLevels = 1;
        uint32_t arrayLayers = 1;
        ImageFormat format = ImageFormat::RGBA8;
        uint32_t samples = 1;
        uint32_t usage = 0;
        const void* data = nullptr;
        size_t dataSize = 0;
    };

    /**
 * @brief 着色器信息
 */
    struct ShaderInfo
    {
        ShaderStage stage = ShaderStage::Vertex;
        std::string source;
        std::string entryPoint = "main";
        std::vector<uint32_t> spirv;
        std::vector<std::string> defines;
    };

    /**
 * @brief 图形管线信息
 */
    struct GraphicsPipelineInfo
    {
        std::vector<ShaderInfo> shaders;
        PrimitiveTopology topology = PrimitiveTopology::TriangleList;
        bool depthTest = true;
        bool depthWrite = true;
        bool blending = false;
        // 更多管线状态...
    };

    /**
 * @brief 计算管线信息
 */
    struct ComputePipelineInfo
    {
        ShaderInfo computeShader;
        uvec3 localSize = {1, 1, 1};
    };

    // ========== 配置结构体 ==========

    /**
 * @brief 渲染引擎配置
 */
    struct RenderEngineConfig
    {
        bool enableDebugLayers = false;
        bool enableValidation = false;
        bool enableProfiling = false;
        uint32_t maxFramesInFlight = 2;

        // Vulkan特定配置
        struct VulkanConfig
        {
#ifdef VSG_ABSTRACTION_HAS_VSG
            vsg::ref_ptr<vsg::Instance> instance = nullptr;
            vsg::ref_ptr<vsg::Device> device = nullptr;
            vsg::ref_ptr<vsg::PhysicalDevice> physicalDevice = nullptr;
#else
            void* instance = nullptr;
            void* device = nullptr;
            void* physicalDevice = nullptr;
#endif
            bool useExistingVSGObjects = false;
            std::vector<const char*> instanceExtensions;
            std::vector<const char*> deviceExtensions;
            std::vector<const char*> validationLayers;
        } vulkan;

        // WebGPU特定配置
        struct WebGPUConfig
        {
            std::string powerPreference = "high-performance";
            bool enableDebugAdapter = false;
        } webgpu;

        // OpenGL特定配置
        struct OpenGLConfig
        {
            int majorVersion = 4;
            int minorVersion = 5;
            bool coreProfile = true;
            bool enableDebugContext = false;
        } opengl;

        // Mock特定配置
        struct MockConfig
        {
            bool simulateErrors = false;
            float errorRate = 0.0f;
            uint32_t simulatedDelay = 0; // 毫秒
            bool enableStatistics = true;
        } mock;
    };

    // ========== 信息结构体 ==========

    /**
 * @brief 引擎信息
 */
    struct EngineInfo
    {
        std::string name;
        std::string version;
        std::string vendor;
        RenderBackend backend;
        std::vector<std::string> extensions;
        std::vector<std::string> layers;
    };

    /**
 * @brief 设备能力信息
 */
    struct DeviceCapabilities
    {
        uint32_t maxTextureSize = 0;
        uint32_t maxTextureLayers = 0;
        uint32_t maxUniformBufferSize = 0;
        uint32_t maxVertexAttributes = 0;
        uint32_t maxViewports = 0;
        bool supportsGeometryShader = false;
        bool supportsTessellation = false;
        bool supportsComputeShader = false;
        bool supportsMultisampling = false;
        std::vector<ImageFormat> supportedFormats;
    };

    /**
 * @brief 渲染统计信息
 */
    struct RenderStatistics
    {
        uint64_t frameCount = 0;
        uint64_t drawCalls = 0;
        uint64_t triangles = 0;
        uint64_t vertices = 0;
        double frameTime = 0.0;     ///< 帧时间（毫秒）
        double cpuTime = 0.0;       ///< CPU时间（毫秒）
        double gpuTime = 0.0;       ///< GPU时间（毫秒）
        size_t memoryUsed = 0;      ///< 显存使用量（字节）
        size_t memoryAllocated = 0; ///< 显存分配量（字节）
    };

    // ========== 回调函数类型 ==========

    /**
 * @brief 调试回调函数类型
 */
    using DebugCallback = std::function<void(const std::string& message, int severity)>;

    /**
 * @brief 错误回调函数类型
 */
    using ErrorCallback = std::function<void(const std::string& error)>;

    /**
 * @brief 窗口事件回调函数类型
 */
    using WindowEventCallback = std::function<void(int eventType, void* eventData)>;

    /**
 * @brief 渲染完成回调函数类型
 */
    using RenderCompleteCallback = std::function<void(const RenderStatistics& stats)>;

    // ========== 工具函数 ==========

    /**
 * @brief 获取渲染后端名称
 */
    inline std::string getRenderBackendName(RenderBackend backend)
    {
        switch (backend)
        {
        case RenderBackend::Vulkan: return "Vulkan";
        case RenderBackend::WebGPU: return "WebGPU";
        case RenderBackend::OpenGL: return "OpenGL";
        case RenderBackend::DirectX12: return "DirectX 12";
        case RenderBackend::Metal: return "Metal";
        case RenderBackend::Mock: return "Mock";
        default: return "Unknown";
        }
    }

    /**
 * @brief 获取图像格式的字节大小
 */
    inline size_t getImageFormatSize(ImageFormat format)
    {
        switch (format)
        {
        case ImageFormat::RGBA8: return 4;
        case ImageFormat::RGBA16F: return 8;
        case ImageFormat::RGBA32F: return 16;
        case ImageFormat::RGB8: return 3;
        case ImageFormat::RGB16F: return 6;
        case ImageFormat::RGB32F: return 12;
        case ImageFormat::RG8: return 2;
        case ImageFormat::RG16F: return 4;
        case ImageFormat::RG32F: return 8;
        case ImageFormat::R8: return 1;
        case ImageFormat::R16F: return 2;
        case ImageFormat::R32F: return 4;
        default: return 0;
        }
    }

    /**
 * @brief 创建单位矩阵
 */
    inline mat4 identity4()
    {
        return {{{{1.0f, 0.0f, 0.0f, 0.0f}},
                 {{0.0f, 1.0f, 0.0f, 0.0f}},
                 {{0.0f, 0.0f, 1.0f, 0.0f}},
                 {{0.0f, 0.0f, 0.0f, 1.0f}}}};
    }

    /**
 * @brief 创建平移矩阵
 */
    inline mat4 translate(const vec3& translation)
    {
        auto result = identity4();
        result[3][0] = translation[0];
        result[3][1] = translation[1];
        result[3][2] = translation[2];
        return result;
    }

    /**
 * @brief 创建缩放矩阵
 */
    inline mat4 scale(const vec3& scaling)
    {
        auto result = identity4();
        result[0][0] = scaling[0];
        result[1][1] = scaling[1];
        result[2][2] = scaling[2];
        return result;
    }

    // ========== 常量定义 ==========

    namespace constants
    {
        constexpr uint32_t MAX_TEXTURE_UNITS = 32;
        constexpr uint32_t MAX_VERTEX_ATTRIBUTES = 16;
        constexpr uint32_t MAX_UNIFORM_BUFFERS = 16;
        constexpr uint32_t MAX_STORAGE_BUFFERS = 16;
        constexpr uint32_t DEFAULT_ALIGNMENT = 256;
        constexpr double PI = 3.14159265358979323846;
        constexpr float PI_F = 3.14159265358979323846f;
    } // namespace constants

} // namespace vsg_abstraction
