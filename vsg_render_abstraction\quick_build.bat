@echo off
echo Quick Build Test
echo ================

cd build_test

echo Step 1: Clean previous build...
cmake --build . --target clean

echo Step 2: Configure with minimal options...
cmake .. -DVSG_ABSTRACTION_BUILD_VSG_BACKEND=OFF -DVSG_ABSTRACTION_BUILD_WEBGPU_BACKEND=OFF -DVSG_ABSTRACTION_BUILD_OPENGL_BACKEND=OFF -DVSG_ABSTRACTION_BUILD_MOCK_BACKEND=ON -DVSG_ABSTRACTION_BUILD_TESTS=ON -DVSG_ABSTRACTION_BUILD_EXAMPLES=ON

echo Step 3: Build...
cmake --build . --config Release --parallel

if errorlevel 1 (
    echo Build failed!
    pause
    exit /b 1
) else (
    echo Build successful!
    echo Running basic test...
    if exist tests\Release\test_basic.exe (
        tests\Release\test_basic.exe
    ) else (
        echo Test executable not found
    )
)

pause
