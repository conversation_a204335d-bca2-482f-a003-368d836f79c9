// include/rocky_webgpu/TerrainRenderer.h
#pragma once
#include <rocky/vsg/engine/TerrainTileNode.h>
#include <rocky_webgpu/State.h>

namespace rocky_webgpu
{

    // 地形瓦片渲染器
    class TerrainTileRenderer
    {
    public:
        TerrainTileRenderer(Device* device);

        // 渲染地形瓦片
        void render(rocky::TerrainTileNode* tile, State& state);

        // 更新瓦片数据
        void updateTileGeometry(rocky::TerrainTileNode* tile);
        void updateTileTextures(rocky::TerrainTileNode* tile);

    private:
        vsg::ref_ptr<Device> _device;

        // 地形着色器
        std::string _terrainVertexShader;
        std::string _terrainFragmentShader;

        // 瓦片资源管理
        struct TileResources
        {
            wgpu::Buffer vertexBuffer;
            wgpu::Buffer indexBuffer;
            wgpu::Buffer uniformBuffer;
            wgpu::BindGroup bindGroup;
            uint32_t indexCount = 0;
        };

        std::unordered_map<rocky::TerrainTileNode*, TileResources> _tileResources;

        void createTerrainShaders();
        TileResources createTileResources(rocky::TerrainTileNode* tile);
    };

    // 实现
    TerrainTileRenderer::TerrainTileRenderer(Device* device) :
        _device(device)
    {
        createTerrainShaders();
    }

    void TerrainTileRenderer::createTerrainShaders()
    {
        // 地形顶点着色器
        _terrainVertexShader = R"(
struct Uniforms {
    mvpMatrix: mat4x4<f32>,
    normalMatrix: mat3x3<f32>,
    tileParams: vec4<f32>,  // minX, minY, sizeX, sizeY
    elevParams: vec2<f32>,  // scale, offset
}

struct VertexInput {
    @location(0) position: vec2<f32>,  // 归一化的瓦片坐标[0,1]
    @location(1) normal: vec3<f32>,
    @location(2) texCoord: vec2<f32>,
}

struct VertexOutput {
    @builtin(position) position: vec4<f32>,
    @location(0) worldPos: vec3<f32>,
    @location(1) normal: vec3<f32>,
    @location(2) texCoord: vec2<f32>,
    @location(3) tileCoord: vec2<f32>,
}

@group(0) @binding(0) var<uniform> uniforms: Uniforms;
@group(0) @binding(1) var elevSampler: sampler;
@group(0) @binding(2) var elevTexture: texture_2d<f32>;

fn sampleElevation(coord: vec2<f32>) -> f32 {
    let elev = textureSampleLevel(elevTexture, elevSampler, coord, 0.0).r;
    return elev * uniforms.elevParams.x + uniforms.elevParams.y;
}

@vertex
fn vs_main(input: VertexInput) -> VertexOutput {
    var output: VertexOutput;
    
    // 计算世界坐标
    let worldX = uniforms.tileParams.x + input.position.x * uniforms.tileParams.z;
    let worldY = uniforms.tileParams.y + input.position.y * uniforms.tileParams.w;
    
    // 采样高程
    let elevation = sampleElevation(input.texCoord);
    
    // 3D位置
    output.worldPos = vec3<f32>(worldX, worldY, elevation);
    
    // 计算法线（如果未提供，使用有限差分）
    var normal = input.normal;
    if (length(normal) < 0.1) {
        let texelSize = 1.0 / 256.0;
        let hL = sampleElevation(input.texCoord + vec2<f32>(-texelSize, 0.0));
        let hR = sampleElevation(input.texCoord + vec2<f32>(texelSize, 0.0));
        let hD = sampleElevation(input.texCoord + vec2<f32>(0.0, -texelSize));
        let hU = sampleElevation(input.texCoord + vec2<f32>(0.0, texelSize));
        
        let dx = (hR - hL) / (2.0 * texelSize * uniforms.tileParams.z);
        let dy = (hU - hD) / (2.0 * texelSize * uniforms.tileParams.w);
        
        normal = normalize(vec3<f32>(-dx, -dy, 1.0));
    }
    
    output.normal = normalize(uniforms.normalMatrix * normal);
    output.texCoord = input.texCoord;
    output.tileCoord = input.position;
    output.position = uniforms.mvpMatrix * vec4<f32>(output.worldPos, 1.0);
    
    return output;
}
)";

        // 地形片段着色器
        _terrainFragmentShader = R"(
struct FragmentInput {
    @location(0) worldPos: vec3<f32>,
    @location(1) normal: vec3<f32>,
    @location(2) texCoord: vec2<f32>,
    @location(3) tileCoord: vec2<f32>,
}

@group(1) @binding(0) var colorSampler: sampler;
@group(1) @binding(1) var colorTexture: texture_2d<f32>;
@group(1) @binding(2) var<uniform> lightDir: vec3<f32>;

@fragment
fn fs_main(input: FragmentInput) -> @location(0) vec4<f32> {
    // 采样颜色纹理
    var color = textureSample(colorTexture, colorSampler, input.texCoord);
    
    // 简单光照
    let normal = normalize(input.normal);
    let NdotL = max(dot(normal, normalize(lightDir)), 0.0);
    let ambient = 0.3;
    let diffuse = 0.7 * NdotL;
    
    // 高度雾
    let fogStart = 100.0;
    let fogEnd = 10000.0;
    let height = input.worldPos.z;
    let fogFactor = clamp((height - fogStart) / (fogEnd - fogStart), 0.0, 1.0);
    
    // 最终颜色
    let lit = color.rgb * (ambient + diffuse);
    let fogColor = vec3<f32>(0.8, 0.85, 0.9);
    let finalColor = mix(lit, fogColor, fogFactor * 0.5);
    
    return vec4<f32>(finalColor, color.a);
}
)";
    }

    void TerrainTileRenderer::render(rocky::TerrainTileNode* tile, State& state)
    {
        if (!tile || !tile->getMesh()) return;

        // 获取或创建瓦片资源
        auto it = _tileResources.find(tile);
        if (it == _tileResources.end())
        {
            _tileResources[tile] = createTileResources(tile);
            it = _tileResources.find(tile);
        }

        auto& resources = it->second;
        auto encoder = state.getRenderPassEncoder();

        // 设置顶点缓冲区
        encoder.SetVertexBuffer(0, resources.vertexBuffer);
        encoder.SetIndexBuffer(resources.indexBuffer, wgpu::IndexFormat::Uint32);

        // 更新统一缓冲区
        struct Uniforms
        {
            vsg::mat4 mvpMatrix;
            vsg::mat3 normalMatrix;
            vsg::vec4 tileParams; // minX, minY, sizeX, sizeY
            vsg::vec2 elevParams; // scale, offset
        } uniforms;

        uniforms.mvpMatrix = state.getMVPMatrix();
        uniforms.normalMatrix = vsg::mat3(state.getViewMatrix() * state.getModelMatrix());

        // 获取瓦片边界
        auto& key = tile->getKey();
        auto extent = key.getExtent();
        uniforms.tileParams = vsg::vec4(
            extent.xmin(), extent.ymin(),
            extent.width(), extent.height());
        uniforms.elevParams = vsg::vec2(1000.0f, 0.0f); // 示例值

        _device->getQueue().WriteBuffer(
            resources.uniformBuffer, 0,
            &uniforms, sizeof(uniforms));

        // 设置绑定组
        encoder.SetBindGroup(0, resources.bindGroup);

        // 绘制
        encoder.DrawIndexed(resources.indexCount);
    }

} // namespace rocky_webgpu
