cmake_minimum_required(VERSION 3.16)

# 简单测试项目
project(SimpleTest)

# 设置C++标准
set(CMAKE_CXX_STANDARD 20)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# 添加包含目录
include_directories(${CMAKE_CURRENT_SOURCE_DIR}/include)
include_directories(${CMAKE_CURRENT_SOURCE_DIR}/build_test/include)

# 添加库目录
link_directories(${CMAKE_CURRENT_SOURCE_DIR}/build_test/lib/Release)

# 创建可执行文件
add_executable(simple_test simple_test.cpp)

# 链接库
target_link_libraries(simple_test vsg_abstraction)

# 设置输出目录
set_target_properties(simple_test PROPERTIES
    RUNTIME_OUTPUT_DIRECTORY ${CMAKE_CURRENT_SOURCE_DIR}/build_test/bin/Release
)

# 复制DLL到输出目录（Windows）
if(WIN32)
    add_custom_command(TARGET simple_test POST_BUILD
        COMMAND ${CMAKE_COMMAND} -E copy_if_different
        "${CMAKE_CURRENT_SOURCE_DIR}/build_test/bin/Release/vsg_abstraction.dll"
        $<TARGET_FILE_DIR:simple_test>
    )
endif()
