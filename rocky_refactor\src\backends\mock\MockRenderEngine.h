#pragma once

/* <editor-fold desc="MIT License">

Copyright(c) 2024 Rocky Render Engine Refactor

Permission is hereby granted, free of charge, to any person obtaining a copy of this software and associated documentation files (the "Software"), to deal in the Software without restriction, including without limitation the rights to use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of the Software, and to permit persons to whom the Software is furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.

</editor-fold> */

#include <rocky/render/IRenderEngine.h>
#include <rocky/render/ISceneNode.h>
#include <rocky/render/Config.h>
#include <memory>
#include <vector>
#include <unordered_map>
#include <mutex>
#include <chrono>

namespace rocky {
namespace render {

// 前向声明
class MockWindow;
class MockSceneNode;
class MockRenderGraph;
class MockTexture;
class MockBuffer;
class MockShader;
class MockPipeline;
class MockCamera;

/**
 * @brief Mock渲染引擎实现
 * 
 * 用于测试和开发的Mock实现，不进行实际渲染，
 * 但提供完整的API兼容性和基本的功能验证。
 */
class MockRenderEngine : public IRenderEngine {
public:
    explicit MockRenderEngine(const void* config = nullptr);
    virtual ~MockRenderEngine();

    // ========== 生命周期管理 ==========
    bool initialize() override;
    void shutdown() override;
    bool isInitialized() const override;

    // ========== 设备和上下文管理 ==========
    RenderBackend getBackendType() const override;
    RenderEngineInfo getEngineInfo() const override;
    DeviceCapabilities getDeviceCapabilities() const override;

    // ========== 窗口和表面管理 ==========
    std::shared_ptr<IWindow> createWindow(const WindowConfig& config) override;
    void destroyWindow(std::shared_ptr<IWindow> window) override;

    // ========== 场景图管理 ==========
    std::shared_ptr<ISceneNode> createGroup() override;
    std::shared_ptr<ISceneNode> createTransform() override;
    std::shared_ptr<ISceneNode> createGeometry(const GeometryData& geometry) override;
    std::shared_ptr<ISceneNode> createStateGroup() override;

    // ========== 渲染图管理 ==========
    std::shared_ptr<IRenderGraph> createRenderGraph(std::shared_ptr<IWindow> window) override;
    bool render(std::shared_ptr<IRenderGraph> renderGraph) override;

    // ========== 资源管理 ==========
    std::shared_ptr<ITexture> createTexture(const TextureConfig& config) override;
    std::shared_ptr<IBuffer> createBuffer(const BufferConfig& config) override;
    std::shared_ptr<IShader> createShader(const ShaderConfig& config) override;
    std::shared_ptr<IPipeline> createPipeline(const PipelineConfig& config) override;

    // ========== 相机管理 ==========
    std::shared_ptr<ICamera> createCamera(const CameraConfig& config) override;

    // ========== 同步和性能 ==========
    void waitIdle() override;
    RenderStatistics getRenderStatistics() const override;
    void resetRenderStatistics() override;

    // ========== 调试和诊断 ==========
    void setDebugCallback(DebugCallback callback) override;
    void beginDebugMarker(const std::string& name) override;
    void endDebugMarker() override;

    // ========== 扩展接口 ==========
    void* getNativeHandle() const override;
    void executeCustomCommand(std::function<void(void*)> command) override;

    // ========== Mock特定功能 ==========
    
    /**
     * @brief 设置模拟渲染延迟
     * @param delayMs 延迟时间（毫秒）
     */
    void setRenderDelay(uint32_t delayMs);
    
    /**
     * @brief 设置模拟错误
     * @param errorRate 错误率（0.0-1.0）
     */
    void setErrorRate(float errorRate);
    
    /**
     * @brief 获取创建的对象统计
     */
    struct ObjectStats {
        size_t windows = 0;
        size_t sceneNodes = 0;
        size_t renderGraphs = 0;
        size_t textures = 0;
        size_t buffers = 0;
        size_t shaders = 0;
        size_t pipelines = 0;
        size_t cameras = 0;
    };
    
    ObjectStats getObjectStats() const;

private:
    // 内部状态
    mutable std::mutex mutex_;
    bool initialized_ = false;
    uint32_t renderDelay_ = 0;
    float errorRate_ = 0.0f;
    
    // 回调函数
    DebugCallback debugCallback_;
    
    // 统计信息
    mutable RenderStatistics statistics_;
    mutable ObjectStats objectStats_;
    
    // 调试标记栈
    std::vector<std::string> debugMarkerStack_;
    
    // 时间记录
    std::chrono::high_resolution_clock::time_point lastRenderTime_;
    
    // 内部方法
    void _updateStatistics();
    bool _shouldSimulateError() const;
    void _simulateRenderDelay() const;
    void _logDebug(const std::string& message) const;
};

} // namespace render
} // namespace rocky
