# VSG渲染引擎抽象层测试程序构建配置

# ========== 基础测试 ==========

# 基础功能测试
add_executable(test_basic test_basic.cpp)
target_link_libraries(test_basic VSGAbstraction)
target_include_directories(test_basic PRIVATE 
    ${CMAKE_CURRENT_SOURCE_DIR}/../include
    ${CMAKE_CURRENT_BINARY_DIR}/../include
)

# 添加到CTest
add_test(NAME BasicFunctionality COMMAND test_basic)
set_tests_properties(BasicFunctionality PROPERTIES
    TIMEOUT 30
    LABELS "basic;core"
)

# ========== VSG集成测试 ==========

if(VSG_ABSTRACTION_BUILD_VSG_BACKEND AND VSG_ABSTRACTION_HAS_VSG)
    # VSG集成测试
    add_executable(test_vsg_integration test_vsg_integration.cpp)
    target_link_libraries(test_vsg_integration VSGAbstraction)
    target_include_directories(test_vsg_integration PRIVATE 
        ${CMAKE_CURRENT_SOURCE_DIR}/../include
        ${CMAKE_CURRENT_BINARY_DIR}/../include
    )
    
    # 添加到CTest
    add_test(NAME VSGIntegration COMMAND test_vsg_integration)
    set_tests_properties(VSGIntegration PROPERTIES
        TIMEOUT 60
        LABELS "vsg;integration"
    )
    
    # VSG兼容性测试
    add_executable(test_vsg_compatibility test_vsg_compatibility.cpp)
    target_link_libraries(test_vsg_compatibility VSGAbstraction)
    target_include_directories(test_vsg_compatibility PRIVATE 
        ${CMAKE_CURRENT_SOURCE_DIR}/../include
        ${CMAKE_CURRENT_BINARY_DIR}/../include
    )
    
    add_test(NAME VSGCompatibility COMMAND test_vsg_compatibility)
    set_tests_properties(VSGCompatibility PROPERTIES
        TIMEOUT 60
        LABELS "vsg;compatibility"
    )
endif()

# ========== 多后端测试 ==========

# 多后端切换测试
add_executable(test_multi_backend test_multi_backend.cpp)
target_link_libraries(test_multi_backend VSGAbstraction)
target_include_directories(test_multi_backend PRIVATE 
    ${CMAKE_CURRENT_SOURCE_DIR}/../include
    ${CMAKE_CURRENT_BINARY_DIR}/../include
)

add_test(NAME MultiBackend COMMAND test_multi_backend)
set_tests_properties(MultiBackend PROPERTIES
    TIMEOUT 45
    LABELS "backend;switching"
)

# ========== 性能测试 ==========

# 性能基准测试
add_executable(test_performance test_performance.cpp)
target_link_libraries(test_performance VSGAbstraction)
target_include_directories(test_performance PRIVATE 
    ${CMAKE_CURRENT_SOURCE_DIR}/../include
    ${CMAKE_CURRENT_BINARY_DIR}/../include
)

add_test(NAME Performance COMMAND test_performance)
set_tests_properties(Performance PROPERTIES
    TIMEOUT 120
    LABELS "performance;benchmark"
)

# ========== 内存测试 ==========

# 内存泄漏测试
add_executable(test_memory test_memory.cpp)
target_link_libraries(test_memory VSGAbstraction)
target_include_directories(test_memory PRIVATE 
    ${CMAKE_CURRENT_SOURCE_DIR}/../include
    ${CMAKE_CURRENT_BINARY_DIR}/../include
)

add_test(NAME Memory COMMAND test_memory)
set_tests_properties(Memory PROPERTIES
    TIMEOUT 60
    LABELS "memory;leak"
)

# ========== 场景图测试 ==========

# 场景图功能测试
add_executable(test_scene_graph test_scene_graph.cpp)
target_link_libraries(test_scene_graph VSGAbstraction)
target_include_directories(test_scene_graph PRIVATE 
    ${CMAKE_CURRENT_SOURCE_DIR}/../include
    ${CMAKE_CURRENT_BINARY_DIR}/../include
)

add_test(NAME SceneGraph COMMAND test_scene_graph)
set_tests_properties(SceneGraph PROPERTIES
    TIMEOUT 45
    LABELS "scene;graph"
)

# ========== 编译器特定设置 ==========

# 为所有测试设置编译选项
set(TEST_TARGETS 
    test_basic 
    test_multi_backend 
    test_performance 
    test_memory 
    test_scene_graph
)

if(VSG_ABSTRACTION_BUILD_VSG_BACKEND AND VSG_ABSTRACTION_HAS_VSG)
    list(APPEND TEST_TARGETS test_vsg_integration test_vsg_compatibility)
endif()

foreach(target ${TEST_TARGETS})
    # C++20标准
    set_target_properties(${target} PROPERTIES
        CXX_STANDARD 20
        CXX_STANDARD_REQUIRED ON
        CXX_EXTENSIONS OFF
    )
    
    # 编译器特定选项
    if(MSVC)
        target_compile_options(${target} PRIVATE
            /W4 /WX- /permissive- /Zc:__cplusplus /utf-8
        )
    else()
        target_compile_options(${target} PRIVATE
            -Wall -Wextra -Wpedantic -Wno-unused-parameter
        )
    endif()
    
    # 调试信息
    if(CMAKE_BUILD_TYPE STREQUAL "Debug")
        target_compile_definitions(${target} PRIVATE VSG_ABSTRACTION_DEBUG=1)
    endif()
    
    # 设置输出目录
    set_target_properties(${target} PROPERTIES
        RUNTIME_OUTPUT_DIRECTORY "${CMAKE_BINARY_DIR}/tests"
    )
endforeach()

# ========== 测试数据 ==========

# 复制测试数据文件（如果有的话）
if(EXISTS "${CMAKE_CURRENT_SOURCE_DIR}/data")
    file(COPY "${CMAKE_CURRENT_SOURCE_DIR}/data"
         DESTINATION "${CMAKE_BINARY_DIR}/tests")
endif()

# ========== 自定义测试目标 ==========

# 运行所有测试的自定义目标
add_custom_target(run_all_tests
    COMMAND ${CMAKE_CTEST_COMMAND} --output-on-failure --parallel 4
    DEPENDS ${TEST_TARGETS}
    COMMENT "Running all VSG Abstraction tests"
)

# 运行基础测试
add_custom_target(run_basic_tests
    COMMAND ${CMAKE_CTEST_COMMAND} --output-on-failure -L "basic"
    DEPENDS test_basic
    COMMENT "Running basic tests"
)

# 运行VSG测试
if(VSG_ABSTRACTION_BUILD_VSG_BACKEND AND VSG_ABSTRACTION_HAS_VSG)
    add_custom_target(run_vsg_tests
        COMMAND ${CMAKE_CTEST_COMMAND} --output-on-failure -L "vsg"
        DEPENDS test_vsg_integration test_vsg_compatibility
        COMMENT "Running VSG tests"
    )
endif()

# 运行性能测试
add_custom_target(run_performance_tests
    COMMAND ${CMAKE_CTEST_COMMAND} --output-on-failure -L "performance"
    DEPENDS test_performance
    COMMENT "Running performance tests"
)

# ========== 测试报告 ==========

# 生成测试报告的自定义目标
add_custom_target(test_report
    COMMAND ${CMAKE_CTEST_COMMAND} --output-on-failure --parallel 4 --output-junit "${CMAKE_BINARY_DIR}/test_results.xml"
    DEPENDS ${TEST_TARGETS}
    COMMENT "Generating test report"
)

# ========== 状态报告 ==========

message(STATUS "")
message(STATUS "Test Configuration:")
message(STATUS "  Basic tests: ENABLED")
message(STATUS "  Multi-backend tests: ENABLED")
message(STATUS "  Performance tests: ENABLED")
message(STATUS "  Memory tests: ENABLED")
message(STATUS "  Scene graph tests: ENABLED")

if(VSG_ABSTRACTION_BUILD_VSG_BACKEND AND VSG_ABSTRACTION_HAS_VSG)
    message(STATUS "  VSG integration tests: ENABLED")
    message(STATUS "  VSG compatibility tests: ENABLED")
else()
    message(STATUS "  VSG tests: DISABLED (VSG not available)")
endif()

message(STATUS "  Test output directory: ${CMAKE_BINARY_DIR}/tests")
message(STATUS "")
