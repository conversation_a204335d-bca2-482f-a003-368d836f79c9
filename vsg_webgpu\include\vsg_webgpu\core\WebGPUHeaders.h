#pragma once

/* <editor-fold desc="MIT License">

Copyright(c) 2024 VSG WebGPU Extension

Permission is hereby granted, free of charge, to any person obtaining a copy of this software and associated documentation files (the "Software"), to deal in the Software without restriction, including without limitation the rights to use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of the Software, and to permit persons to whom the Software is furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.

</editor-fold> */

#include <vsg_webgpu/core/Export.h>
#include <vsg_webgpu/core/vsg_webgpu_type_name.h>

// WebGPU头文件包含
#if VSG_WEBGPU_PLATFORM_EMSCRIPTEN
    // Emscripten WebGPU
    #include <emscripten/html5_webgpu.h>
    #include <webgpu/webgpu.h>
    #include <webgpu/webgpu_cpp.h>
#elif VSG_WEBGPU_USE_MOCK
    // Mock WebGPU implementation for testing
    #include <vsg_webgpu/core/MockWebGPU.h>
#else
    // Dawn WebGPU
    #include <dawn/webgpu_cpp.h>
    #include <dawn/native/DawnNative.h>
    #include <dawn/dawn_proc.h>
#endif

// 通用WebGPU类型别名
namespace vsg_webgpu
{
    // WebGPU对象类型
    using WGPUInstance = wgpu::Instance;
    using WGPUAdapter = wgpu::Adapter;
    using WGPUDevice = wgpu::Device;
    using WGPUQueue = wgpu::Queue;
    using WGPUSurface = wgpu::Surface;
    using WGPUSwapChain = wgpu::SwapChain;
    using WGPUCommandEncoder = wgpu::CommandEncoder;
    using WGPUCommandBuffer = wgpu::CommandBuffer;
    using WGPURenderPassEncoder = wgpu::RenderPassEncoder;
    using WGPUComputePassEncoder = wgpu::ComputePassEncoder;
    using WGPUBuffer = wgpu::Buffer;
    using WGPUTexture = wgpu::Texture;
    using WGPUTextureView = wgpu::TextureView;
    using WGPUSampler = wgpu::Sampler;
    using WGPUShaderModule = wgpu::ShaderModule;
    using WGPURenderPipeline = wgpu::RenderPipeline;
    using WGPUComputePipeline = wgpu::ComputePipeline;
    using WGPUBindGroup = wgpu::BindGroup;
    using WGPUBindGroupLayout = wgpu::BindGroupLayout;
    using WGPUPipelineLayout = wgpu::PipelineLayout;

    // WebGPU枚举类型
    using WGPUTextureFormat = wgpu::TextureFormat;
    using WGPUBufferUsage = wgpu::BufferUsage;
    using WGPUTextureUsage = wgpu::TextureUsage;
    using WGPUShaderStage = wgpu::ShaderStage;
    using WGPUPrimitiveTopology = wgpu::PrimitiveTopology;
    using WGPUIndexFormat = wgpu::IndexFormat;
    using WGPUCompareFunction = wgpu::CompareFunction;
    using WGPUBlendOperation = wgpu::BlendOperation;
    using WGPUBlendFactor = wgpu::BlendFactor;
    using WGPUCullMode = wgpu::CullMode;
    using WGPUFrontFace = wgpu::FrontFace;

    // WebGPU描述符结构
    using WGPUInstanceDescriptor = wgpu::InstanceDescriptor;
    using WGPUAdapterOptions = wgpu::RequestAdapterOptions;
    using WGPUDeviceDescriptor = wgpu::DeviceDescriptor;
    using WGPUSwapChainDescriptor = wgpu::SwapChainDescriptor;
    using WGPUBufferDescriptor = wgpu::BufferDescriptor;
    using WGPUTextureDescriptor = wgpu::TextureDescriptor;
    using WGPUSamplerDescriptor = wgpu::SamplerDescriptor;
    using WGPUShaderModuleDescriptor = wgpu::ShaderModuleDescriptor;
    using WGPURenderPipelineDescriptor = wgpu::RenderPipelineDescriptor;
    using WGPUComputePipelineDescriptor = wgpu::ComputePipelineDescriptor;
    using WGPUBindGroupDescriptor = wgpu::BindGroupDescriptor;
    using WGPUBindGroupLayoutDescriptor = wgpu::BindGroupLayoutDescriptor;
    using WGPUPipelineLayoutDescriptor = wgpu::PipelineLayoutDescriptor;
    using WGPURenderPassDescriptor = wgpu::RenderPassDescriptor;
    using WGPUComputePassDescriptor = wgpu::ComputePassDescriptor;

    // 初始化函数
    void initializeWebGPU();
    void shutdownWebGPU();

    // 错误处理
    using ErrorCallback = std::function<void(const std::string&)>;
    void setErrorCallback(ErrorCallback callback);
    void reportError(const std::string& message);

} // namespace vsg_webgpu
