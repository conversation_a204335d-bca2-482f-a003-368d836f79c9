#pragma once

/* <editor-fold desc="MIT License">

Copyright(c) 2024 VSG WebGPU Extension

Permission is hereby granted, free of charge, to any person obtaining a copy of this software and associated documentation files (the "Software"), to deal in the Software without restriction, including without limitation the rights to use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of the Software, and to permit persons to whom the Software is furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.

</editor-fold> */

#include <vsg_webgpu/core/Export.h>
#include <vsg_webgpu/core/WebGPUHeaders.h>
#include <vsg/core/Inherit.h>
#include <vsg/core/Object.h>
#include <vsg/core/ref_ptr.h>
#include <vsg/core/ScratchMemory.h>
#include <vsg/core/Mask.h>
#include <atomic>

namespace vsg_webgpu
{
    // forward declarations
    class Device;
    class CommandPool;
    class State;
    class ViewDependentState;

    /// WebGPU命令缓冲区封装，对应VSG的CommandBuffer类
    /// 提供与VSG CommandBuffer相同的接口，但底层使用WebGPU实现
    class VSG_WEBGPU_DECLSPEC CommandBuffer : public vsg::Inherit<vsg::Object, CommandBuffer>
    {
    public:
        // 构造函数 - 由CommandPool创建
        CommandBuffer(CommandPool* commandPool, WGPUCommandEncoder encoder);

        // WebGPU对象访问
        WGPUCommandEncoder getEncoder() const { return _encoder; }
        WGPUCommandBuffer finish();

        // 对应VSG的CommandBuffer接口
        const uint32_t deviceID;
        uint32_t viewID = 0;
        vsg::Mask traversalMask = vsg::MASK_ALL;
        vsg::Mask overrideMask = vsg::MASK_OFF;
        ViewDependentState* viewDependentState = nullptr;
        State* state = nullptr;

        std::atomic_uint& numDependentSubmissions() { return _numDependentSubmissions; }

        // 设备和命令池访问
        Device* getDevice() { return _device; }
        const Device* getDevice() const { return _device; }
        CommandPool* getCommandPool() { return _commandPool; }
        const CommandPool* getCommandPool() const { return _commandPool; }

        // 命令录制状态
        bool isRecording() const { return _isRecording; }
        void begin();
        void end();
        void reset();

        // 渲染通道管理
        WGPURenderPassEncoder beginRenderPass(const WGPURenderPassDescriptor& descriptor);
        void endRenderPass();

        // 计算通道管理
        WGPUComputePassEncoder beginComputePass(const WGPUComputePassDescriptor& descriptor = {});
        void endComputePass();

        // 资源操作
        void copyBufferToBuffer(WGPUBuffer source, uint64_t sourceOffset,
                               WGPUBuffer destination, uint64_t destinationOffset,
                               uint64_t size);
        
        void copyBufferToTexture(const WGPUImageCopyBuffer& source,
                                const WGPUImageCopyTexture& destination,
                                const WGPUExtent3D& copySize);

        void copyTextureToBuffer(const WGPUImageCopyTexture& source,
                                const WGPUImageCopyBuffer& destination,
                                const WGPUExtent3D& copySize);

        void copyTextureToTexture(const WGPUImageCopyTexture& source,
                                 const WGPUImageCopyTexture& destination,
                                 const WGPUExtent3D& copySize);

        // 调试标签
        void pushDebugGroup(const char* groupLabel);
        void popDebugGroup();
        void insertDebugMarker(const char* markerLabel);

        // 时间戳查询
        void writeTimestamp(WGPUQuerySet querySet, uint32_t queryIndex);

        // 内存管理
        vsg::ref_ptr<vsg::ScratchMemory> scratchMemory;

        // 当前渲染/计算通道访问
        WGPURenderPassEncoder getCurrentRenderPass() const { return _currentRenderPass; }
        WGPUComputePassEncoder getCurrentComputePass() const { return _currentComputePass; }
        bool inRenderPass() const { return _inRenderPass; }
        bool inComputePass() const { return _inComputePass; }

    protected:
        virtual ~CommandBuffer();

    private:
        friend CommandPool;

        Device* _device;
        CommandPool* _commandPool;
        WGPUCommandEncoder _encoder;
        WGPUCommandBuffer _commandBuffer; // 完成后的命令缓冲区
        
        bool _isRecording = false;
        bool _isFinished = false;
        
        std::atomic_uint _numDependentSubmissions{0};

        // 当前渲染/计算通道
        WGPURenderPassEncoder _currentRenderPass;
        WGPUComputePassEncoder _currentComputePass;
        bool _inRenderPass = false;
        bool _inComputePass = false;
    };

    VSG_type_name(vsg_webgpu::CommandBuffer);

} // namespace vsg_webgpu
