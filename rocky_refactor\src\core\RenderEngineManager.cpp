/* <editor-fold desc="MIT License">

Copyright(c) 2024 Rocky Render Engine Refactor

Permission is hereby granted, free of charge, to any person obtaining a copy of this software and associated documentation files (the "Software"), to deal in the Software without restriction, including without limitation the rights to use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of the Software, and to permit persons to whom the Software is furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.

</editor-fold> */

#include <rocky/render/RenderEngineManager.h>
#include <rocky/render/Config.h>
#include "RenderEngineFactory.h"
#include <iostream>
#include <chrono>

namespace rocky {
namespace render {

RenderEngineManager& RenderEngineManager::instance() {
    static RenderEngineManager instance;
    return instance;
}

RenderEngineManager::~RenderEngineManager() {
    shutdown();
}

bool RenderEngineManager::setEngine(std::unique_ptr<IRenderEngine> engine, bool autoInitialize) {
    std::lock_guard<std::mutex> lock(mutex_);
    
    if (!engine) {
        _handleError("Cannot set null render engine");
        return false;
    }
    
    // 关闭当前引擎
    if (engine_) {
        RenderBackend oldBackend = currentBackend_;
        engine_->shutdown();
        engine_.reset();
        initialized_ = false;
        
        // 通知引擎切换
        RenderBackend newBackend = engine->getBackendType();
        currentBackend_ = newBackend;
        _notifyEngineChange(oldBackend, newBackend);
    } else {
        currentBackend_ = engine->getBackendType();
    }
    
    // 设置新引擎
    engine_ = std::move(engine);
    
    // 自动初始化
    if (autoInitialize) {
        return initialize();
    }
    
    ROCKY_LOG_INFO("Render engine set to " << getRenderBackendName(currentBackend_));
    return true;
}

IRenderEngine* RenderEngineManager::getEngine() const {
    std::lock_guard<std::mutex> lock(mutex_);
    return engine_.get();
}

bool RenderEngineManager::hasEngine() const {
    std::lock_guard<std::mutex> lock(mutex_);
    return engine_ != nullptr;
}

RenderBackend RenderEngineManager::getCurrentBackend() const {
    std::lock_guard<std::mutex> lock(mutex_);
    return currentBackend_;
}

bool RenderEngineManager::switchBackend(RenderBackend backend, const void* config) {
    std::lock_guard<std::mutex> lock(mutex_);
    
    if (backend == currentBackend_ && engine_) {
        ROCKY_LOG_INFO("Already using " << getRenderBackendName(backend) << " backend");
        return true;
    }
    
    // 创建新引擎
    auto newEngine = RenderEngineFactory::create(backend, config);
    if (!newEngine) {
        _handleError("Failed to create " + getRenderBackendName(backend) + " render engine");
        return false;
    }
    
    // 关闭当前引擎
    RenderBackend oldBackend = currentBackend_;
    if (engine_) {
        engine_->shutdown();
        initialized_ = false;
    }
    
    // 设置新引擎
    engine_ = std::move(newEngine);
    currentBackend_ = backend;
    
    // 初始化新引擎
    if (!engine_->initialize()) {
        _handleError("Failed to initialize " + getRenderBackendName(backend) + " render engine");
        engine_.reset();
        currentBackend_ = RenderBackend::Mock;
        return false;
    }
    
    initialized_ = true;
    
    // 通知引擎切换
    _notifyEngineChange(oldBackend, backend);
    
    ROCKY_LOG_INFO("Switched to " << getRenderBackendName(backend) << " backend");
    return true;
}

bool RenderEngineManager::initialize() {
    std::lock_guard<std::mutex> lock(mutex_);
    
    if (!engine_) {
        _handleError("No render engine set");
        return false;
    }
    
    if (initialized_) {
        ROCKY_LOG_DEBUG("Render engine already initialized");
        return true;
    }
    
    ROCKY_LOG_INFO("Initializing " << getRenderBackendName(currentBackend_) << " render engine...");
    
    auto startTime = std::chrono::high_resolution_clock::now();
    
    if (!engine_->initialize()) {
        _handleError("Failed to initialize render engine");
        return false;
    }
    
    auto endTime = std::chrono::high_resolution_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(endTime - startTime);
    
    initialized_ = true;
    
    ROCKY_LOG_INFO("Render engine initialized successfully in " << duration.count() << "ms");
    
    // 重置统计信息
    resetRenderStatistics();
    
    return true;
}

void RenderEngineManager::shutdown() {
    std::lock_guard<std::mutex> lock(mutex_);
    
    if (!engine_) {
        return;
    }
    
    ROCKY_LOG_INFO("Shutting down " << getRenderBackendName(currentBackend_) << " render engine...");
    
    engine_->shutdown();
    engine_.reset();
    initialized_ = false;
    currentBackend_ = RenderBackend::Mock;
    
    ROCKY_LOG_INFO("Render engine shutdown completed");
}

bool RenderEngineManager::isInitialized() const {
    std::lock_guard<std::mutex> lock(mutex_);
    return initialized_ && engine_ && engine_->isInitialized();
}

std::shared_ptr<IWindow> RenderEngineManager::createWindow(const WindowConfig& config) {
    std::lock_guard<std::mutex> lock(mutex_);
    
    if (!engine_) {
        _handleError("No render engine available");
        return nullptr;
    }
    
    if (!initialized_ && !const_cast<RenderEngineManager*>(this)->initialize()) {
        _handleError("Failed to initialize render engine");
        return nullptr;
    }
    
    return engine_->createWindow(config);
}

std::shared_ptr<ISceneNode> RenderEngineManager::createSceneRoot() {
    std::lock_guard<std::mutex> lock(mutex_);
    
    if (!engine_) {
        _handleError("No render engine available");
        return nullptr;
    }
    
    if (!initialized_ && !const_cast<RenderEngineManager*>(this)->initialize()) {
        _handleError("Failed to initialize render engine");
        return nullptr;
    }
    
    return engine_->createGroup();
}

std::shared_ptr<IRenderGraph> RenderEngineManager::createRenderGraph(std::shared_ptr<IWindow> window) {
    std::lock_guard<std::mutex> lock(mutex_);
    
    if (!engine_) {
        _handleError("No render engine available");
        return nullptr;
    }
    
    if (!initialized_ && !const_cast<RenderEngineManager*>(this)->initialize()) {
        _handleError("Failed to initialize render engine");
        return nullptr;
    }
    
    return engine_->createRenderGraph(window);
}

bool RenderEngineManager::render(std::shared_ptr<IRenderGraph> renderGraph) {
    std::lock_guard<std::mutex> lock(mutex_);
    
    if (!engine_) {
        _handleError("No render engine available");
        return false;
    }
    
    if (!initialized_) {
        _handleError("Render engine not initialized");
        return false;
    }
    
    auto startTime = std::chrono::high_resolution_clock::now();
    
    bool result = engine_->render(renderGraph);
    
    auto endTime = std::chrono::high_resolution_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::microseconds>(endTime - startTime);
    
    // 更新统计信息
    statistics_.frameCount++;
    statistics_.frameTime = duration.count() / 1000.0; // 转换为毫秒
    
    return result;
}

void RenderEngineManager::setEngineChangeCallback(EngineChangeCallback callback) {
    std::lock_guard<std::mutex> lock(mutex_);
    engineChangeCallback_ = callback;
}

void RenderEngineManager::setErrorCallback(ErrorCallback callback) {
    std::lock_guard<std::mutex> lock(mutex_);
    errorCallback_ = callback;
}

RenderStatistics RenderEngineManager::getRenderStatistics() const {
    std::lock_guard<std::mutex> lock(mutex_);
    
    if (engine_ && initialized_) {
        // 合并引擎统计信息
        auto engineStats = engine_->getRenderStatistics();
        statistics_.drawCalls = engineStats.drawCalls;
        statistics_.triangles = engineStats.triangles;
        statistics_.vertices = engineStats.vertices;
        statistics_.cpuTime = engineStats.cpuTime;
        statistics_.gpuTime = engineStats.gpuTime;
        statistics_.memoryUsed = engineStats.memoryUsed;
    }
    
    return statistics_;
}

void RenderEngineManager::resetRenderStatistics() {
    std::lock_guard<std::mutex> lock(mutex_);
    
    statistics_ = {};
    
    if (engine_ && initialized_) {
        engine_->resetRenderStatistics();
    }
}

RenderEngineInfo RenderEngineManager::getEngineInfo() const {
    std::lock_guard<std::mutex> lock(mutex_);
    
    if (!engine_) {
        return {"No Engine", "0.0.0", "Unknown", RenderBackend::Mock, {}};
    }
    
    return engine_->getEngineInfo();
}

DeviceCapabilities RenderEngineManager::getDeviceCapabilities() const {
    std::lock_guard<std::mutex> lock(mutex_);
    
    if (!engine_) {
        return {};
    }
    
    return engine_->getDeviceCapabilities();
}

void RenderEngineManager::beginProfileMarker(const std::string& name) {
    std::lock_guard<std::mutex> lock(mutex_);
    
    if (engine_ && initialized_) {
        engine_->beginDebugMarker(name);
    }
}

void RenderEngineManager::endProfileMarker() {
    std::lock_guard<std::mutex> lock(mutex_);
    
    if (engine_ && initialized_) {
        engine_->endDebugMarker();
    }
}

void RenderEngineManager::waitIdle() {
    std::lock_guard<std::mutex> lock(mutex_);
    
    if (engine_ && initialized_) {
        engine_->waitIdle();
    }
}

void RenderEngineManager::executeCustomCommand(std::function<void(IRenderEngine*)> command) {
    std::lock_guard<std::mutex> lock(mutex_);
    
    if (engine_ && initialized_ && command) {
        command(engine_.get());
    }
}

void* RenderEngineManager::getNativeEngineHandle() const {
    std::lock_guard<std::mutex> lock(mutex_);
    
    if (!engine_) {
        return nullptr;
    }
    
    return engine_->getNativeHandle();
}

void RenderEngineManager::_notifyEngineChange(RenderBackend oldBackend, RenderBackend newBackend) {
    if (engineChangeCallback_) {
        engineChangeCallback_(oldBackend, newBackend);
    }
}

void RenderEngineManager::_handleError(const std::string& error) {
    ROCKY_LOG_ERROR(error);
    
    if (errorCallback_) {
        errorCallback_(error);
    }
}

} // namespace render
} // namespace rocky
