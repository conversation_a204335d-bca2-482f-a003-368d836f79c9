// src/main.cpp
#include <rocky_webgpu/Device.h>
#include <rocky_webgpu/RenderGraph.h>
#include <vsg/app/View.h>
#include <vsg/app/Viewer.h>
#include <vsg/nodes/Group.h>

int main()
{
    // 创建WebGPU设备
    auto device = vsg::ref_ptr<rocky_webgpu::Device>(new rocky_webgpu::Device());
    wgpu::Surface surface = emscripten::CreateWebGPUSurface();
    if (!device->initialize(surface))
    {
        std::cerr << "Failed to initialize WebGPU device" << std::endl;
        return -1;
    }

    // 创建渲染图
    auto renderGraph = vsg::ref_ptr<rocky_webgpu::RenderGraph>(new rocky_webgpu::RenderGraph(device));

    // 创建视图
    auto view = vsg::ref_ptr<vsg::View>(new vsg::View());
    auto scene = vsg::ref_ptr<vsg::Group>(new vsg::Group());
    view->scene = scene;

    // 添加视图到渲染图
    renderGraph->addView(view);

    // 渲染循环
    while (true)
    {
        renderGraph->render();
    }

    return 0;
}
