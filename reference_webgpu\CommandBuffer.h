// include/vsg_webgpu/CommandBuffer.h
#pragma once
#include <vsg/core/Object.h>
#include <vsg/core/ref_ptr.h>
#include <vsg/core/ScratchMemory.h>
#include <vsg/core/Mask.h>
#include <webgpu/webgpu_cpp.h>
#include <atomic>

namespace vsg_webgpu
{
    // forward declarations
    class Device;
    class CommandPool;
    class State;
    class ViewDependentState;

    /// WebGPU命令缓冲区封装，对应VSG的CommandBuffer类
    /// 提供与VSG CommandBuffer相同的接口，但底层使用WebGPU实现
    class VSG_DECLSPEC CommandBuffer : public vsg::Inherit<vsg::Object, CommandBuffer>
    {
    public:
        // 构造函数 - 由CommandPool创建
        CommandBuffer(CommandPool* commandPool, wgpu::CommandEncoder encoder);

        // WebGPU对象访问
        wgpu::CommandEncoder getEncoder() const { return _encoder; }
        wgpu::CommandBuffer finish();

        // 对应VSG的CommandBuffer接口
        const uint32_t deviceID;
        uint32_t viewID = 0;
        vsg::Mask traversalMask = vsg::MASK_ALL;
        vsg::Mask overrideMask = vsg::MASK_OFF;
        ViewDependentState* viewDependentState = nullptr;
        State* state = nullptr;

        std::atomic_uint& numDependentSubmissions() { return _numDependentSubmissions; }

        // 设备和命令池访问
        Device* getDevice() { return _device; }
        const Device* getDevice() const { return _device; }
        CommandPool* getCommandPool() { return _commandPool; }
        const CommandPool* getCommandPool() const { return _commandPool; }

        // 命令录制状态
        bool isRecording() const { return _isRecording; }
        void begin();
        void end();
        void reset();

        // 渲染通道管理
        wgpu::RenderPassEncoder beginRenderPass(const wgpu::RenderPassDescriptor& descriptor);
        void endRenderPass();

        // 计算通道管理
        wgpu::ComputePassEncoder beginComputePass(const wgpu::ComputePassDescriptor& descriptor = {});
        void endComputePass();

        // 资源操作
        void copyBufferToBuffer(wgpu::Buffer source, uint64_t sourceOffset,
                               wgpu::Buffer destination, uint64_t destinationOffset,
                               uint64_t size);
        
        void copyBufferToTexture(const wgpu::ImageCopyBuffer& source,
                                const wgpu::ImageCopyTexture& destination,
                                const wgpu::Extent3D& copySize);

        void copyTextureToBuffer(const wgpu::ImageCopyTexture& source,
                                const wgpu::ImageCopyBuffer& destination,
                                const wgpu::Extent3D& copySize);

        void copyTextureToTexture(const wgpu::ImageCopyTexture& source,
                                 const wgpu::ImageCopyTexture& destination,
                                 const wgpu::Extent3D& copySize);

        // 调试标签
        void pushDebugGroup(const char* groupLabel);
        void popDebugGroup();
        void insertDebugMarker(const char* markerLabel);

        // 时间戳查询
        void writeTimestamp(wgpu::QuerySet querySet, uint32_t queryIndex);

        // 内存管理
        vsg::ref_ptr<vsg::ScratchMemory> scratchMemory;

    protected:
        virtual ~CommandBuffer();

    private:
        friend CommandPool;

        Device* _device;
        CommandPool* _commandPool;
        wgpu::CommandEncoder _encoder;
        wgpu::CommandBuffer _commandBuffer; // 完成后的命令缓冲区
        
        bool _isRecording = false;
        bool _isFinished = false;
        
        std::atomic_uint _numDependentSubmissions{0};

        // 当前渲染/计算通道
        wgpu::RenderPassEncoder _currentRenderPass;
        wgpu::ComputePassEncoder _currentComputePass;
        bool _inRenderPass = false;
        bool _inComputePass = false;
    };

    VSG_type_name(vsg_webgpu::CommandBuffer);

} // namespace vsg_webgpu
