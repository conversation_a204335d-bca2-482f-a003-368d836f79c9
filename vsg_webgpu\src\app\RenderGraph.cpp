/* <editor-fold desc="MIT License">

Copyright(c) 2024 VSG WebGPU Extension

Permission is hereby granted, free of charge, to any person obtaining a copy of this software and associated documentation files (the "Software"), to deal in the Software without restriction, including without limitation the rights to use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of the Software, and to permit persons to whom the Software is furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.

</editor-fold> */

#include <vsg_webgpu/app/RenderGraph.h>
#include <vsg_webgpu/app/Window.h>
#include <vsg_webgpu/vk/Device.h>
#include <vsg_webgpu/vk/CommandBuffer.h>
#include <vsg_webgpu/app/RecordTraversal.h>

using namespace vsg_webgpu;

RenderGraph::RenderGraph(Device* device, Window* window) :
    device(device), window(window)
{
    VSG_WEBGPU_LOG_DEBUG("RenderGraph::RenderGraph()");
}

RenderGraph::~RenderGraph()
{
    VSG_WEBGPU_LOG_DEBUG("RenderGraph::~RenderGraph()");
    releaseFramebufferResources();
}

void RenderGraph::addRenderPass(const RenderPass& renderPass)
{
    renderPasses.push_back(renderPass);
    _statistics.numRenderPasses = renderPasses.size();
}

void RenderGraph::addRenderPass(const std::string& name, 
                                vsg::ref_ptr<vsg::Group> contents,
                                const std::vector<WGPUTextureView>& colorTargets,
                                WGPUTextureView depthTarget)
{
    RenderPass renderPass;
    renderPass.name = name;
    renderPass.contents = contents;
    renderPass.colorAttachments = colorTargets;
    renderPass.depthStencilAttachment = depthTarget;
    
    // 设置默认清除值
    renderPass.clearColors.resize(colorTargets.size());
    for (auto& clearColor : renderPass.clearColors)
    {
        clearColor = {0.0, 0.0, 0.0, 1.0}; // 黑色背景
    }
    renderPass.clearDepth = 1.0f;
    renderPass.clearStencil = 0;
    
    addRenderPass(renderPass);
}

void RenderGraph::record(vsg::ref_ptr<RecordTraversal> recordTraversal, 
                        vsg::ref_ptr<CommandBuffer> commandBuffer)
{
    if (!recordTraversal || !commandBuffer) return;
    
    recordTraversal->setCommandBuffer(commandBuffer);
    
    // 录制所有渲染通道
    for (auto& renderPass : renderPasses)
    {
        _recordRenderPass(renderPass, recordTraversal, commandBuffer);
    }
}

void RenderGraph::render(vsg::ref_ptr<RecordTraversal> recordTraversal)
{
    if (!device || !recordTraversal) return;
    
    auto startTime = std::chrono::high_resolution_clock::now();
    
    // 获取命令池和命令缓冲区
    auto commandPool = device->getCommandPool();
    if (!commandPool) return;
    
    auto commandBuffer = commandPool->allocate();
    if (!commandBuffer) return;
    
    // 开始录制
    commandBuffer->begin();
    
    // 录制渲染命令
    record(recordTraversal, commandBuffer);
    
    // 结束录制
    commandBuffer->end();
    
    // 提交命令
    auto queue = device->getQueue();
    if (queue)
    {
        queue->submit(commandBuffer);
    }
    
    // 释放命令缓冲区
    commandPool->free(commandBuffer.get());
    
    auto endTime = std::chrono::high_resolution_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::microseconds>(endTime - startTime);
    _statistics.renderTime = duration.count() / 1000.0; // 转换为毫秒
    
    // 更新统计信息
    _updateStatistics(recordTraversal->getStatistics());
}

void RenderGraph::compile()
{
    // 编译渲染图
    if (!device) return;
    
    // 设置渲染通道描述符
    for (auto& renderPass : renderPasses)
    {
        _setupRenderPassDescriptor(renderPass);
    }
    
    VSG_WEBGPU_LOG_DEBUG("RenderGraph::compile() - compiled ", renderPasses.size(), " render passes");
}

void RenderGraph::createFramebufferResources(uint32_t width, uint32_t height)
{
    if (!device) return;
    
    releaseFramebufferResources();
    
    _framebufferResources.width = width;
    _framebufferResources.height = height;
    
#if !VSG_WEBGPU_USE_MOCK
    // 创建颜色纹理
    WGPUTextureDescriptor colorTextureDesc = {};
    colorTextureDesc.label = "RenderGraph Color Texture";
    colorTextureDesc.size = {width, height, 1};
    colorTextureDesc.mipLevelCount = 1;
    colorTextureDesc.sampleCount = 1;
    colorTextureDesc.dimension = WGPUTextureDimension_2D;
    colorTextureDesc.format = WGPUTextureFormat_BGRA8Unorm;
    colorTextureDesc.usage = WGPUTextureUsage_RenderAttachment | WGPUTextureUsage_TextureBinding;
    
    _framebufferResources.colorTexture = device->getDevice().CreateTexture(&colorTextureDesc);
    
    // 创建颜色纹理视图
    WGPUTextureViewDescriptor colorViewDesc = {};
    colorViewDesc.label = "RenderGraph Color Texture View";
    colorViewDesc.format = WGPUTextureFormat_BGRA8Unorm;
    colorViewDesc.dimension = WGPUTextureViewDimension_2D;
    colorViewDesc.baseMipLevel = 0;
    colorViewDesc.mipLevelCount = 1;
    colorViewDesc.baseArrayLayer = 0;
    colorViewDesc.arrayLayerCount = 1;
    
    _framebufferResources.colorTextureView = _framebufferResources.colorTexture.CreateView(&colorViewDesc);
    
    // 创建深度纹理
    device->createDepthBuffer(width, height);
    _framebufferResources.depthTexture = {}; // 由Device管理
    _framebufferResources.depthTextureView = device->getDepthTextureView();
#endif
    
    _framebufferResourcesValid = true;
    
    VSG_WEBGPU_LOG_DEBUG("RenderGraph::createFramebufferResources() - ", width, "x", height);
}

void RenderGraph::resizeFramebufferResources(uint32_t width, uint32_t height)
{
    createFramebufferResources(width, height);
}

void RenderGraph::releaseFramebufferResources()
{
#if !VSG_WEBGPU_USE_MOCK
    _framebufferResources.colorTextureView = {};
    _framebufferResources.colorTexture = {};
    _framebufferResources.depthTextureView = {};
    _framebufferResources.depthTexture = {};
#endif
    
    _framebufferResources.width = 0;
    _framebufferResources.height = 0;
    _framebufferResourcesValid = false;
}

void RenderGraph::_setupRenderPassDescriptor(RenderPass& renderPass)
{
    // 设置渲染通道描述符
    renderPass.descriptor = {};
    renderPass.descriptor.label = renderPass.name.c_str();
    
    // 如果没有指定颜色附件，使用默认的
    if (renderPass.colorAttachments.empty() && window)
    {
        auto currentView = window->getCurrentTextureView();
        if (currentView)
        {
            renderPass.colorAttachments.push_back(currentView);
        }
    }
    
    // 如果没有指定深度附件，使用默认的
    if (!renderPass.depthStencilAttachment && device)
    {
        renderPass.depthStencilAttachment = device->getDepthTextureView();
    }
    
    // 设置颜色附件
    static std::vector<WGPURenderPassColorAttachment> colorAttachments;
    colorAttachments.clear();
    colorAttachments.resize(renderPass.colorAttachments.size());
    
    for (size_t i = 0; i < renderPass.colorAttachments.size(); ++i)
    {
        colorAttachments[i].view = renderPass.colorAttachments[i];
        colorAttachments[i].loadOp = WGPULoadOp_Clear;
        colorAttachments[i].storeOp = WGPUStoreOp_Store;
        if (i < renderPass.clearColors.size())
        {
            colorAttachments[i].clearValue = renderPass.clearColors[i];
        }
        else
        {
            colorAttachments[i].clearValue = {0.0, 0.0, 0.0, 1.0};
        }
    }
    
    renderPass.descriptor.colorAttachmentCount = colorAttachments.size();
    renderPass.descriptor.colorAttachments = colorAttachments.data();
    
    // 设置深度模板附件
    static WGPURenderPassDepthStencilAttachment depthStencilAttachment = {};
    if (renderPass.depthStencilAttachment)
    {
        depthStencilAttachment.view = renderPass.depthStencilAttachment;
        depthStencilAttachment.depthLoadOp = WGPULoadOp_Clear;
        depthStencilAttachment.depthStoreOp = WGPUStoreOp_Store;
        depthStencilAttachment.depthClearValue = renderPass.clearDepth;
        depthStencilAttachment.stencilLoadOp = WGPULoadOp_Clear;
        depthStencilAttachment.stencilStoreOp = WGPUStoreOp_Store;
        depthStencilAttachment.stencilClearValue = renderPass.clearStencil;
        
        renderPass.descriptor.depthStencilAttachment = &depthStencilAttachment;
    }
}

void RenderGraph::_recordRenderPass(const RenderPass& renderPass, 
                                   vsg::ref_ptr<RecordTraversal> recordTraversal,
                                   vsg::ref_ptr<CommandBuffer> commandBuffer)
{
    if (!renderPass.contents) return;
    
    // 开始渲染通道
    recordTraversal->beginRenderPass(renderPass.descriptor);
    
    // 遍历渲染通道内容
    renderPass.contents->traverse(*recordTraversal);
    
    // 结束渲染通道
    recordTraversal->endRenderPass();
}

void RenderGraph::_updateStatistics(const RecordTraversal::Statistics& traversalStats)
{
    _statistics.numDrawCalls = traversalStats.numDrawCalls;
    _statistics.totalTriangles = traversalStats.numTriangles;
}
