/* <editor-fold desc="MIT License">

Copyright(c) 2024 VSG WebGPU Extension

Permission is hereby granted, free of charge, to any person obtaining a copy of this software and associated documentation files (the "Software"), to deal in the Software without restriction, including without limitation the rights to use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of the Software, and to permit persons to whom the Software is furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.

</editor-fold> */

#include <vsg_webgpu/all.h>
#include <vsg/all.h>
#include <iostream>

using namespace vsg_webgpu;

class StateTest
{
public:
    bool runTests()
    {
        std::cout << "=== VSG WebGPU State Management Test ===" << std::endl;
        
        bool allPassed = true;
        
        allPassed &= testStateCreation();
        allPassed &= testMatrixStack();
        allPassed &= testStateStack();
        allPassed &= testBoundResources();
        
        std::cout << "\n=== Test Results ===" << std::endl;
        std::cout << "Overall: " << (allPassed ? "PASSED" : "FAILED") << std::endl;
        
        return allPassed;
    }

private:
    bool testStateCreation()
    {
        std::cout << "\n--- Test: State Creation ---" << std::endl;
        
        try
        {
            initializeWebGPU();
            
            auto device = Device::create();
            if (!device || !device->initialize())
            {
#if VSG_WEBGPU_USE_MOCK
                std::cout << "Mock mode: Creating state with mock device" << std::endl;
#else
                std::cout << "FAILED: Could not initialize device" << std::endl;
                return false;
#endif
            }
            
            // 创建状态对象
            vsg::Slots maxSlots{16, 16, 16, 16};
            auto state = State::create(device.get(), maxSlots);
            
            if (!state)
            {
                std::cout << "FAILED: Could not create state" << std::endl;
                return false;
            }
            
            std::cout << "PASSED: State created successfully" << std::endl;
            std::cout << "Max slots: " << maxSlots.max() << std::endl;
            
            return true;
        }
        catch (const std::exception& e)
        {
            std::cout << "FAILED: Exception - " << e.what() << std::endl;
            return false;
        }
    }
    
    bool testMatrixStack()
    {
        std::cout << "\n--- Test: Matrix Stack ---" << std::endl;
        
        try
        {
            auto device = Device::create();
            if (!device) device->initialize();
            
            auto state = State::create(device.get());
            
            // 测试投影矩阵栈
            vsg::mat4 projMatrix = vsg::perspective(45.0, 1.0, 0.1, 100.0);
            state->pushProjectionMatrix(projMatrix);
            
            auto retrievedProj = state->getProjectionMatrix();
            if (retrievedProj != projMatrix)
            {
                std::cout << "FAILED: Projection matrix mismatch" << std::endl;
                return false;
            }
            
            // 测试视图矩阵栈
            vsg::mat4 viewMatrix = vsg::lookAt(vsg::vec3(0, 0, 5), vsg::vec3(0, 0, 0), vsg::vec3(0, 1, 0));
            state->pushViewMatrix(viewMatrix);
            
            auto retrievedView = state->getViewMatrix();
            if (retrievedView != viewMatrix)
            {
                std::cout << "FAILED: View matrix mismatch" << std::endl;
                return false;
            }
            
            // 测试模型矩阵栈
            vsg::mat4 modelMatrix = vsg::translate(1.0, 2.0, 3.0);
            state->pushModelMatrix(modelMatrix);
            
            auto retrievedModel = state->getModelMatrix();
            if (retrievedModel != modelMatrix)
            {
                std::cout << "FAILED: Model matrix mismatch" << std::endl;
                return false;
            }
            
            // 测试MVP矩阵计算
            auto mvpMatrix = state->getMVPMatrix();
            auto expectedMVP = projMatrix * viewMatrix * modelMatrix;
            
            // 由于浮点精度问题，我们检查是否足够接近
            bool mvpMatches = true;
            for (int i = 0; i < 4; ++i)
            {
                for (int j = 0; j < 4; ++j)
                {
                    if (std::abs(mvpMatrix[i][j] - expectedMVP[i][j]) > 1e-6)
                    {
                        mvpMatches = false;
                        break;
                    }
                }
                if (!mvpMatches) break;
            }
            
            if (!mvpMatches)
            {
                std::cout << "FAILED: MVP matrix calculation incorrect" << std::endl;
                return false;
            }
            
            // 测试矩阵栈弹出
            state->popModelMatrix();
            state->popViewMatrix();
            state->popProjectionMatrix();
            
            std::cout << "PASSED: Matrix stack operations successful" << std::endl;
            return true;
        }
        catch (const std::exception& e)
        {
            std::cout << "FAILED: Exception - " << e.what() << std::endl;
            return false;
        }
    }
    
    bool testStateStack()
    {
        std::cout << "\n--- Test: State Stack ---" << std::endl;
        
        try
        {
            auto device = Device::create();
            if (!device) device->initialize();
            
            vsg::Slots maxSlots{16, 16, 16, 16};
            auto state = State::create(device.get(), maxSlots);
            
            // 创建一些状态命令
            auto stateCommand1 = vsg::StateCommand::create();
            stateCommand1->slot = 0;
            
            auto stateCommand2 = vsg::StateCommand::create();
            stateCommand2->slot = 1;
            
            // 测试状态推入
            std::vector<vsg::ref_ptr<vsg::StateCommand>> commands = {stateCommand1, stateCommand2};
            state->push(commands.begin(), commands.end());
            
            // 验证状态栈
            if (state->stateStacks.size() < 2)
            {
                std::cout << "FAILED: State stacks not properly sized" << std::endl;
                return false;
            }
            
            // 测试状态弹出
            state->pop(commands.begin(), commands.end());
            
            std::cout << "PASSED: State stack operations successful" << std::endl;
            return true;
        }
        catch (const std::exception& e)
        {
            std::cout << "FAILED: Exception - " << e.what() << std::endl;
            return false;
        }
    }
    
    bool testBoundResources()
    {
        std::cout << "\n--- Test: Bound Resources ---" << std::endl;
        
        try
        {
            auto device = Device::create();
            if (!device) device->initialize();
            
            auto state = State::create(device.get());
            
            // 获取绑定资源
            auto& boundResources = state->getBoundResources();
            
            // 测试初始状态
            if (boundResources.indexFormat != WGPUIndexFormat_Uint16)
            {
                std::cout << "FAILED: Default index format incorrect" << std::endl;
                return false;
            }
            
            if (boundResources.indexBufferOffset != 0)
            {
                std::cout << "FAILED: Default index buffer offset incorrect" << std::endl;
                return false;
            }
            
            // 测试设置绑定资源
            boundResources.indexFormat = WGPUIndexFormat_Uint32;
            boundResources.indexBufferOffset = 128;
            
            if (boundResources.indexFormat != WGPUIndexFormat_Uint32)
            {
                std::cout << "FAILED: Index format not updated" << std::endl;
                return false;
            }
            
            if (boundResources.indexBufferOffset != 128)
            {
                std::cout << "FAILED: Index buffer offset not updated" << std::endl;
                return false;
            }
            
            std::cout << "PASSED: Bound resources management successful" << std::endl;
            return true;
        }
        catch (const std::exception& e)
        {
            std::cout << "FAILED: Exception - " << e.what() << std::endl;
            return false;
        }
    }
};

int main()
{
    try
    {
        StateTest test;
        bool success = test.runTests();
        
        shutdownWebGPU();
        
        return success ? 0 : 1;
    }
    catch (const std::exception& e)
    {
        std::cerr << "Fatal error: " << e.what() << std::endl;
        return 1;
    }
}
