/* <editor-fold desc="MIT License">

Copyright(c) 2024 VSG WebGPU Extension

Permission is hereby granted, free of charge, to any person obtaining a copy of this software and associated documentation files (the "Software"), to deal in the Software without restriction, including without limitation the rights to use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of the Software, and to permit persons to whom the Software is furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.

</editor-fold> */

#include <vsg_webgpu/all.h>
#include <vsg/all.h>
#include <iostream>
#include <chrono>

using namespace vsg_webgpu;

int main()
{
    try
    {
        std::cout << "VSG WebGPU Triangle Example" << std::endl;
        
        // 初始化WebGPU
        initializeWebGPU();
        
        // 创建设备
        auto device = Device::create();
        if (!device)
        {
            std::cerr << "Failed to create WebGPU device" << std::endl;
            return 1;
        }
        
        // 创建窗口
        auto window = Window::create("VSG WebGPU Triangle", 800, 600);
        if (!window)
        {
            std::cerr << "Failed to create window" << std::endl;
            return 1;
        }
        
        window->device = device;
        
        // 在非mock模式下创建实际窗口
#if !VSG_WEBGPU_USE_MOCK
        if (!window->create())
        {
            std::cerr << "Failed to create window surface" << std::endl;
            return 1;
        }
        
        if (!device->initialize(window->surface))
        {
            std::cerr << "Failed to initialize WebGPU device" << std::endl;
            return 1;
        }
#else
        device->initialize();
        std::cout << "Running in mock mode - no actual rendering" << std::endl;
#endif
        
        // 创建场景图
        auto sceneGraph = vsg::Group::create();
        
        // 创建三角形顶点数据
        auto vertices = vsg::vec3Array::create({
            {0.0f, 0.5f, 0.0f},   // 顶部
            {-0.5f, -0.5f, 0.0f}, // 左下
            {0.5f, -0.5f, 0.0f}   // 右下
        });
        
        auto colors = vsg::vec3Array::create({
            {1.0f, 0.0f, 0.0f}, // 红色
            {0.0f, 1.0f, 0.0f}, // 绿色
            {0.0f, 0.0f, 1.0f}  // 蓝色
        });
        
        auto indices = vsg::ushortArray::create({0, 1, 2});
        
        // 创建几何体
        auto geometry = vsg::Geometry::create();
        geometry->arrays = {vertices, colors};
        geometry->indices = indices;
        geometry->commands = {vsg::DrawIndexed::create(3, 1, 0, 0, 0)};
        
        // 添加到场景图
        sceneGraph->addChild(geometry);
        
        // 创建渲染图
        auto renderGraph = RenderGraph::create(device.get(), window.get());
        renderGraph->addRenderPass("main", sceneGraph);
        
        // 创建记录遍历器
        auto recordTraversal = RecordTraversal::create();
        
        std::cout << "Starting render loop..." << std::endl;
        
        // 渲染循环
        auto startTime = std::chrono::high_resolution_clock::now();
        int frameCount = 0;
        
#if VSG_WEBGPU_USE_MOCK
        // Mock模式：模拟渲染
        const int maxFrames = 60;
        while (frameCount < maxFrames)
        {
            // 模拟渲染
            renderGraph->render(recordTraversal);
            
            frameCount++;
            
            if (frameCount % 10 == 0)
            {
                auto stats = recordTraversal->getStatistics();
                std::cout << "Frame " << frameCount << " - Mock rendering" << std::endl;
            }
            
            // 模拟帧时间
            std::this_thread::sleep_for(std::chrono::milliseconds(16));
        }
#else
        // 实际渲染循环
        while (window->valid() && frameCount < 300) // 限制帧数以便测试
        {
            // 处理窗口事件
            if (!window->pollEvents())
            {
                break;
            }
            
            // 渲染帧
            renderGraph->render(recordTraversal);
            
            // 呈现
            window->present();
            
            frameCount++;
            
            // 每30帧输出一次统计信息
            if (frameCount % 30 == 0)
            {
                auto stats = recordTraversal->getStatistics();
                auto currentTime = std::chrono::high_resolution_clock::now();
                auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(currentTime - startTime);
                double fps = frameCount * 1000.0 / duration.count();
                
                std::cout << "Frame " << frameCount 
                         << " - FPS: " << fps
                         << " - Draw calls: " << stats.numDrawCalls 
                         << " - Triangles: " << stats.numTriangles << std::endl;
            }
        }
#endif
        
        auto endTime = std::chrono::high_resolution_clock::now();
        auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(endTime - startTime);
        
        std::cout << "\nRendering completed:" << std::endl;
        std::cout << "  Frames rendered: " << frameCount << std::endl;
        std::cout << "  Total time: " << duration.count() << "ms" << std::endl;
        std::cout << "  Average FPS: " << (frameCount * 1000.0 / duration.count()) << std::endl;
        
        // 获取最终统计信息
        auto finalStats = recordTraversal->getStatistics();
        std::cout << "  Total draw calls: " << finalStats.numDrawCalls << std::endl;
        std::cout << "  Total triangles: " << finalStats.numTriangles << std::endl;
        
        // 清理
        recordTraversal.reset();
        renderGraph.reset();
        sceneGraph.reset();
        
        if (window)
        {
            window->destroy();
            window.reset();
        }
        
        device.reset();
        shutdownWebGPU();
        
        std::cout << "Triangle example completed successfully!" << std::endl;
        return 0;
    }
    catch (const std::exception& e)
    {
        std::cerr << "Error: " << e.what() << std::endl;
        return 1;
    }
}
