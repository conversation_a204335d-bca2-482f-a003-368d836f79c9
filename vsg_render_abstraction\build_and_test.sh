#!/bin/bash

# VSG渲染引擎抽象层自动构建测试脚本

set -e  # 遇到错误时退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}========================================"
echo "VSG渲染引擎抽象层自动构建测试"
echo -e "========================================${NC}"
echo

# 默认参数
BUILD_TYPE="Release"
BUILD_DIR="build_new"
CLEAN_BUILD=0
JOBS=$(nproc 2>/dev/null || sysctl -n hw.ncpu 2>/dev/null || echo 4)

# 解析命令行参数
show_help() {
    echo "用法: $0 [选项]"
    echo
    echo "选项:"
    echo "  --debug       构建Debug版本 (默认: Release)"
    echo "  --clean       清理后重新构建"
    echo "  --jobs N      并行构建任务数 (默认: $JOBS)"
    echo "  --help        显示此帮助信息"
    echo
}

while [[ $# -gt 0 ]]; do
    case $1 in
        --debug)
            BUILD_TYPE="Debug"
            shift
            ;;
        --clean)
            CLEAN_BUILD=1
            shift
            ;;
        --jobs)
            JOBS="$2"
            shift 2
            ;;
        --help)
            show_help
            exit 0
            ;;
        *)
            echo -e "${RED}未知选项: $1${NC}"
            show_help
            exit 1
            ;;
    esac
done

echo -e "${BLUE}检查构建环境...${NC}"

# 检查CMake
if ! command -v cmake &> /dev/null; then
    echo -e "${RED}✗ 未找到CMake${NC}"
    exit 1
fi

CMAKE_VERSION=$(cmake --version | head -n1 | cut -d' ' -f3)
echo -e "${GREEN}✓ CMake版本: $CMAKE_VERSION${NC}"

# 检查编译器
if command -v g++ &> /dev/null; then
    GCC_VERSION=$(g++ --version | head -n1)
    echo -e "${GREEN}✓ 编译器: $GCC_VERSION${NC}"
elif command -v clang++ &> /dev/null; then
    CLANG_VERSION=$(clang++ --version | head -n1)
    echo -e "${GREEN}✓ 编译器: $CLANG_VERSION${NC}"
else
    echo -e "${RED}✗ 未找到C++编译器${NC}"
    exit 1
fi

echo
echo -e "${BLUE}构建配置:${NC}"
echo "  构建类型: $BUILD_TYPE"
echo "  构建目录: $BUILD_DIR"
echo "  并行任务: $JOBS"
echo

# 清理构建目录
if [[ $CLEAN_BUILD -eq 1 ]]; then
    echo -e "${YELLOW}清理构建目录...${NC}"
    rm -rf "$BUILD_DIR"
    echo -e "${GREEN}✓ 清理完成${NC}"
    echo
fi

# 创建构建目录
mkdir -p "$BUILD_DIR"
cd "$BUILD_DIR"

echo -e "${BLUE}步骤 1/5: 配置CMake...${NC}"
cmake .. \
    -DCMAKE_BUILD_TYPE="$BUILD_TYPE" \
    -DVSG_ABSTRACTION_BUILD_VSG_BACKEND=OFF \
    -DVSG_ABSTRACTION_BUILD_WEBGPU_BACKEND=OFF \
    -DVSG_ABSTRACTION_BUILD_OPENGL_BACKEND=OFF \
    -DVSG_ABSTRACTION_BUILD_MOCK_BACKEND=ON \
    -DVSG_ABSTRACTION_BUILD_TESTS=ON \
    -DVSG_ABSTRACTION_BUILD_EXAMPLES=ON \
    -DVSG_ABSTRACTION_BUILD_SHARED_LIBS=OFF

echo -e "${GREEN}✓ CMake配置成功${NC}"
echo

echo -e "${BLUE}步骤 2/5: 编译项目...${NC}"
cmake --build . --config "$BUILD_TYPE" --parallel "$JOBS"

echo -e "${GREEN}✓ 编译成功${NC}"
echo

echo -e "${BLUE}步骤 3/5: 运行基础测试...${NC}"
if [[ -x "tests/test_basic" ]]; then
    echo "运行基础功能测试..."
    if ./tests/test_basic; then
        echo -e "${GREEN}✓ 基础测试通过${NC}"
    else
        echo -e "${RED}✗ 基础测试失败${NC}"
        TEST_FAILED=1
    fi
else
    echo -e "${YELLOW}⚠ 基础测试程序未找到${NC}"
fi

echo

echo -e "${BLUE}步骤 4/5: 运行示例程序...${NC}"
if [[ -x "examples/basic_example" ]]; then
    echo "运行基础示例..."
    if ./examples/basic_example; then
        echo -e "${GREEN}✓ 基础示例运行成功${NC}"
    else
        echo -e "${RED}✗ 基础示例运行失败${NC}"
    fi
else
    echo -e "${YELLOW}⚠ 基础示例程序未找到${NC}"
fi

if [[ -x "examples/vsg_rendering_example" ]]; then
    echo "运行VSG渲染示例..."
    if ./examples/vsg_rendering_example; then
        echo -e "${GREEN}✓ VSG渲染示例运行成功${NC}"
    else
        echo -e "${YELLOW}⚠ VSG渲染示例运行失败（可能是VSG不可用）${NC}"
    fi
else
    echo -e "${YELLOW}⚠ VSG渲染示例程序未找到${NC}"
fi

echo

echo -e "${BLUE}步骤 5/5: 运行完整测试套件...${NC}"
if ctest --output-on-failure --build-config "$BUILD_TYPE"; then
    echo -e "${GREEN}✓ 所有测试通过${NC}"
else
    echo -e "${YELLOW}⚠ 部分测试失败${NC}"
fi

# 返回根目录
cd ..

echo
echo -e "${BLUE}========================================"
echo -e "${GREEN}构建和测试完成!${NC}"
echo -e "${BLUE}========================================${NC}"
echo

echo -e "${BLUE}构建输出:${NC}"
echo "  库文件: $BUILD_DIR/lib/"
echo "  可执行文件: $BUILD_DIR/bin/"
echo "  测试程序: $BUILD_DIR/tests/"
echo "  示例程序: $BUILD_DIR/examples/"
echo

echo -e "${BLUE}可用的程序:${NC}"
for program in "$BUILD_DIR/examples"/* "$BUILD_DIR/tests"/*; do
    if [[ -x "$program" && -f "$program" ]]; then
        echo "  $program"
    fi
done

echo
echo -e "${BLUE}快速运行命令:${NC}"
echo "  cd $BUILD_DIR/examples"
echo "  ./basic_example"
echo

# 设置库路径提示
if [[ "$OSTYPE" == "linux-gnu"* ]]; then
    echo -e "${YELLOW}注意: 如果运行时找不到共享库，请设置LD_LIBRARY_PATH:${NC}"
    echo "  export LD_LIBRARY_PATH=\$PWD/$BUILD_DIR/lib:\$LD_LIBRARY_PATH"
elif [[ "$OSTYPE" == "darwin"* ]]; then
    echo -e "${YELLOW}注意: 如果运行时找不到共享库，请设置DYLD_LIBRARY_PATH:${NC}"
    echo "  export DYLD_LIBRARY_PATH=\$PWD/$BUILD_DIR/lib:\$DYLD_LIBRARY_PATH"
fi

echo
echo -e "${GREEN}🎉 VSG渲染引擎抽象层构建测试成功完成！${NC}"
