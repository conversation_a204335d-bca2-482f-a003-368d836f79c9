# VSG渲染引擎抽象层示例程序构建配置

# ========== 基础示例 ==========

# 基础抽象层示例
add_executable(basic_example basic_vsg_abstraction.cpp)
target_link_libraries(basic_example VSGAbstraction)
target_include_directories(basic_example PRIVATE 
    ${CMAKE_CURRENT_SOURCE_DIR}/../include
    ${CMAKE_CURRENT_BINARY_DIR}/../include
)

# ========== VSG特定示例 ==========

if(VSG_ABSTRACTION_BUILD_VSG_BACKEND AND VSG_ABSTRACTION_HAS_VSG)
    # VSG兼容性示例
    add_executable(vsg_compatibility_example vsg_compatibility.cpp)
    target_link_libraries(vsg_compatibility_example VSGAbstraction)
    target_include_directories(vsg_compatibility_example PRIVATE 
        ${CMAKE_CURRENT_SOURCE_DIR}/../include
        ${CMAKE_CURRENT_BINARY_DIR}/../include
    )
    
    # VSG渲染示例
    add_executable(vsg_rendering_example vsg_rendering.cpp)
    target_link_libraries(vsg_rendering_example VSGAbstraction)
    target_include_directories(vsg_rendering_example PRIVATE 
        ${CMAKE_CURRENT_SOURCE_DIR}/../include
        ${CMAKE_CURRENT_BINARY_DIR}/../include
    )
endif()

# ========== 多后端示例 ==========

# 多后端切换示例
add_executable(multi_backend_example multi_backend.cpp)
target_link_libraries(multi_backend_example VSGAbstraction)
target_include_directories(multi_backend_example PRIVATE 
    ${CMAKE_CURRENT_SOURCE_DIR}/../include
    ${CMAKE_CURRENT_BINARY_DIR}/../include
)

# ========== 性能示例 ==========

# 性能测试示例
add_executable(performance_example performance_test.cpp)
target_link_libraries(performance_example VSGAbstraction)
target_include_directories(performance_example PRIVATE 
    ${CMAKE_CURRENT_SOURCE_DIR}/../include
    ${CMAKE_CURRENT_BINARY_DIR}/../include
)

# ========== 编译器设置 ==========

# 收集所有示例目标
set(EXAMPLE_TARGETS 
    basic_example 
    multi_backend_example 
    performance_example
)

if(VSG_ABSTRACTION_BUILD_VSG_BACKEND AND VSG_ABSTRACTION_HAS_VSG)
    list(APPEND EXAMPLE_TARGETS 
        vsg_compatibility_example 
        vsg_rendering_example
    )
endif()

# 为所有示例设置通用属性
foreach(target ${EXAMPLE_TARGETS})
    # C++20标准
    set_target_properties(${target} PROPERTIES
        CXX_STANDARD 20
        CXX_STANDARD_REQUIRED ON
        CXX_EXTENSIONS OFF
    )
    
    # 编译器特定选项
    if(MSVC)
        target_compile_options(${target} PRIVATE
            /W4 /WX- /permissive- /Zc:__cplusplus /utf-8
        )
        
        # 禁用特定警告
        target_compile_options(${target} PRIVATE
            /wd4251 # 需要dll接口的警告
            /wd4275 # 非dll接口基类的警告
        )
    else()
        target_compile_options(${target} PRIVATE
            -Wall -Wextra -Wpedantic -Wno-unused-parameter
        )
    endif()
    
    # 调试信息
    if(CMAKE_BUILD_TYPE STREQUAL "Debug")
        target_compile_definitions(${target} PRIVATE VSG_ABSTRACTION_DEBUG=1)
        if(MSVC)
            target_compile_options(${target} PRIVATE /Zi)
        else()
            target_compile_options(${target} PRIVATE -g)
        endif()
    endif()
    
    # 设置输出目录
    set_target_properties(${target} PROPERTIES
        RUNTIME_OUTPUT_DIRECTORY "${CMAKE_BINARY_DIR}/examples"
    )
endforeach()

# ========== 资源文件 ==========

# 复制示例资源文件（如果有的话）
if(EXISTS "${CMAKE_CURRENT_SOURCE_DIR}/data")
    file(COPY "${CMAKE_CURRENT_SOURCE_DIR}/data"
         DESTINATION "${CMAKE_BINARY_DIR}/examples")
endif()

# 复制着色器文件（如果有的话）
if(EXISTS "${CMAKE_CURRENT_SOURCE_DIR}/shaders")
    file(COPY "${CMAKE_CURRENT_SOURCE_DIR}/shaders"
         DESTINATION "${CMAKE_BINARY_DIR}/examples")
endif()

# ========== 自定义目标 ==========

# 构建所有示例的自定义目标
add_custom_target(build_examples
    DEPENDS ${EXAMPLE_TARGETS}
    COMMENT "Building all VSG Abstraction examples"
)

# 运行基础示例
add_custom_target(run_basic_example
    COMMAND basic_example
    DEPENDS basic_example
    WORKING_DIRECTORY "${CMAKE_BINARY_DIR}/examples"
    COMMENT "Running basic example"
)

# 运行多后端示例
add_custom_target(run_multi_backend_example
    COMMAND multi_backend_example
    DEPENDS multi_backend_example
    WORKING_DIRECTORY "${CMAKE_BINARY_DIR}/examples"
    COMMENT "Running multi-backend example"
)

# 运行性能示例
add_custom_target(run_performance_example
    COMMAND performance_example
    DEPENDS performance_example
    WORKING_DIRECTORY "${CMAKE_BINARY_DIR}/examples"
    COMMENT "Running performance example"
)

if(VSG_ABSTRACTION_BUILD_VSG_BACKEND AND VSG_ABSTRACTION_HAS_VSG)
    # 运行VSG兼容性示例
    add_custom_target(run_vsg_compatibility_example
        COMMAND vsg_compatibility_example
        DEPENDS vsg_compatibility_example
        WORKING_DIRECTORY "${CMAKE_BINARY_DIR}/examples"
        COMMENT "Running VSG compatibility example"
    )
    
    # 运行VSG渲染示例
    add_custom_target(run_vsg_rendering_example
        COMMAND vsg_rendering_example
        DEPENDS vsg_rendering_example
        WORKING_DIRECTORY "${CMAKE_BINARY_DIR}/examples"
        COMMENT "Running VSG rendering example"
    )
endif()

# ========== 安装配置 ==========

# 安装示例程序（可选）
if(VSG_ABSTRACTION_INSTALL_EXAMPLES)
    install(TARGETS ${EXAMPLE_TARGETS}
        RUNTIME DESTINATION examples
        COMPONENT Examples
    )
    
    # 安装示例源代码
    install(DIRECTORY "${CMAKE_CURRENT_SOURCE_DIR}/"
        DESTINATION examples/src
        COMPONENT Examples
        FILES_MATCHING 
        PATTERN "*.cpp"
        PATTERN "*.h"
        PATTERN "CMakeLists.txt"
    )
endif()

# ========== 状态报告 ==========

message(STATUS "")
message(STATUS "Example Configuration:")
message(STATUS "  Basic example: ENABLED")
message(STATUS "  Multi-backend example: ENABLED")
message(STATUS "  Performance example: ENABLED")

if(VSG_ABSTRACTION_BUILD_VSG_BACKEND AND VSG_ABSTRACTION_HAS_VSG)
    message(STATUS "  VSG compatibility example: ENABLED")
    message(STATUS "  VSG rendering example: ENABLED")
else()
    message(STATUS "  VSG examples: DISABLED (VSG not available)")
endif()

message(STATUS "  Example output directory: ${CMAKE_BINARY_DIR}/examples")
list(LENGTH EXAMPLE_TARGETS EXAMPLE_COUNT)
message(STATUS "  Total examples: ${EXAMPLE_COUNT}")
message(STATUS "")
