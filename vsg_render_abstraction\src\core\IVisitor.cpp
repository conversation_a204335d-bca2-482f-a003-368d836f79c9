#include <vsg_abstraction/core/IGeometry.h>
#include <vsg_abstraction/core/IGroup.h>
#include <vsg_abstraction/core/INode.h>
#include <vsg_abstraction/core/IStateGroup.h>
#include <vsg_abstraction/core/ITransform.h>
#include <vsg_abstraction/core/IVisitor.h>

namespace vsg_abstraction
{

    // ========== IVisitor实现 ==========

    void IVisitor::traverse(INode& node)
    {
        // 默认实现：不做任何遍历
        (void)node;
    }

    void IVisitor::traverse(IGroup& group)
    {
        // 默认实现：遍历所有子节点
        if (getTraversalMode() == TraversalMode::TRAVERSE_ALL_CHILDREN)
        {
            for (size_t i = 0; i < group.getNumChildren(); ++i)
            {
                auto child = group.getChild(i);
                if (child)
                {
                    child->accept(*this);
                }
            }
        }
    }

    // ========== IConstVisitor实现 ==========

    void IConstVisitor::traverse(const INode& node)
    {
        // 默认实现：不做任何遍历
        (void)node;
    }

    void IConstVisitor::traverse(const IGroup& group)
    {
        // 默认实现：遍历所有子节点
        if (getTraversalMode() == TraversalMode::TRAVERSE_ALL_CHILDREN)
        {
            for (size_t i = 0; i < group.getNumChildren(); ++i)
            {
                auto child = group.getChild(i);
                if (child)
                {
                    child->accept(*this);
                }
            }
        }
    }

} // namespace vsg_abstraction
