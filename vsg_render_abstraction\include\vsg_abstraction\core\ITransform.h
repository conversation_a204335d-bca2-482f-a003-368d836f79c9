#pragma once

/* <editor-fold desc="MIT License">

Copyright(c) 2024 VSG Render Engine Abstraction

Permission is hereby granted, free of charge, to any person obtaining a copy of this software and associated documentation files (the "Software"), to deal in the Software without restriction, including without limitation the rights to use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of the Software, and to permit persons to whom the Software is furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.

</editor-fold> */

#include <vsg_abstraction/core/IGroup.h>

namespace vsg_abstraction {

/**
 * @brief 变换节点接口
 */
class VSG_ABSTRACTION_API ITransform : public IGroup {
public:
    virtual ~ITransform() = default;

    // 矩阵变换
    virtual void setMatrix(const mat4& matrix) = 0;
    virtual mat4 getMatrix() const = 0;
    virtual void getWorldMatrix(mat4& matrix) const = 0;

    // 位置、旋转、缩放
    virtual void setPosition(const vec3& position) = 0;
    virtual vec3 getPosition() const = 0;

    virtual void setRotation(const vec4& rotation) = 0;
    virtual vec4 getRotation() const = 0;

    virtual void setScale(const vec3& scale) = 0;
    virtual vec3 getScale() const = 0;
};

} // namespace vsg_abstraction
