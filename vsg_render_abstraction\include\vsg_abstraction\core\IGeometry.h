#pragma once

/* <editor-fold desc="MIT License">

Copyright(c) 2024 VSG Render Engine Abstraction

Permission is hereby granted, free of charge, to any person obtaining a copy of this software and associated documentation files (the "Software"), to deal in the Software without restriction, including without limitation the rights to use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of the Software, and to permit persons to whom the Software is furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.

</editor-fold> */

#include <vector>
#include <vsg_abstraction/core/INode.h>

namespace vsg_abstraction
{

    /**
 * @brief 几何体接口
 */
    class VSG_ABSTRACTION_API IGeometry : public INode
    {
    public:
        virtual ~IGeometry() = default;

        // 顶点数据
        virtual void setVertices(const std::vector<vec3>& vertices) = 0;
        virtual std::vector<vec3> getVertices() const = 0;

        // 索引数据
        virtual void setIndices(const std::vector<uint32_t>& indices) = 0;
        virtual std::vector<uint32_t> getIndices() const = 0;

        // 法线数据
        virtual void setNormals(const std::vector<vec3>& normals) = 0;
        virtual std::vector<vec3> getNormals() const = 0;

        // 纹理坐标
        virtual void setTexCoords(const std::vector<vec2>& texCoords) = 0;
        virtual std::vector<vec2> getTexCoords() const = 0;

        // 颜色数据
        virtual void setColors(const std::vector<vec4>& colors) = 0;
        virtual std::vector<vec4> getColors() const = 0;

        // 图元拓扑
        virtual void setPrimitiveTopology(PrimitiveTopology topology) = 0;
        virtual PrimitiveTopology getPrimitiveTopology() const = 0;
    };

} // namespace vsg_abstraction
