/* <editor-fold desc="MIT License">

Copyright(c) 2024 VSG Render Engine Abstraction

Permission is hereby granted, free of charge, to any person obtaining a copy of this software and associated documentation files (the "Software"), to deal in the Software without restriction, including without limitation the rights to use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of the Software, and to permit persons to whom the Software is furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.

</editor-fold> */

#include <vsg_abstraction/core/RenderEngineManager.h>
#include <vsg_abstraction/core/Config.h>
#include <iostream>

using namespace vsg_abstraction;

int main() {
    std::cout << "=== Memory Test ===" << std::endl;
    
    auto& manager = getRenderManager();
    
    if (!manager.switchBackend(RenderBackend::Mock)) {
        std::cout << "Failed to switch to Mock backend" << std::endl;
        return 1;
    }
    
    // Test memory management
    {
        auto group = manager.createGroup();
        auto transform = manager.createTransform();
        auto geometry = manager.createGeometry();
        
        if (group && transform && geometry) {
            std::cout << "Objects created successfully" << std::endl;
        }
        
        // Objects should be automatically destroyed when going out of scope
    }
    
    auto stats = manager.getRenderStatistics();
    std::cout << "Memory usage: " << (stats.memoryUsed / 1024) << "KB" << std::endl;
    
    std::cout << "Memory test completed!" << std::endl;
    return 0;
}
