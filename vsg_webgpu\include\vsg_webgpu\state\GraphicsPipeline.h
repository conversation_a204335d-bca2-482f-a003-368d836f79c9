#pragma once

/* <editor-fold desc="MIT License">

Copyright(c) 2024 VSG WebGPU Extension

Permission is hereby granted, free of charge, to any person obtaining a copy of this software and associated documentation files (the "Software"), to deal in the Software without restriction, including without limitation the rights to use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of the Software, and to permit persons to whom the Software is furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.

</editor-fold> */

#include <vsg_webgpu/core/Export.h>
#include <vsg_webgpu/core/WebGPUHeaders.h>
#include <vsg/core/Inherit.h>
#include <vsg/core/Object.h>
#include <vsg/core/ref_ptr.h>
#include <vsg/state/StateCommand.h>
#include <vsg/state/GraphicsPipeline.h>
#include <vector>
#include <string>

namespace vsg_webgpu
{
    // forward declarations
    class Device;
    class State;
    class Context;

    /// WebGPU渲染管线状态基类，对应VSG的GraphicsPipelineState
    class VSG_WEBGPU_DECLSPEC GraphicsPipelineState : public vsg::Inherit<vsg::StateCommand, GraphicsPipelineState>
    {
    public:
        GraphicsPipelineState() {}
        GraphicsPipelineState(const GraphicsPipelineState& gps) :
            vsg::Inherit<vsg::StateCommand, GraphicsPipelineState>(), mask(gps.mask) {}

        /// 应用GraphicsPipelineState当(mask & view.mask)非零时
        vsg::Mask mask = vsg::MASK_ALL;

        virtual void apply(Context& context, WGPURenderPipelineDescriptor& pipelineDesc) const = 0;
        void record(vsg::CommandBuffer&) const override {}
    };

    using GraphicsPipelineStates = std::vector<vsg::ref_ptr<GraphicsPipelineState>>;

    /// WebGPU着色器阶段
    class VSG_WEBGPU_DECLSPEC ShaderStage : public vsg::Inherit<vsg::Object, ShaderStage>
    {
    public:
        ShaderStage(const std::string& wgslCode, const std::string& entryPoint = "main");

        std::string wgslCode;
        std::string entryPoint;
        
        // 编译着色器模块
        WGPUShaderModule compile(Device* device) const;

        int compare(const vsg::Object& rhs_object) const override;
    };

    using ShaderStages = std::vector<vsg::ref_ptr<ShaderStage>>;

    /// WebGPU顶点输入状态
    class VSG_WEBGPU_DECLSPEC VertexInputState : public vsg::Inherit<GraphicsPipelineState, VertexInputState>
    {
    public:
        VertexInputState();

        std::vector<WGPUVertexAttribute> vertexAttributes;
        std::vector<WGPUVertexBufferLayout> vertexBufferLayouts;

        void apply(Context& context, WGPURenderPipelineDescriptor& pipelineDesc) const override;

        int compare(const vsg::Object& rhs_object) const override;
    };

    /// WebGPU图元状态
    class VSG_WEBGPU_DECLSPEC PrimitiveState : public vsg::Inherit<GraphicsPipelineState, PrimitiveState>
    {
    public:
        PrimitiveState();

        WGPUPrimitiveTopology topology = WGPUPrimitiveTopology_TriangleList;
        WGPUIndexFormat stripIndexFormat = WGPUIndexFormat_Undefined;
        WGPUFrontFace frontFace = WGPUFrontFace_CCW;
        WGPUCullMode cullMode = WGPUCullMode_Back;

        void apply(Context& context, WGPURenderPipelineDescriptor& pipelineDesc) const override;

        int compare(const vsg::Object& rhs_object) const override;
    };

    /// WebGPU深度模板状态
    class VSG_WEBGPU_DECLSPEC DepthStencilState : public vsg::Inherit<GraphicsPipelineState, DepthStencilState>
    {
    public:
        DepthStencilState();

        WGPUTextureFormat format = WGPUTextureFormat_Depth32Float;
        bool depthWriteEnabled = true;
        WGPUCompareFunction depthCompare = WGPUCompareFunction_Less;

        // 模板测试
        WGPUStencilFaceState stencilFront = {};
        WGPUStencilFaceState stencilBack = {};
        uint32_t stencilReadMask = 0xFFFFFFFF;
        uint32_t stencilWriteMask = 0xFFFFFFFF;

        void apply(Context& context, WGPURenderPipelineDescriptor& pipelineDesc) const override;

        int compare(const vsg::Object& rhs_object) const override;
    };

    /// WebGPU多重采样状态
    class VSG_WEBGPU_DECLSPEC MultisampleState : public vsg::Inherit<GraphicsPipelineState, MultisampleState>
    {
    public:
        MultisampleState();

        uint32_t count = 1;
        uint32_t mask = 0xFFFFFFFF;
        bool alphaToCoverageEnabled = false;

        void apply(Context& context, WGPURenderPipelineDescriptor& pipelineDesc) const override;

        int compare(const vsg::Object& rhs_object) const override;
    };

    /// WebGPU颜色混合状态
    class VSG_WEBGPU_DECLSPEC ColorBlendState : public vsg::Inherit<GraphicsPipelineState, ColorBlendState>
    {
    public:
        ColorBlendState();

        std::vector<WGPUColorTargetState> targets;

        void apply(Context& context, WGPURenderPipelineDescriptor& pipelineDesc) const override;

        int compare(const vsg::Object& rhs_object) const override;
    };

    /// WebGPU图形管线
    class VSG_WEBGPU_DECLSPEC GraphicsPipeline : public vsg::Inherit<vsg::Object, GraphicsPipeline>
    {
    public:
        GraphicsPipeline();
        GraphicsPipeline(const ShaderStages& shaderStages, 
                        const GraphicsPipelineStates& pipelineStates);

        /// 获取WebGPU管线对象
        WGPURenderPipeline getPipeline(uint32_t viewID = 0) const;

        /// 管线配置
        ShaderStages stages;
        GraphicsPipelineStates pipelineStates;

        int compare(const vsg::Object& rhs_object) const override;

        // 编译管线
        void compile(Context& context);

        // 释放资源
        void release(uint32_t viewID = 0);
        void release();

    protected:
        virtual ~GraphicsPipeline();

    private:
        struct Implementation
        {
            WGPURenderPipeline pipeline;
            Device* device;
        };

        std::vector<std::unique_ptr<Implementation>> _implementations;
    };

    /// WebGPU绑定图形管线命令
    class VSG_WEBGPU_DECLSPEC BindGraphicsPipeline : public vsg::Inherit<vsg::StateCommand, BindGraphicsPipeline>
    {
    public:
        explicit BindGraphicsPipeline(vsg::ref_ptr<GraphicsPipeline> pipeline = {});

        vsg::ref_ptr<GraphicsPipeline> pipeline;

        int compare(const vsg::Object& rhs_object) const override;

        void record(vsg::CommandBuffer& commandBuffer) const override;
        void record(State& state) const;
    };

    VSG_type_name(vsg_webgpu::GraphicsPipeline);
    VSG_type_name(vsg_webgpu::BindGraphicsPipeline);

} // namespace vsg_webgpu
