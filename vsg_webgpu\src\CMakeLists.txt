# VSG WebGPU源文件

# 收集源文件
set(SOURCES
    # 核心文件
    core/WebGPUHeaders.cpp
    
    # 设备和资源管理
    vk/Device.cpp
    vk/Queue.cpp
    vk/CommandBuffer.cpp
    vk/CommandPool.cpp
    vk/Context.cpp
    vk/State.cpp
    
    # 状态管理
    state/GraphicsPipeline.cpp
    state/ComputePipeline.cpp
    state/ShaderModule.cpp
    state/Buffer.cpp
    state/Image.cpp
    state/Sampler.cpp
    state/DescriptorSet.cpp
    
    # 渲染系统
    app/RenderGraph.cpp
    app/RecordTraversal.cpp
    app/Window.cpp
    
    # 命令
    commands/Draw.cpp
    commands/DrawIndexed.cpp
    commands/Dispatch.cpp
    commands/BindDescriptorSet.cpp
    commands/BindVertexBuffers.cpp
    commands/BindIndexBuffer.cpp
    
    # 工具
    utils/Builder.cpp
    utils/ShaderCompiler.cpp
)

# 收集头文件
set(HEADERS
    # 核心头文件
    ${CMAKE_CURRENT_SOURCE_DIR}/../include/vsg_webgpu/all.h
    ${CMAKE_CURRENT_SOURCE_DIR}/../include/vsg_webgpu/core/Export.h
    ${CMAKE_CURRENT_SOURCE_DIR}/../include/vsg_webgpu/core/WebGPUHeaders.h
    ${CMAKE_CURRENT_SOURCE_DIR}/../include/vsg_webgpu/core/MockWebGPU.h
    
    # 设备和资源管理
    ${CMAKE_CURRENT_SOURCE_DIR}/../include/vsg_webgpu/vk/Device.h
    ${CMAKE_CURRENT_SOURCE_DIR}/../include/vsg_webgpu/vk/Queue.h
    ${CMAKE_CURRENT_SOURCE_DIR}/../include/vsg_webgpu/vk/CommandBuffer.h
    ${CMAKE_CURRENT_SOURCE_DIR}/../include/vsg_webgpu/vk/CommandPool.h
    ${CMAKE_CURRENT_SOURCE_DIR}/../include/vsg_webgpu/vk/Context.h
    ${CMAKE_CURRENT_SOURCE_DIR}/../include/vsg_webgpu/vk/State.h
)

# 创建库
add_library(vsg_webgpu ${SOURCES} ${HEADERS})

# 设置目标属性
set_target_properties(vsg_webgpu PROPERTIES
    CXX_STANDARD 20
    CXX_STANDARD_REQUIRED ON
    VERSION ${PROJECT_VERSION}
    SOVERSION ${PROJECT_VERSION_MAJOR}
)

# 定义导出宏
target_compile_definitions(vsg_webgpu PRIVATE VSG_WEBGPU_EXPORTS)

# 包含目录
target_include_directories(vsg_webgpu
    PUBLIC
        $<BUILD_INTERFACE:${CMAKE_CURRENT_SOURCE_DIR}/../include>
        $<INSTALL_INTERFACE:include>
    PRIVATE
        ${CMAKE_CURRENT_SOURCE_DIR}
)

# 链接库
target_link_libraries(vsg_webgpu
    PUBLIC
        vsg::vsg
    PRIVATE
)

# WebGPU特定链接
if(VSG_WEBGPU_USE_EMSCRIPTEN)
    # Emscripten WebGPU
    target_compile_definitions(vsg_webgpu PUBLIC VSG_WEBGPU_PLATFORM_EMSCRIPTEN=1)
elseif(VSG_WEBGPU_USE_DAWN AND DAWN_FOUND)
    # Dawn WebGPU
    target_link_libraries(vsg_webgpu PRIVATE dawn)
    target_compile_definitions(vsg_webgpu PUBLIC VSG_WEBGPU_USE_DAWN=1)
else()
    # Mock implementation
    target_compile_definitions(vsg_webgpu PUBLIC VSG_WEBGPU_MOCK_IMPLEMENTATION=1)
endif()

# Vulkan支持（如果可用）
if(NOT VSG_WEBGPU_USE_EMSCRIPTEN AND Vulkan_FOUND)
    target_link_libraries(vsg_webgpu PRIVATE Vulkan::Vulkan)
    target_compile_definitions(vsg_webgpu PRIVATE VSG_WEBGPU_VULKAN_SUPPORT=1)
endif()

# 安装 - 简化版本，避免导出问题
install(TARGETS vsg_webgpu
    LIBRARY DESTINATION lib
    ARCHIVE DESTINATION lib
    RUNTIME DESTINATION bin
)

install(DIRECTORY ${CMAKE_CURRENT_SOURCE_DIR}/../include/
    DESTINATION include
    FILES_MATCHING PATTERN "*.h"
)
