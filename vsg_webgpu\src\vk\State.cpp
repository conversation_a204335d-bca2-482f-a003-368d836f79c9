/* <editor-fold desc="MIT License">

Copyright(c) 2024 VSG WebGPU Extension

Permission is hereby granted, free of charge, to any person obtaining a copy of this software and associated documentation files (the "Software"), to deal in the Software without restriction, including without limitation the rights to use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of the Software, and to permit persons to whom the Software is furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.

</editor-fold> */

#include <vsg_webgpu/vk/State.h>
#include <vsg_webgpu/vk/Device.h>
#include <vsg_webgpu/vk/CommandBuffer.h>

using namespace vsg_webgpu;

State::State(Device* device, const vsg::Slots& maxSlots) :
    maxSlots(maxSlots),
    _device(device)
{
    VSG_WEBGPU_LOG_DEBUG("State::State()");
    
    reserve(maxSlots);
    
    // 初始化绑定资源
    _boundResources = {};
}

void State::reserve(const vsg::Slots& in_maxSlots)
{
    maxSlots = in_maxSlots;
    uint32_t maxSlot = maxSlots.max();
    
    if (stateStacks.size() < maxSlot)
    {
        stateStacks.resize(maxSlot);
    }
    
    activeMaxStateSlot = maxSlot;
}

void State::connect(vsg::ref_ptr<CommandBuffer> commandBuffer)
{
    _commandBuffer = commandBuffer;
}

void State::reset()
{
    // 重置所有状态栈
    for (auto& stack : stateStacks)
    {
        while (!stack.empty())
        {
            stack.pop();
        }
    }
    
    // 重置矩阵栈
    projectionMatrixStack = vsg::MatrixStack{0};
    modelviewMatrixStack = vsg::MatrixStack{64};
    
    // 重置视锥体栈
    while (!_frustumStack.empty())
    {
        _frustumStack.pop();
    }
    
    // 重置绑定资源
    _boundResources = {};
    
    dirty = true;
}

void State::pushView(vsg::ref_ptr<vsg::StateCommand> command)
{
    if (command && command->slot < stateStacks.size())
    {
        stateStacks[command->slot].push(command);
        dirty = true;
    }
}

void State::popView(vsg::ref_ptr<vsg::StateCommand> command)
{
    if (command && command->slot < stateStacks.size() && !stateStacks[command->slot].empty())
    {
        stateStacks[command->slot].pop();
        dirty = true;
    }
}

void State::record()
{
    if (!dirty) return;
    
    // 记录当前状态到命令缓冲区
    // 在WebGPU中，这主要涉及设置渲染状态
    
    if (_commandBuffer && _renderPassEncoder)
    {
        // 应用当前绑定的管线
        if (_boundResources.renderPipeline)
        {
            _renderPassEncoder.SetPipeline(_boundResources.renderPipeline);
        }
        
        // 应用绑定组
        for (uint32_t i = 0; i < _boundResources.bindGroups.size(); ++i)
        {
            if (_boundResources.bindGroups[i])
            {
                _renderPassEncoder.SetBindGroup(i, _boundResources.bindGroups[i]);
            }
        }
        
        // 应用顶点缓冲区
        for (uint32_t i = 0; i < _boundResources.vertexBuffers.size(); ++i)
        {
            if (_boundResources.vertexBuffers[i])
            {
                _renderPassEncoder.SetVertexBuffer(i, _boundResources.vertexBuffers[i], 
                                                  _boundResources.vertexBufferOffsets[i]);
            }
        }
        
        // 应用索引缓冲区
        if (_boundResources.indexBuffer)
        {
            _renderPassEncoder.SetIndexBuffer(_boundResources.indexBuffer, 
                                            _boundResources.indexFormat,
                                            _boundResources.indexBufferOffset);
        }
    }
    
    dirty = false;
}

void State::apply(const vsg::StateCommand* command)
{
    if (!command) return;
    
    // 应用状态命令
    if (command->slot < stateStacks.size())
    {
        // 这里可以根据具体的状态命令类型进行处理
        dirty = true;
    }
}

void State::apply(const vsg::StateGroup* stateGroup)
{
    if (!stateGroup) return;
    
    // 推入状态组的状态命令
    for (auto& stateCommand : stateGroup->stateCommands)
    {
        if (stateCommand && stateCommand->slot < stateStacks.size())
        {
            stateStacks[stateCommand->slot].push(stateCommand);
        }
    }
    
    dirty = true;
}

void State::pushProjectionMatrix(const vsg::mat4& matrix)
{
    // 在mock VSG中，我们直接存储矩阵
    // 实际VSG中会使用MatrixStack
    _projectionMatrix = matrix;
}

void State::popProjectionMatrix()
{
    // 在mock实现中，我们恢复到单位矩阵
    _projectionMatrix = vsg::mat4{};
}

void State::pushViewMatrix(const vsg::mat4& matrix)
{
    _viewMatrix = matrix;
}

void State::popViewMatrix()
{
    _viewMatrix = vsg::mat4{};
}

void State::pushModelMatrix(const vsg::mat4& matrix)
{
    _modelMatrix = matrix;
}

void State::popModelMatrix()
{
    _modelMatrix = vsg::mat4{};
}

vsg::mat4 State::getProjectionMatrix() const
{
    return _projectionMatrix;
}

vsg::mat4 State::getViewMatrix() const
{
    return _viewMatrix;
}

vsg::mat4 State::getModelMatrix() const
{
    return _modelMatrix;
}

vsg::mat4 State::getMVPMatrix() const
{
    // 计算MVP矩阵：Projection * View * Model
    vsg::mat4 mvp;
    
    // 简化的矩阵乘法（在实际VSG中会使用优化的矩阵运算）
    for (int i = 0; i < 4; ++i)
    {
        for (int j = 0; j < 4; ++j)
        {
            mvp[i][j] = 0.0f;
            for (int k = 0; k < 4; ++k)
            {
                mvp[i][j] += _projectionMatrix[i][k] * _viewMatrix[k][j];
            }
        }
    }
    
    vsg::mat4 result;
    for (int i = 0; i < 4; ++i)
    {
        for (int j = 0; j < 4; ++j)
        {
            result[i][j] = 0.0f;
            for (int k = 0; k < 4; ++k)
            {
                result[i][j] += mvp[i][k] * _modelMatrix[k][j];
            }
        }
    }
    
    return result;
}

void State::setInheritedViewProjectionAndViewMatrix(const vsg::dmat4& projMatrix, const vsg::dmat4& viewMatrix)
{
    inheritedProjectionMatrix = projMatrix;
    inheritedViewMatrix = viewMatrix;
    inheritViewForLODScaling = true;
}

void State::_dirtyStateStacks()
{
    dirty = true;
}
