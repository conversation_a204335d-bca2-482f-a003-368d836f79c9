/* <editor-fold desc="MIT License">

Copyright(c) 2024 VSG Render Engine Abstraction

Permission is hereby granted, free of charge, to any person obtaining a copy of this software and associated documentation files (the "Software"), to deal in the Software without restriction, including without limitation the rights to use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of the Software, and to permit persons to whom the Software is furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND <PERSON>NINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.

</editor-fold> */

/**
 * @file test_multi_backend.cpp
 * @brief 多后端切换测试
 */

#include <vsg_abstraction/core/RenderEngineManager.h>
#include <vsg_abstraction/core/RenderEngineFactory.h>
#include <vsg_abstraction/core/Config.h>

#include <iostream>
#include <cassert>

using namespace vsg_abstraction;

int main() {
    std::cout << "=== 多后端切换测试 ===" << std::endl;
    
    auto& manager = getRenderManager();
    
    // 测试支持的后端
    auto supportedBackends = RenderEngineFactory::getSupportedBackends();
    std::cout << "支持的后端数量: " << supportedBackends.size() << std::endl;
    
    for (auto backend : supportedBackends) {
        std::cout << "  - " << getRenderBackendName(backend) << std::endl;
    }
    
    // 测试后端切换
    bool testPassed = true;
    
    for (auto backend : supportedBackends) {
        std::cout << "\n测试后端: " << getRenderBackendName(backend) << std::endl;
        
        if (manager.switchBackend(backend)) {
            std::cout << "  ✓ 切换成功" << std::endl;
            
            // 测试基本功能
            auto group = manager.createGroup();
            if (group) {
                std::cout << "  ✓ 创建Group成功" << std::endl;
            } else {
                std::cout << "  ✗ 创建Group失败" << std::endl;
                testPassed = false;
            }
            
            auto transform = manager.createTransform();
            if (transform) {
                std::cout << "  ✓ 创建Transform成功" << std::endl;
            } else {
                std::cout << "  ✗ 创建Transform失败" << std::endl;
                testPassed = false;
            }
            
            // 测试引擎信息
            auto info = manager.getEngineInfo();
            std::cout << "  引擎: " << info.name << " v" << info.version << std::endl;
            
        } else {
            std::cout << "  ✗ 切换失败" << std::endl;
            testPassed = false;
        }
    }
    
    if (testPassed) {
        std::cout << "\n🎉 所有多后端测试通过！" << std::endl;
        return 0;
    } else {
        std::cout << "\n❌ 部分多后端测试失败" << std::endl;
        return 1;
    }
}
