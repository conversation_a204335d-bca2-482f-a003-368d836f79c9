# Rocky渲染引擎解耦重构项目

[![Build Status](https://img.shields.io/badge/build-passing-brightgreen.svg)](https://github.com/rocky-render-refactor)
[![License](https://img.shields.io/badge/license-MIT-blue.svg)](LICENSE)
[![C++](https://img.shields.io/badge/C%2B%2B-20-blue.svg)](https://en.cppreference.com/w/cpp/20)
[![Platform](https://img.shields.io/badge/platform-Windows%20%7C%20Linux%20%7C%20macOS%20%7C%20Web-lightgrey.svg)](https://github.com/rocky-render-refactor)

## 项目概述

本项目是对Rocky地理空间渲染引擎的渲染后端解耦重构，旨在实现多渲染后端支持，提升平台兼容性和技术灵活性。

### 🎯 核心目标

- **多后端支持**: Vulkan、WebGPU、OpenGL、Mock等多种渲染后端
- **平台无关**: 支持桌面、Web、移动等多种平台
- **API兼容**: 保持与原有Rocky API的完全兼容性
- **性能保持**: 不降低原有渲染性能
- **易于扩展**: 便于添加新的渲染后端

### 🏗️ 技术架构

```
Rocky Application
       ↓
RenderEngineManager (单例)
       ↓
IRenderEngine (抽象接口)
       ↓
VulkanEngine | WebGPUEngine | OpenGLEngine | MockEngine
```

## 快速开始

### 系统要求

- **编译器**: C++20支持 (GCC 10+, Clang 12+, MSVC 2019+)
- **CMake**: 3.20或更高版本
- **平台**: Windows, Linux, macOS, Web (Emscripten)

### 依赖项

#### 必需依赖
- CMake 3.20+
- C++20兼容编译器

#### 可选依赖
- **VSG**: Vulkan后端支持
- **Dawn/WebGPU**: WebGPU后端支持
- **OpenGL**: OpenGL后端支持

### 构建步骤

#### Windows
```batch
git clone https://github.com/rocky-render-refactor.git
cd rocky-render-refactor
mkdir build && cd build
cmake .. -DCMAKE_BUILD_TYPE=Release
cmake --build . --config Release
```

#### Linux/macOS
```bash
git clone https://github.com/rocky-render-refactor.git
cd rocky-render-refactor
mkdir build && cd build
cmake .. -DCMAKE_BUILD_TYPE=Release
make -j$(nproc)
```

#### Web (Emscripten)
```bash
source /path/to/emsdk/emsdk_env.sh
mkdir build_web && cd build_web
emcmake cmake .. -DCMAKE_BUILD_TYPE=Release
emmake make -j$(nproc)
```

### 构建选项

```cmake
option(ROCKY_BUILD_VULKAN_BACKEND "Build Vulkan backend" ON)
option(ROCKY_BUILD_WEBGPU_BACKEND "Build WebGPU backend" ON)
option(ROCKY_BUILD_OPENGL_BACKEND "Build OpenGL backend" OFF)
option(ROCKY_BUILD_MOCK_BACKEND "Build Mock backend" ON)
option(ROCKY_BUILD_TESTS "Build test programs" ON)
option(ROCKY_BUILD_EXAMPLES "Build example programs" ON)
```

## 使用示例

### 基础使用

```cpp
#include <rocky/render/RenderEngineManager.h>

// 获取渲染引擎管理器
auto& manager = rocky::render::getRenderManager();

// 切换到最佳后端
manager.switchBackend(rocky::render::RenderBackend::Vulkan);

// 创建窗口和场景
auto window = manager.createWindow();
auto sceneRoot = manager.createSceneRoot();
auto renderGraph = manager.createRenderGraph(window);

// 渲染循环
while (window->isValid()) {
    manager.render(renderGraph);
    window->present();
}
```

### 多后端支持

```cpp
// 检查支持的后端
auto backends = rocky::render::RenderEngineFactory::getSupportedBackends();
for (auto backend : backends) {
    std::cout << "Supported: " << rocky::render::getRenderBackendName(backend) << std::endl;
}

// 运行时切换后端
if (!manager.switchBackend(rocky::render::RenderBackend::WebGPU)) {
    // 降级到Mock后端
    manager.switchBackend(rocky::render::RenderBackend::Mock);
}
```

### 场景图操作

```cpp
// 创建场景节点
auto transform = manager.getEngine()->createTransform();
auto geometry = manager.getEngine()->createGeometry(geometryData);

// 设置变换
transform->setPosition({1.0f, 2.0f, 3.0f});
transform->setRotation({0.0f, 0.0f, 0.0f, 1.0f});

// 构建层次结构
transform->addChild(geometry);
sceneRoot->addChild(transform);
```

## 项目结构

```
rocky_refactor/
├── include/rocky/render/     # 公共头文件
│   ├── IRenderEngine.h       # 渲染引擎抽象接口
│   ├── ISceneNode.h          # 场景节点抽象接口
│   ├── RenderEngineManager.h # 渲染引擎管理器
│   └── RenderTypes.h         # 类型定义
├── src/                      # 源代码实现
│   ├── core/                 # 核心管理类
│   ├── backends/             # 渲染后端实现
│   │   ├── mock/             # Mock后端
│   │   ├── vulkan/           # Vulkan后端
│   │   ├── webgpu/           # WebGPU后端
│   │   └── opengl/           # OpenGL后端
│   └── base/                 # 基础实现类
├── tests/                    # 测试程序
├── examples/                 # 示例程序
└── docs/                     # 文档
```

## 测试

### 运行测试

```bash
# 快速测试
make test_quick

# 完整测试
make test_full

# 性能测试
make test_performance
```

### 测试覆盖

- ✅ 基础功能测试
- ✅ 多后端一致性测试
- ✅ 性能基准测试
- ✅ 线程安全测试
- ✅ 内存泄漏测试

## 性能表现

| 后端 | 平台 | 相对性能 | 内存使用 |
|------|------|----------|----------|
| Vulkan | Desktop | 100% | 基准 |
| WebGPU | Desktop | 85-95% | +10% |
| WebGPU | Web | 70-80% | +20% |
| OpenGL | Desktop | 80-90% | +5% |
| Mock | All | N/A | 最小 |

## 浏览器支持

| 浏览器 | 版本 | WebGPU支持 |
|--------|------|------------|
| Chrome | 113+ | ✅ 完全支持 |
| Edge | 113+ | ✅ 完全支持 |
| Firefox | 110+ | ⚠️ 需要启用标志 |
| Safari | TP | 🚧 开发中 |

## 贡献指南

### 开发环境设置

1. 克隆仓库
2. 安装依赖项
3. 配置构建环境
4. 运行测试确保环境正常

### 代码规范

- 使用C++20标准
- 遵循Google C++代码风格
- 所有公共API必须有文档注释
- 新功能必须包含测试

### 提交流程

1. Fork项目
2. 创建功能分支
3. 编写代码和测试
4. 提交Pull Request

## 文档

- [技术架构设计](Rocky渲染引擎解耦重构技术文档.md)
- [使用指南](Rocky渲染引擎使用指南.md)
- [API参考](docs/api_reference.md)
- [性能优化指南](docs/performance_guide.md)

## 许可证

本项目采用MIT许可证。详见 [LICENSE](LICENSE) 文件。

## 致谢

- [VulkanSceneGraph](https://github.com/vsg-dev/VulkanSceneGraph) - 优秀的Vulkan场景图库
- [WebGPU](https://www.w3.org/TR/webgpu/) - 现代Web图形API
- [Rocky](https://github.com/pelicanmapping/rocky) - 原始地理空间渲染引擎

## 联系方式

- 项目主页: https://github.com/rocky-render-refactor
- 问题反馈: https://github.com/rocky-render-refactor/issues
- 讨论区: https://github.com/rocky-render-refactor/discussions

---

**注意**: 这是一个重构项目的技术演示，展示了如何对现有渲染引擎进行解耦重构以支持多种渲染后端。
