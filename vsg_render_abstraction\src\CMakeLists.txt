# VSG渲染引擎抽象层源文件构建配置

# ========== 核心源文件 ==========

set(CORE_SOURCES
    core/RenderEngineManager.cpp
    core/RenderEngineFactory.cpp
    core/Types.cpp
)

set(CORE_HEADERS
    ${CMAKE_CURRENT_SOURCE_DIR}/../include/vsg_abstraction/core/IRenderEngine.h
    ${CMAKE_CURRENT_SOURCE_DIR}/../include/vsg_abstraction/core/RenderEngineManager.h
    ${CMAKE_CURRENT_SOURCE_DIR}/../include/vsg_abstraction/core/RenderEngineFactory.h
    ${CMAKE_CURRENT_SOURCE_DIR}/../include/vsg_abstraction/core/Types.h
    ${CMAKE_CURRENT_SOURCE_DIR}/../include/vsg_abstraction/core/Export.h
    ${CMAKE_CURRENT_SOURCE_DIR}/../include/vsg_abstraction/nodes/INode.h
    ${CMAKE_CURRENT_BINARY_DIR}/../include/vsg_abstraction/core/Config.h
)

# ========== 后端源文件 ==========

set(BACKEND_SOURCES)
set(BACKEND_HEADERS)

# VSG后端
if(VSG_ABSTRACTION_BUILD_VSG_BACKEND AND VSG_ABSTRACTION_HAS_VSG)
    list(APPEND BACKEND_SOURCES
        backends/vsg/VulkanRenderEngine.cpp
        adapters/VSGNodeAdapter.cpp
    )
    list(APPEND BACKEND_HEADERS
        ${CMAKE_CURRENT_SOURCE_DIR}/../include/vsg_abstraction/backends/VSGRenderEngine.h
        ${CMAKE_CURRENT_SOURCE_DIR}/../include/vsg_abstraction/adapters/VSGNodeAdapter.h
    )
    message(STATUS "Adding VSG backend sources")
endif()

# WebGPU后端
if(VSG_ABSTRACTION_BUILD_WEBGPU_BACKEND AND VSG_ABSTRACTION_HAS_WEBGPU)
    list(APPEND BACKEND_SOURCES
        backends/webgpu/WebGPURenderEngine.cpp
    )
    list(APPEND BACKEND_HEADERS
        ${CMAKE_CURRENT_SOURCE_DIR}/../include/vsg_abstraction/backends/WebGPURenderEngine.h
    )
    message(STATUS "Adding WebGPU backend sources")
endif()

# OpenGL后端
if(VSG_ABSTRACTION_BUILD_OPENGL_BACKEND AND VSG_ABSTRACTION_HAS_OPENGL)
    list(APPEND BACKEND_SOURCES
        backends/opengl/OpenGLRenderEngine.cpp
    )
    list(APPEND BACKEND_HEADERS
        ${CMAKE_CURRENT_SOURCE_DIR}/../include/vsg_abstraction/backends/OpenGLRenderEngine.h
    )
    message(STATUS "Adding OpenGL backend sources")
endif()

# Mock后端
if(VSG_ABSTRACTION_BUILD_MOCK_BACKEND)
    list(APPEND BACKEND_SOURCES
        backends/mock/MockRenderEngine.cpp
    )
    list(APPEND BACKEND_HEADERS
        ${CMAKE_CURRENT_SOURCE_DIR}/../include/vsg_abstraction/backends/MockRenderEngine.h
    )
    message(STATUS "Adding Mock backend sources")
endif()

# ========== 创建库目标 ==========

set(ALL_SOURCES ${CORE_SOURCES} ${BACKEND_SOURCES})
set(ALL_HEADERS ${CORE_HEADERS} ${BACKEND_HEADERS})

# 创建库
add_library(VSGAbstraction ${VSG_ABSTRACTION_LIBRARY_TYPE} ${ALL_SOURCES} ${ALL_HEADERS})

# 设置目标属性
set_target_properties(VSGAbstraction PROPERTIES
    VERSION ${PROJECT_VERSION}
    SOVERSION ${PROJECT_VERSION_MAJOR}
    OUTPUT_NAME "vsg_abstraction"
    DEBUG_POSTFIX "_d"
    CXX_STANDARD 20
    CXX_STANDARD_REQUIRED ON
    CXX_EXTENSIONS OFF
)

# ========== 包含目录 ==========

target_include_directories(VSGAbstraction
    PUBLIC
        $<BUILD_INTERFACE:${CMAKE_CURRENT_SOURCE_DIR}/../include>
        $<BUILD_INTERFACE:${CMAKE_CURRENT_BINARY_DIR}/../include>
        $<INSTALL_INTERFACE:include>
    PRIVATE
        ${CMAKE_CURRENT_SOURCE_DIR}
)

# ========== 编译定义 ==========

target_compile_definitions(VSGAbstraction PRIVATE
    VSG_ABSTRACTION_VERSION_MAJOR=${PROJECT_VERSION_MAJOR}
    VSG_ABSTRACTION_VERSION_MINOR=${PROJECT_VERSION_MINOR}
    VSG_ABSTRACTION_VERSION_PATCH=${PROJECT_VERSION_PATCH}
    VSG_ABSTRACTION_VERSION="${PROJECT_VERSION}"
)

# 共享库导出定义
if(VSG_ABSTRACTION_BUILD_SHARED_LIBS)
    target_compile_definitions(VSGAbstraction PRIVATE VSG_ABSTRACTION_EXPORTS)
    target_compile_definitions(VSGAbstraction PUBLIC VSG_ABSTRACTION_SHARED_LIBRARY)
else()
    target_compile_definitions(VSGAbstraction PUBLIC VSG_ABSTRACTION_STATIC_LIBRARY)
endif()

# 后端支持定义
if(VSG_ABSTRACTION_HAS_VSG)
    target_compile_definitions(VSGAbstraction PUBLIC VSG_ABSTRACTION_HAS_VSG)
endif()

if(VSG_ABSTRACTION_HAS_WEBGPU)
    target_compile_definitions(VSGAbstraction PUBLIC VSG_ABSTRACTION_HAS_WEBGPU)
endif()

if(VSG_ABSTRACTION_HAS_OPENGL)
    target_compile_definitions(VSGAbstraction PUBLIC VSG_ABSTRACTION_HAS_OPENGL)
endif()

if(VSG_ABSTRACTION_BUILD_MOCK_BACKEND)
    target_compile_definitions(VSGAbstraction PUBLIC VSG_ABSTRACTION_HAS_MOCK)
endif()

# ========== 链接库 ==========

# 线程支持
target_link_libraries(VSGAbstraction PUBLIC Threads::Threads)

# VSG后端依赖
if(VSG_ABSTRACTION_BUILD_VSG_BACKEND AND VSG_ABSTRACTION_HAS_VSG)
    target_link_libraries(VSGAbstraction PUBLIC vsg::vsg)
    message(STATUS "Linking VSG library")
endif()

# OpenGL后端依赖
if(VSG_ABSTRACTION_BUILD_OPENGL_BACKEND AND VSG_ABSTRACTION_HAS_OPENGL)
    target_link_libraries(VSGAbstraction PUBLIC OpenGL::GL)
    message(STATUS "Linking OpenGL library")
endif()

# ========== 编译器特定设置 ==========

if(MSVC)
    target_compile_options(VSGAbstraction PRIVATE
        /W4          # 警告级别4
        /WX-         # 警告不作为错误
        /permissive- # 严格标准符合性
        /Zc:__cplusplus # 正确的__cplusplus宏值
        /utf-8       # UTF-8源文件编码
    )
    
    # 禁用特定警告
    target_compile_options(VSGAbstraction PRIVATE
        /wd4251 # 需要dll接口的警告
        /wd4275 # 非dll接口基类的警告
    )
else()
    target_compile_options(VSGAbstraction PRIVATE
        -Wall
        -Wextra
        -Wpedantic
        -Wno-unused-parameter
    )
    
    # GCC/Clang特定选项
    if(CMAKE_CXX_COMPILER_ID STREQUAL "GNU")
        target_compile_options(VSGAbstraction PRIVATE -Wno-maybe-uninitialized)
    endif()
endif()

# ========== 调试信息 ==========

if(CMAKE_BUILD_TYPE STREQUAL "Debug")
    target_compile_definitions(VSGAbstraction PRIVATE VSG_ABSTRACTION_DEBUG=1)
    if(MSVC)
        target_compile_options(VSGAbstraction PRIVATE /Zi)
    else()
        target_compile_options(VSGAbstraction PRIVATE -g)
    endif()
endif()

# ========== 安装配置 ==========

# 安装库文件
install(TARGETS VSGAbstraction
    EXPORT VSGAbstractionTargets
    LIBRARY DESTINATION lib
    ARCHIVE DESTINATION lib
    RUNTIME DESTINATION bin
    INCLUDES DESTINATION include
)

# 导出目标
install(EXPORT VSGAbstractionTargets
    FILE VSGAbstractionTargets.cmake
    NAMESPACE VSGAbstraction::
    DESTINATION lib/cmake/VSGAbstraction
)

# ========== 状态报告 ==========

message(STATUS "")
message(STATUS "VSG Abstraction Library Configuration:")
message(STATUS "  Library type: ${VSG_ABSTRACTION_LIBRARY_TYPE}")
message(STATUS "  Core sources: ${CORE_SOURCES}")
message(STATUS "  Backend sources: ${BACKEND_SOURCES}")
message(STATUS "  Total sources: ${ALL_SOURCES}")
message(STATUS "")

# 显示后端状态
message(STATUS "Backend Status:")
if(VSG_ABSTRACTION_BUILD_VSG_BACKEND AND VSG_ABSTRACTION_HAS_VSG)
    message(STATUS "  ✓ VSG Backend: ENABLED")
else()
    message(STATUS "  ✗ VSG Backend: DISABLED")
endif()

if(VSG_ABSTRACTION_BUILD_WEBGPU_BACKEND AND VSG_ABSTRACTION_HAS_WEBGPU)
    message(STATUS "  ✓ WebGPU Backend: ENABLED")
else()
    message(STATUS "  ✗ WebGPU Backend: DISABLED")
endif()

if(VSG_ABSTRACTION_BUILD_OPENGL_BACKEND AND VSG_ABSTRACTION_HAS_OPENGL)
    message(STATUS "  ✓ OpenGL Backend: ENABLED")
else()
    message(STATUS "  ✗ OpenGL Backend: DISABLED")
endif()

if(VSG_ABSTRACTION_BUILD_MOCK_BACKEND)
    message(STATUS "  ✓ Mock Backend: ENABLED")
else()
    message(STATUS "  ✗ Mock Backend: DISABLED")
endif()

message(STATUS "")
