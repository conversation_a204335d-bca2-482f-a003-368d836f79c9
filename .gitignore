

lib/
bin/
build/

# local clone of gslang
src/glslang

# Autogenerated files
include/vsg/core/Version.h

CMakeDoxyfile.in
CMakeDoxygenDefaults.cmake
Doxyfile.docs
docs/ExplorationPhase/3rdPartyResources.md.backup
vsgConfigVersion.cmake
vsgConfig.cmake
Doxyfile.docs-vsg

html

*.pc
*.conf
*.backup
cmake_uninstall.cmake
CMakeCache.txt
CMakeFiles
CMakeScripts
Makefile
cmake_install.cmake
install_manifest.txt
*.ninja
.ninja_log
.ninja_deps
.cmake/
compile_commands.json


# Compiled Object files
*.slo
*.lo
*.o
*.obj

# Precompiled Headers
*.gch
*.pch

# Compiled Dynamic libraries
*.so
*.dylib
*.dll

# Fortran module files
*.mod

# Compiled Static libraries
*.lai
*.la
*.a
*.lib

# Executables
*.exe
*.out
*.app

# Visual Studio files
*.sln
*.vcxproj
*.vcxproj.filters
*.vcxproj.user
.vs/
x64/
src/vsg/vsg.dir/

#xcode
DerivedData/
*.build
*.xcodeproj

#glslang automatically generated files
include/glslang
src/vsg/CHANGES.md

