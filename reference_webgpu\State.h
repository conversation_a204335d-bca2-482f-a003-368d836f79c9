// include/vsg_webgpu/State.h
#pragma once
#include <stack>
#include <vector>
#include <vsg/core/Mask.h>
#include <vsg/maths/mat4.h>
#include <vsg/state/StateCommand.h>
#include <vsg/state/StateGroup.h>
#include <vsg/vk/State.h>
#include <vsg_webgpu/CommandBuffer.h>
#include <vsg_webgpu/Device.h>

namespace vsg_webgpu
{
    // forward declarations
    class RenderPassEncoder;

    /// WebGPU状态管理，对应VSG的State类
    /// 提供与VSG State相同的接口，但底层使用WebGPU实现
    class VSG_DECLSPEC State : public vsg::Inherit<vsg::Object, State>
    {
    public:
        State(Device* device, const vsg::Slots& maxSlots = {});

        // 对应VSG State的核心接口
        using StateCommandStack = vsg::StateStack<vsg::StateCommand>;
        using StateStacks = std::vector<StateCommandStack>;

        vsg::ref_ptr<CommandBuffer> _commandBuffer;

        // 视锥体管理
        vsg::Frustum _frustumUnit;
        vsg::Frustum _frustumProjected;
        using FrustumStack = std::stack<vsg::Frustum>;
        FrustumStack _frustumStack;

        bool dirty = true;

        // LOD缩放的继承视图
        bool inheritViewForLODScaling = false;
        vsg::dmat4 inheritedProjectionMatrix;
        vsg::dmat4 inheritedViewMatrix;
        vsg::dmat4 inheritedViewTransform;

        // 状态槽管理
        vsg::Slots maxSlots;
        uint32_t activeMaxStateSlot = 0;
        StateStacks stateStacks;

        // 视口状态提示
        uint32_t viewportStateHint = 0;

        // 矩阵栈
        vsg::MatrixStack projectionMatrixStack{0};
        vsg::MatrixStack modelviewMatrixStack{64};

        // 状态管理方法 - 对应VSG State接口
        void reserve(const vsg::Slots& in_maxSlots);
        void connect(vsg::ref_ptr<CommandBuffer> commandBuffer);
        void reset();

        // 状态栈操作
        template<typename Iterator>
        void push(Iterator begin, Iterator end)
        {
            for (auto itr = begin; itr != end; ++itr)
            {
                auto& stateCommand = *itr;
                stateStacks[stateCommand->slot].push(stateCommand);
            }
            activeMaxStateSlot = maxSlots.max();
        }

        template<typename Iterator>
        void pop(Iterator begin, Iterator end)
        {
            for (auto itr = begin; itr != end; ++itr)
            {
                auto& stateCommand = *itr;
                stateStacks[stateCommand->slot].pop();
            }
        }

        void pushView(vsg::ref_ptr<vsg::StateCommand> command);
        void popView(vsg::ref_ptr<vsg::StateCommand> command);

        // 状态录制
        void record();

        // 状态应用
        void apply(const vsg::StateCommand* command);
        void apply(const vsg::StateGroup* stateGroup);

        // WebGPU特定接口
        void setRenderPassEncoder(wgpu::RenderPassEncoder encoder)
        {
            _renderPassEncoder = encoder;
        }
        wgpu::RenderPassEncoder getRenderPassEncoder() const
        {
            return _renderPassEncoder;
        }

        Device* getDevice() const { return _device.get(); }

        // 当前绑定的资源状态
        struct BoundResources
        {
            wgpu::RenderPipeline renderPipeline;
            wgpu::ComputePipeline computePipeline;
            std::array<wgpu::BindGroup, 4> bindGroups;
            std::array<wgpu::Buffer, 8> vertexBuffers;
            std::array<uint64_t, 8> vertexBufferOffsets;
            wgpu::Buffer indexBuffer;
            uint64_t indexBufferOffset = 0;
            wgpu::IndexFormat indexFormat = wgpu::IndexFormat::Uint16;
        };

        BoundResources& getBoundResources() { return _boundResources; }

        // 矩阵操作 - 兼容性接口
        void pushProjectionMatrix(const vsg::mat4& matrix);
        void popProjectionMatrix();
        void pushViewMatrix(const vsg::mat4& matrix);
        void popViewMatrix();
        void pushModelMatrix(const vsg::mat4& matrix);
        void popModelMatrix();

        vsg::mat4 getProjectionMatrix() const;
        vsg::mat4 getViewMatrix() const;
        vsg::mat4 getModelMatrix() const;
        vsg::mat4 getMVPMatrix() const;

        // 继承视图设置
        void setInheritedViewProjectionAndViewMatrix(const vsg::dmat4& projMatrix, const vsg::dmat4& viewMatrix);

    private:
        vsg::ref_ptr<Device> _device;
        wgpu::RenderPassEncoder _renderPassEncoder;
        wgpu::ComputePassEncoder _computePassEncoder;

        BoundResources _boundResources;

        // 内部状态管理
        void _dirtyStateStacks();
    };

    VSG_type_name(vsg_webgpu::State);

    // WebGPU图形管线绑定命令[[1]](#__1)
    class BindGraphicsPipeline : public vsg::Inherit<vsg::StateCommand, BindGraphicsPipeline>
    {
    public:
        BindGraphicsPipeline();

        // 管线配置
        struct PipelineConfig
        {
            // 顶点状态
            std::vector<wgpu::VertexAttribute> vertexAttributes;
            std::vector<wgpu::VertexBufferLayout> vertexLayouts;

            // 着色器
            std::string vertexShader;
            std::string fragmentShader;

            // 渲染状态
            wgpu::PrimitiveTopology topology = wgpu::PrimitiveTopology::TriangleList;
            wgpu::CullMode cullMode = wgpu::CullMode::Back;
            wgpu::FrontFace frontFace = wgpu::FrontFace::CCW;

            // 深度测试
            bool depthTest = true;
            bool depthWrite = true;
            wgpu::CompareFunction depthCompare = wgpu::CompareFunction::Less;

            // 混合
            bool blending = false;
            wgpu::BlendState blendState{};
        };

        void setPipelineConfig(const PipelineConfig& config) { _config = config; }

        void record(State& state) const;

    private:
        PipelineConfig _config;
        mutable size_t _pipelineHash = 0;

        wgpu::RenderPipeline createPipeline(Device* device) const;
        size_t computeHash() const;
    };

    // 实现
    void BindGraphicsPipeline::record(State& state) const
    {
        auto device = state.getDevice();
        auto& cache = device->getResourceCache();

        if (_pipelineHash == 0)
        {
            _pipelineHash = computeHash();
        }

        auto pipeline = cache.getOrCreatePipeline(_pipelineHash,
                                                  [this, device]() { return createPipeline(device); });

        auto encoder = state.getRenderPassEncoder();
        encoder.SetPipeline(pipeline);

        state.getBoundResources().pipeline = pipeline;
    }

    wgpu::RenderPipeline BindGraphicsPipeline::createPipeline(Device* device) const
    {
        auto wgpuDevice = device->getDevice();

        // 创建着色器模块
        wgpu::ShaderModuleWGSLDescriptor wgslDesc{};

        // 顶点着色器
        wgslDesc.code = _config.vertexShader.c_str();
        wgpu::ShaderModuleDescriptor vertexDesc{};
        vertexDesc.nextInChain = &wgslDesc;
        auto vertexModule = wgpuDevice.CreateShaderModule(&vertexDesc);

        // 片段着色器
        wgslDesc.code = _config.fragmentShader.c_str();
        wgpu::ShaderModuleDescriptor fragmentDesc{};
        fragmentDesc.nextInChain = &wgslDesc;
        auto fragmentModule = wgpuDevice.CreateShaderModule(&fragmentDesc);

        // 管线布局
        wgpu::PipelineLayoutDescriptor layoutDesc{};
        auto pipelineLayout = wgpuDevice.CreatePipelineLayout(&layoutDesc);

        // 渲染管线
        wgpu::RenderPipelineDescriptor pipelineDesc{};
        pipelineDesc.layout = pipelineLayout;

        // 顶点阶段
        pipelineDesc.vertex.module = vertexModule;
        pipelineDesc.vertex.entryPoint = "vs_main";
        pipelineDesc.vertex.bufferCount = _config.vertexLayouts.size();
        pipelineDesc.vertex.buffers = _config.vertexLayouts.data();

        // 图元
        pipelineDesc.primitive.topology = _config.topology;
        pipelineDesc.primitive.cullMode = _config.cullMode;
        pipelineDesc.primitive.frontFace = _config.frontFace;

        // 深度模板
        if (_config.depthTest)
        {
            wgpu::DepthStencilState depthStencil{};
            depthStencil.format = wgpu::TextureFormat::Depth32Float;
            depthStencil.depthWriteEnabled = _config.depthWrite;
            depthStencil.depthCompare = _config.depthCompare;
            pipelineDesc.depthStencil = &depthStencil;
        }

        // 片段阶段
        wgpu::FragmentState fragment{};
        fragment.module = fragmentModule;
        fragment.entryPoint = "fs_main";

        wgpu::ColorTargetState colorTarget{};
        colorTarget.format = device->getSwapChainFormat();
        colorTarget.blend = _config.blending ? &_config.blendState : nullptr;
        colorTarget.writeMask = wgpu::ColorWriteMask::All;

        fragment.targetCount = 1;
        fragment.targets = &colorTarget;
        pipelineDesc.fragment = &fragment;

        // 多重采样
        pipelineDesc.multisample.count = 1;
        pipelineDesc.multisample.mask = ~0u;

        return wgpuDevice.CreateRenderPipeline(&pipelineDesc);
    }

} // namespace vsg_webgpu
