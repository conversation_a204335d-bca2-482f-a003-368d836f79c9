// include/rocky_webgpu/State.h
#pragma once
#include <rocky_webgpu/Device.h>
#include <stack>
#include <vsg/maths/mat4.h>
#include <vsg/state/StateCommand.h>
#include <vsg/state/StateGroup.h>

namespace rocky_webgpu
{

    // WebGPU状态管理
    class State : public vsg::Inherit<vsg::Object, State>
    {
    public:
        State(Device* device);

        // 矩阵栈
        void pushProjectionMatrix(const vsg::mat4& matrix);
        void popProjectionMatrix();
        void pushViewMatrix(const vsg::mat4& matrix);
        void popViewMatrix();
        void pushModelMatrix(const vsg::mat4& matrix);
        void popModelMatrix();

        vsg::mat4 getProjectionMatrix() const;
        vsg::mat4 getViewMatrix() const;
        vsg::mat4 getModelMatrix() const;
        vsg::mat4 getMVPMatrix() const;

        // 状态应用
        void apply(const vsg::StateCommand* command);
        void apply(const vsg::StateGroup* stateGroup);

        // WebGPU特定
        void setRenderPassEncoder(wgpu::RenderPassEncoder encoder)
        {
            _renderPassEncoder = encoder;
        }
        wgpu::RenderPassEncoder getRenderPassEncoder() const
        {
            return _renderPassEncoder;
        }

        Device* getDevice() const { return _device.get(); }

        // 当前绑定的资源
        struct BoundResources
        {
            wgpu::RenderPipeline pipeline;
            std::array<wgpu::BindGroup, 4> bindGroups;
            std::array<wgpu::Buffer, 8> vertexBuffers;
            wgpu::Buffer indexBuffer;
            wgpu::IndexFormat indexFormat = wgpu::IndexFormat::Uint16;
        };

        BoundResources& getBoundResources() { return _boundResources; }

    private:
        vsg::ref_ptr<Device> _device;
        wgpu::RenderPassEncoder _renderPassEncoder;

        // 矩阵栈
        std::stack<vsg::mat4> _projectionStack;
        std::stack<vsg::mat4> _viewStack;
        std::stack<vsg::mat4> _modelStack;

        BoundResources _boundResources;
    };

    // WebGPU图形管线绑定命令[[1]](#__1)
    class BindGraphicsPipeline : public vsg::Inherit<vsg::StateCommand, BindGraphicsPipeline>
    {
    public:
        BindGraphicsPipeline();

        // 管线配置
        struct PipelineConfig
        {
            // 顶点状态
            std::vector<wgpu::VertexAttribute> vertexAttributes;
            std::vector<wgpu::VertexBufferLayout> vertexLayouts;

            // 着色器
            std::string vertexShader;
            std::string fragmentShader;

            // 渲染状态
            wgpu::PrimitiveTopology topology = wgpu::PrimitiveTopology::TriangleList;
            wgpu::CullMode cullMode = wgpu::CullMode::Back;
            wgpu::FrontFace frontFace = wgpu::FrontFace::CCW;

            // 深度测试
            bool depthTest = true;
            bool depthWrite = true;
            wgpu::CompareFunction depthCompare = wgpu::CompareFunction::Less;

            // 混合
            bool blending = false;
            wgpu::BlendState blendState{};
        };

        void setPipelineConfig(const PipelineConfig& config) { _config = config; }

        void record(State& state) const;

    private:
        PipelineConfig _config;
        mutable size_t _pipelineHash = 0;

        wgpu::RenderPipeline createPipeline(Device* device) const;
        size_t computeHash() const;
    };

    // 实现
    void BindGraphicsPipeline::record(State& state) const
    {
        auto device = state.getDevice();
        auto& cache = device->getResourceCache();

        if (_pipelineHash == 0)
        {
            _pipelineHash = computeHash();
        }

        auto pipeline = cache.getOrCreatePipeline(_pipelineHash,
                                                  [this, device]() { return createPipeline(device); });

        auto encoder = state.getRenderPassEncoder();
        encoder.SetPipeline(pipeline);

        state.getBoundResources().pipeline = pipeline;
    }

    wgpu::RenderPipeline BindGraphicsPipeline::createPipeline(Device* device) const
    {
        auto wgpuDevice = device->getDevice();

        // 创建着色器模块
        wgpu::ShaderModuleWGSLDescriptor wgslDesc{};

        // 顶点着色器
        wgslDesc.code = _config.vertexShader.c_str();
        wgpu::ShaderModuleDescriptor vertexDesc{};
        vertexDesc.nextInChain = &wgslDesc;
        auto vertexModule = wgpuDevice.CreateShaderModule(&vertexDesc);

        // 片段着色器
        wgslDesc.code = _config.fragmentShader.c_str();
        wgpu::ShaderModuleDescriptor fragmentDesc{};
        fragmentDesc.nextInChain = &wgslDesc;
        auto fragmentModule = wgpuDevice.CreateShaderModule(&fragmentDesc);

        // 管线布局
        wgpu::PipelineLayoutDescriptor layoutDesc{};
        auto pipelineLayout = wgpuDevice.CreatePipelineLayout(&layoutDesc);

        // 渲染管线
        wgpu::RenderPipelineDescriptor pipelineDesc{};
        pipelineDesc.layout = pipelineLayout;

        // 顶点阶段
        pipelineDesc.vertex.module = vertexModule;
        pipelineDesc.vertex.entryPoint = "vs_main";
        pipelineDesc.vertex.bufferCount = _config.vertexLayouts.size();
        pipelineDesc.vertex.buffers = _config.vertexLayouts.data();

        // 图元
        pipelineDesc.primitive.topology = _config.topology;
        pipelineDesc.primitive.cullMode = _config.cullMode;
        pipelineDesc.primitive.frontFace = _config.frontFace;

        // 深度模板
        if (_config.depthTest)
        {
            wgpu::DepthStencilState depthStencil{};
            depthStencil.format = wgpu::TextureFormat::Depth32Float;
            depthStencil.depthWriteEnabled = _config.depthWrite;
            depthStencil.depthCompare = _config.depthCompare;
            pipelineDesc.depthStencil = &depthStencil;
        }

        // 片段阶段
        wgpu::FragmentState fragment{};
        fragment.module = fragmentModule;
        fragment.entryPoint = "fs_main";

        wgpu::ColorTargetState colorTarget{};
        colorTarget.format = device->getSwapChainFormat();
        colorTarget.blend = _config.blending ? &_config.blendState : nullptr;
        colorTarget.writeMask = wgpu::ColorWriteMask::All;

        fragment.targetCount = 1;
        fragment.targets = &colorTarget;
        pipelineDesc.fragment = &fragment;

        // 多重采样
        pipelineDesc.multisample.count = 1;
        pipelineDesc.multisample.mask = ~0u;

        return wgpuDevice.CreateRenderPipeline(&pipelineDesc);
    }

} // namespace rocky_webgpu
