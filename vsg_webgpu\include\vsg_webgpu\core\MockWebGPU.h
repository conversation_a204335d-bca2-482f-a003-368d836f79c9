#pragma once

/* <editor-fold desc="MIT License">

Copyright(c) 2024 VSG WebGPU Extension

Permission is hereby granted, free of charge, to any person obtaining a copy of this software and associated documentation files (the "Software"), to deal in the Software without restriction, including without limitation the rights to use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of the Software, and to permit persons to whom the Software is furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.

</editor-fold> */

// Mock WebGPU implementation for testing without Dawn
#include <cstdint>
#include <functional>
#include <string>
#include <vector>

namespace wgpu {

// Mock enums
enum class TextureFormat : uint32_t {
    BGRA8Unorm = 0,
    RGBA8Unorm = 1,
    Depth32Float = 2,
};

enum class BufferUsage : uint32_t {
    Vertex = 1,
    Index = 2,
    Uniform = 4,
    Storage = 8,
    CopyDst = 16,
    CopySrc = 32,
};

enum class TextureUsage : uint32_t {
    RenderAttachment = 1,
    TextureBinding = 2,
    CopyDst = 4,
    CopySrc = 8,
};

enum class ShaderStage : uint32_t {
    Vertex = 1,
    Fragment = 2,
    Compute = 4,
};

enum class PrimitiveTopology : uint32_t {
    TriangleList = 0,
    TriangleStrip = 1,
    LineList = 2,
    LineStrip = 3,
    PointList = 4,
};

enum class IndexFormat : uint32_t {
    Uint16 = 0,
    Uint32 = 1,
    Undefined = 2,
};

enum class CompareFunction : uint32_t {
    Never = 0,
    Less = 1,
    Equal = 2,
    LessEqual = 3,
    Greater = 4,
    NotEqual = 5,
    GreaterEqual = 6,
    Always = 7,
};

enum class CullMode : uint32_t {
    None = 0,
    Front = 1,
    Back = 2,
};

enum class FrontFace : uint32_t {
    CCW = 0,
    CW = 1,
};

enum class BlendOperation : uint32_t {
    Add = 0,
    Subtract = 1,
    ReverseSubtract = 2,
    Min = 3,
    Max = 4,
};

enum class BlendFactor : uint32_t {
    Zero = 0,
    One = 1,
    Src = 2,
    OneMinusSrc = 3,
    SrcAlpha = 4,
    OneMinusSrcAlpha = 5,
    Dst = 6,
    OneMinusDst = 7,
    DstAlpha = 8,
    OneMinusDstAlpha = 9,
};

// Mock classes
class Instance {
public:
    Instance() = default;
    static Instance Create() { return Instance{}; }
};

class Adapter {
public:
    Adapter() = default;
};

class Device {
public:
    Device() = default;
};

class Queue {
public:
    Queue() = default;
};

class Surface {
public:
    Surface() = default;
};

class SwapChain {
public:
    SwapChain() = default;
};

class CommandEncoder {
public:
    CommandEncoder() = default;
};

class CommandBuffer {
public:
    CommandBuffer() = default;
};

class RenderPassEncoder {
public:
    RenderPassEncoder() = default;
};

class ComputePassEncoder {
public:
    ComputePassEncoder() = default;
};

class Buffer {
public:
    Buffer() = default;
};

class Texture {
public:
    Texture() = default;
};

class TextureView {
public:
    TextureView() = default;
};

class Sampler {
public:
    Sampler() = default;
};

class ShaderModule {
public:
    ShaderModule() = default;
};

class RenderPipeline {
public:
    RenderPipeline() = default;
};

class ComputePipeline {
public:
    ComputePipeline() = default;
};

class BindGroup {
public:
    BindGroup() = default;
};

class BindGroupLayout {
public:
    BindGroupLayout() = default;
};

class PipelineLayout {
public:
    PipelineLayout() = default;
};

// Mock descriptors
struct InstanceDescriptor {};
struct RequestAdapterOptions {};
struct DeviceDescriptor {};
struct SwapChainDescriptor {};
struct BufferDescriptor {};
struct TextureDescriptor {};
struct SamplerDescriptor {};
struct ShaderModuleDescriptor {};
struct RenderPipelineDescriptor {};
struct ComputePipelineDescriptor {};
struct BindGroupDescriptor {};
struct BindGroupLayoutDescriptor {};
struct PipelineLayoutDescriptor {};
struct RenderPassDescriptor {};
struct ComputePassDescriptor {};

} // namespace wgpu
