#pragma once

/* <editor-fold desc="MIT License">

Copyright(c) 2018 <PERSON>

Permission is hereby granted, free of charge, to any person obtaining a copy of this software and associated documentation files (the "Software"), to deal in the Software without restriction, including without limitation the rights to use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of the Software, and to permit persons to whom the Software is furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.

</editor-fold> */

#include <vsg/ui/KeyEvent.h>
#include <vsg/ui/PointerEvent.h>
#include <vsg/ui/ScrollWheelEvent.h>

namespace vsg
{

    /// ShiftEventTime is a visitor for modifying the UIEvent::time values by a specified delta.
    /// Used by PlayEvents to replace recorded events with a different time point.
    class VSG_DECLSPEC ShiftEventTime : public vsg::Inherit<vsg::Visitor, ShiftEventTime>
    {
    public:
        explicit ShiftEventTime(vsg::clock::time_point::duration in_delta);

        vsg::clock::time_point::duration delta;

        void apply(vsg::Object& object) override;
        void apply(vsg::UIEvent& event) override;
    };
    VSG_type_name(vsg::ShiftEventTime);

} // namespace vsg
