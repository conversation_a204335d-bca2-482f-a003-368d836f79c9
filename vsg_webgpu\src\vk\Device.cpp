/* <editor-fold desc="MIT License">

Copyright(c) 2024 VSG WebGPU Extension

Permission is hereby granted, free of charge, to any person obtaining a copy of this software and associated documentation files (the "Software"), to deal in the Software without restriction, including without limitation the rights to use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of the Software, and to permit persons to whom the Software is furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.

</editor-fold> */

#include <vsg_webgpu/vk/Device.h>
#include <vsg_webgpu/vk/Queue.h>
#include <vsg_webgpu/vk/CommandPool.h>
#include <vsg_webgpu/core/WebGPUHeaders.h>

using namespace vsg_webgpu;

Device::Device() : 
    deviceID(0),
    _resourceCache(this)
{
    VSG_WEBGPU_LOG_DEBUG("Device::Device()");
}

Device::~Device()
{
    VSG_WEBGPU_LOG_DEBUG("Device::~Device()");
    
    // 清理资源
    _resourceCache.clear();
    _queues.clear();
    _commandPools.clear();
    
#if !VSG_WEBGPU_USE_MOCK
    // 释放WebGPU资源
    if (_depthTextureView) _depthTextureView = nullptr;
    if (_depthTexture) _depthTexture = nullptr;
    if (_swapChain) _swapChain = nullptr;
    if (_surface) _surface = nullptr;
    if (_device) _device = nullptr;
    if (_adapter) _adapter = nullptr;
    if (_instance) _instance = nullptr;
#endif
}

bool Device::initialize(const vsg::DeviceFeatures* deviceFeatures)
{
    VSG_WEBGPU_LOG_INFO("Initializing WebGPU Device...");
    
    if (!_initializeInstance())
    {
        VSG_WEBGPU_LOG_ERROR("Failed to initialize WebGPU instance");
        return false;
    }
    
    if (!_requestAdapter())
    {
        VSG_WEBGPU_LOG_ERROR("Failed to request WebGPU adapter");
        return false;
    }
    
    if (!_requestDevice(deviceFeatures))
    {
        VSG_WEBGPU_LOG_ERROR("Failed to request WebGPU device");
        return false;
    }
    
    _setupFeatures();
    
    // 创建默认队列
    auto queue = Queue::create(this, _device.GetQueue(), 0, 0);
    _queues.push_back(queue);
    
    // 创建默认命令池
    auto commandPool = CommandPool::create(this, 0);
    _commandPools.push_back(commandPool);
    
    VSG_WEBGPU_LOG_INFO("WebGPU Device initialized successfully");
    return true;
}

bool Device::initialize(WGPUSurface surface, const vsg::DeviceFeatures* deviceFeatures)
{
    _surface = surface;
    
    if (!initialize(deviceFeatures))
    {
        return false;
    }
    
    _setupSwapChain();
    return true;
}

vsg::ref_ptr<Queue> Device::getQueue(uint32_t queueFamilyIndex, uint32_t queueIndex)
{
    // WebGPU通常只有一个队列
    if (!_queues.empty())
    {
        return _queues[0];
    }
    return {};
}

vsg::ref_ptr<CommandPool> Device::getCommandPool(uint32_t queueFamilyIndex)
{
    // WebGPU通常只有一个命令池
    if (!_commandPools.empty())
    {
        return _commandPools[0];
    }
    return {};
}

void Device::resizeSwapChain(uint32_t width, uint32_t height)
{
#if !VSG_WEBGPU_USE_MOCK
    if (_swapChain && _surface)
    {
        // 重新创建交换链
        WGPUSwapChainDescriptor swapChainDesc = {};
        swapChainDesc.usage = WGPUTextureUsage_RenderAttachment;
        swapChainDesc.format = _swapChainFormat;
        swapChainDesc.width = width;
        swapChainDesc.height = height;
        swapChainDesc.presentMode = WGPUPresentMode_Fifo;
        
        _swapChain = _device.CreateSwapChain(_surface, &swapChainDesc);
        
        // 重新创建深度缓冲
        createDepthBuffer(width, height);
    }
#endif
}

WGPUTextureView Device::getCurrentTextureView()
{
#if !VSG_WEBGPU_USE_MOCK
    if (_swapChain)
    {
        return _swapChain.GetCurrentTextureView();
    }
#endif
    return {};
}

WGPUCommandEncoder Device::createCommandEncoder()
{
#if !VSG_WEBGPU_USE_MOCK
    WGPUCommandEncoderDescriptor desc = {};
    desc.label = "VSG WebGPU Command Encoder";
    return _device.CreateCommandEncoder(&desc);
#else
    return {};
#endif
}

void Device::submitCommandBuffer(WGPUCommandBuffer commands)
{
#if !VSG_WEBGPU_USE_MOCK
    if (!_queues.empty())
    {
        auto queue = _queues[0];
        queue->getQueue().Submit(1, &commands);
    }
#endif
}

void Device::createDepthBuffer(uint32_t width, uint32_t height)
{
#if !VSG_WEBGPU_USE_MOCK
    // 创建深度纹理
    WGPUTextureDescriptor depthTextureDesc = {};
    depthTextureDesc.label = "Depth Texture";
    depthTextureDesc.size = {width, height, 1};
    depthTextureDesc.mipLevelCount = 1;
    depthTextureDesc.sampleCount = 1;
    depthTextureDesc.dimension = WGPUTextureDimension_2D;
    depthTextureDesc.format = WGPUTextureFormat_Depth32Float;
    depthTextureDesc.usage = WGPUTextureUsage_RenderAttachment;
    
    _depthTexture = _device.CreateTexture(&depthTextureDesc);
    
    // 创建深度纹理视图
    WGPUTextureViewDescriptor depthViewDesc = {};
    depthViewDesc.label = "Depth Texture View";
    depthViewDesc.format = WGPUTextureFormat_Depth32Float;
    depthViewDesc.dimension = WGPUTextureViewDimension_2D;
    depthViewDesc.baseMipLevel = 0;
    depthViewDesc.mipLevelCount = 1;
    depthViewDesc.baseArrayLayer = 0;
    depthViewDesc.arrayLayerCount = 1;
    depthViewDesc.aspect = WGPUTextureAspect_DepthOnly;
    
    _depthTextureView = _depthTexture.CreateView(&depthViewDesc);
#endif
}

void Device::setErrorCallback(const std::function<void(const std::string&)>& callback)
{
    _errorCallback = callback;
}

void Device::logError(const std::string& message)
{
    VSG_WEBGPU_LOG_ERROR(message);
    if (_errorCallback)
    {
        _errorCallback(message);
    }
}

bool Device::supportsFeature(const std::string& feature) const
{
    auto it = std::find(_supportedFeatures.begin(), _supportedFeatures.end(), feature);
    return it != _supportedFeatures.end();
}

void Device::waitIdle()
{
    // WebGPU没有直接的waitIdle方法，这里是空实现
    // 在实际应用中，可能需要等待所有提交的命令完成
}

bool Device::_initializeInstance()
{
#if VSG_WEBGPU_USE_MOCK
    return true;
#else
    WGPUInstanceDescriptor instanceDesc = {};
    _instance = wgpu::CreateInstance(&instanceDesc);
    return _instance != nullptr;
#endif
}

bool Device::_requestAdapter()
{
#if VSG_WEBGPU_USE_MOCK
    return true;
#else
    WGPURequestAdapterOptions adapterOpts = {};
    adapterOpts.compatibleSurface = _surface;
    
    _adapter = _instance.RequestAdapter(&adapterOpts);
    return _adapter != nullptr;
#endif
}

bool Device::_requestDevice(const vsg::DeviceFeatures* deviceFeatures)
{
#if VSG_WEBGPU_USE_MOCK
    return true;
#else
    WGPUDeviceDescriptor deviceDesc = {};
    deviceDesc.label = "VSG WebGPU Device";
    
    // 设置错误回调
    deviceDesc.deviceLostCallback = _deviceLostCallback;
    deviceDesc.deviceLostUserdata = this;
    
    _device = _adapter.RequestDevice(&deviceDesc);
    if (_device)
    {
        _device.SetUncapturedErrorCallback(_deviceErrorCallback, this);
        return true;
    }
    return false;
#endif
}

void Device::_setupFeatures()
{
    // 设置支持的功能列表
    _supportedFeatures = {
        "basic_rendering",
        "compute_shaders",
        "texture_binding",
        "vertex_buffers",
        "index_buffers",
        "uniform_buffers"
    };
}

void Device::_setupSwapChain()
{
#if !VSG_WEBGPU_USE_MOCK
    if (_surface && _adapter)
    {
        _swapChainFormat = _surface.GetPreferredFormat(_adapter);
        
        WGPUSwapChainDescriptor swapChainDesc = {};
        swapChainDesc.usage = WGPUTextureUsage_RenderAttachment;
        swapChainDesc.format = _swapChainFormat;
        swapChainDesc.width = 1920;  // 默认尺寸
        swapChainDesc.height = 1080;
        swapChainDesc.presentMode = WGPUPresentMode_Fifo;
        
        _swapChain = _device.CreateSwapChain(_surface, &swapChainDesc);
    }
#endif
}

void Device::_deviceErrorCallback(int type, const char* message, void* userdata)
{
    Device* device = static_cast<Device*>(userdata);
    std::string errorMsg = "WebGPU Device Error: ";
    errorMsg += message;
    device->logError(errorMsg);
}

void Device::_deviceLostCallback(int reason, const char* message, void* userdata)
{
    Device* device = static_cast<Device*>(userdata);
    std::string errorMsg = "WebGPU Device Lost: ";
    errorMsg += message;
    device->logError(errorMsg);
}
