@echo off
chcp 65001 >nul
setlocal enabledelayedexpansion

echo ========================================
echo VSG渲染引擎抽象层自动构建测试
echo ========================================
echo.

REM 设置构建参数
set BUILD_TYPE=Release
set BUILD_DIR=build_new
set CLEAN_BUILD=0

REM 解析命令行参数
:parse_args
if "%1"=="" goto :start_build
if "%1"=="--debug" (
    set BUILD_TYPE=Debug
    shift
    goto :parse_args
)
if "%1"=="--clean" (
    set CLEAN_BUILD=1
    shift
    goto :parse_args
)
if "%1"=="--help" (
    goto :show_help
)
shift
goto :parse_args

:show_help
echo 用法: build_and_test.bat [选项]
echo.
echo 选项:
echo   --debug     构建Debug版本 (默认: Release)
echo   --clean     清理后重新构建
echo   --help      显示此帮助信息
echo.
goto :end

:start_build

echo 检查构建环境...

REM 检查CMake
cmake --version >nul 2>&1
if errorlevel 1 (
    echo 错误: 未找到CMake
    goto :error
)

echo ✓ CMake已安装

REM 检查编译器
where cl >nul 2>&1
if errorlevel 1 (
    echo 错误: 未找到MSVC编译器
    echo 请在Visual Studio Developer Command Prompt中运行此脚本
    goto :error
)

echo ✓ MSVC编译器已找到

echo.
echo 构建配置:
echo   构建类型: %BUILD_TYPE%
echo   构建目录: %BUILD_DIR%
echo.

REM 清理构建目录
if %CLEAN_BUILD%==1 (
    echo 清理构建目录...
    if exist %BUILD_DIR% rmdir /s /q %BUILD_DIR%
    echo ✓ 清理完成
    echo.
)

REM 创建构建目录
if not exist %BUILD_DIR% mkdir %BUILD_DIR%
cd %BUILD_DIR%

echo 步骤 1/5: 配置CMake...
cmake .. ^
    -G "Visual Studio 17 2022" ^
    -A x64 ^
    -DCMAKE_BUILD_TYPE=%BUILD_TYPE% ^
    -DVSG_ABSTRACTION_BUILD_VSG_BACKEND=OFF ^
    -DVSG_ABSTRACTION_BUILD_WEBGPU_BACKEND=OFF ^
    -DVSG_ABSTRACTION_BUILD_OPENGL_BACKEND=OFF ^
    -DVSG_ABSTRACTION_BUILD_MOCK_BACKEND=ON ^
    -DVSG_ABSTRACTION_BUILD_TESTS=ON ^
    -DVSG_ABSTRACTION_BUILD_EXAMPLES=ON ^
    -DVSG_ABSTRACTION_BUILD_SHARED_LIBS=OFF

if errorlevel 1 (
    echo ✗ CMake配置失败
    goto :error
)

echo ✓ CMake配置成功
echo.

echo 步骤 2/5: 编译项目...
cmake --build . --config %BUILD_TYPE% --parallel

if errorlevel 1 (
    echo ✗ 编译失败
    goto :error
)

echo ✓ 编译成功
echo.

echo 步骤 3/5: 运行基础测试...
if exist tests\%BUILD_TYPE%\test_basic.exe (
    echo 运行基础功能测试...
    tests\%BUILD_TYPE%\test_basic.exe
    if errorlevel 1 (
        echo ✗ 基础测试失败
        set TEST_FAILED=1
    ) else (
        echo ✓ 基础测试通过
    )
) else (
    echo ⚠ 基础测试程序未找到
)

echo.

echo 步骤 4/5: 运行示例程序...
if exist examples\%BUILD_TYPE%\basic_example.exe (
    echo 运行基础示例...
    examples\%BUILD_TYPE%\basic_example.exe
    if errorlevel 1 (
        echo ✗ 基础示例运行失败
    ) else (
        echo ✓ 基础示例运行成功
    )
) else (
    echo ⚠ 基础示例程序未找到
)

if exist examples\%BUILD_TYPE%\vsg_rendering_example.exe (
    echo 运行VSG渲染示例...
    examples\%BUILD_TYPE%\vsg_rendering_example.exe
    if errorlevel 1 (
        echo ⚠ VSG渲染示例运行失败（可能是VSG不可用）
    ) else (
        echo ✓ VSG渲染示例运行成功
    )
) else (
    echo ⚠ VSG渲染示例程序未找到
)

echo.

echo 步骤 5/5: 运行完整测试套件...
ctest --output-on-failure --build-config %BUILD_TYPE%

if errorlevel 1 (
    echo ⚠ 部分测试失败
) else (
    echo ✓ 所有测试通过
)

cd ..

echo.
echo ========================================
echo 构建和测试完成!
echo ========================================
echo.
echo 构建输出:
echo   库文件: %BUILD_DIR%\lib\%BUILD_TYPE%\
echo   可执行文件: %BUILD_DIR%\bin\%BUILD_TYPE%\
echo   测试程序: %BUILD_DIR%\tests\%BUILD_TYPE%\
echo   示例程序: %BUILD_DIR%\examples\%BUILD_TYPE%\
echo.

echo 可用的程序:
if exist %BUILD_DIR%\examples\%BUILD_TYPE%\basic_example.exe (
    echo   %BUILD_DIR%\examples\%BUILD_TYPE%\basic_example.exe
)
if exist %BUILD_DIR%\examples\%BUILD_TYPE%\vsg_rendering_example.exe (
    echo   %BUILD_DIR%\examples\%BUILD_TYPE%\vsg_rendering_example.exe
)
if exist %BUILD_DIR%\tests\%BUILD_TYPE%\test_basic.exe (
    echo   %BUILD_DIR%\tests\%BUILD_TYPE%\test_basic.exe
)

echo.
echo 快速运行命令:
echo   cd %BUILD_DIR%\examples\%BUILD_TYPE%
echo   basic_example.exe
echo.

echo 🎉 VSG渲染引擎抽象层构建测试成功完成！
goto :end

:error
echo.
echo ========================================
echo 构建或测试失败!
echo ========================================
echo.
echo 请检查上面的错误信息并解决问题后重试。
echo.
if exist %BUILD_DIR% cd ..
exit /b 1

:end
endlocal
