#pragma once

/* <editor-fold desc="MIT License">

Copyright(c) 2024 VSG WebGPU Extension

Permission is hereby granted, free of charge, to any person obtaining a copy of this software and associated documentation files (the "Software"), to deal in the Software without restriction, including without limitation the rights to use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of the Software, and to permit persons to whom the Software is furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.

</editor-fold> */

// Core headers
#include <vsg_webgpu/core/Export.h>
#include <vsg_webgpu/core/WebGPUHeaders.h>

// Device and resource management
#include <vsg_webgpu/vk/Device.h>
#include <vsg_webgpu/vk/Queue.h>
#include <vsg_webgpu/vk/CommandBuffer.h>
#include <vsg_webgpu/vk/CommandPool.h>
#include <vsg_webgpu/vk/Context.h>

// State management
#include <vsg_webgpu/vk/State.h>

// Pipeline and shaders
#include <vsg_webgpu/state/GraphicsPipeline.h>
#include <vsg_webgpu/state/ComputePipeline.h>
#include <vsg_webgpu/state/ShaderModule.h>
#include <vsg_webgpu/state/Buffer.h>
#include <vsg_webgpu/state/Image.h>
#include <vsg_webgpu/state/Sampler.h>
#include <vsg_webgpu/state/DescriptorSet.h>

// Rendering
#include <vsg_webgpu/app/RenderGraph.h>
#include <vsg_webgpu/app/RecordTraversal.h>
#include <vsg_webgpu/app/Window.h>

// Commands
#include <vsg_webgpu/commands/Draw.h>
#include <vsg_webgpu/commands/DrawIndexed.h>
#include <vsg_webgpu/commands/Dispatch.h>
#include <vsg_webgpu/commands/BindDescriptorSet.h>
#include <vsg_webgpu/commands/BindVertexBuffers.h>
#include <vsg_webgpu/commands/BindIndexBuffer.h>

// Utilities
#include <vsg_webgpu/utils/Builder.h>
#include <vsg_webgpu/utils/ShaderCompiler.h>
