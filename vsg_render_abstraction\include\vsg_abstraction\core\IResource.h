#pragma once

/* <editor-fold desc="MIT License">

Copyright(c) 2024 VSG Render Engine Abstraction

Permission is hereby granted, free of charge, to any person obtaining a copy of this software and associated documentation files (the "Software"), to deal in the Software without restriction, including without limitation the rights to use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of the Software, and to permit persons to whom the Software is furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.

</editor-fold> */

#include <vsg_abstraction/core/Types.h>
#include <vsg_abstraction/core/Export.h>
#include <cstdint>

namespace vsg_abstraction {

/**
 * @brief 缓冲区接口
 */
class IBuffer {
public:
    virtual ~IBuffer() = default;

    // 缓冲区属性
    virtual size_t getSize() const = 0;
    virtual BufferUsage getUsage() const = 0;

    // 数据操作
    virtual void* map() = 0;
    virtual void unmap() = 0;
    virtual void write(const void* data, size_t size, size_t offset = 0) = 0;
    virtual void read(void* data, size_t size, size_t offset = 0) = 0;

    // 原生句柄
    virtual void* getNativeHandle() const = 0;
};

/**
 * @brief 图像接口
 */
class IImage {
public:
    virtual ~IImage() = default;

    // 图像属性
    virtual uint32_t getWidth() const = 0;
    virtual uint32_t getHeight() const = 0;
    virtual uint32_t getDepth() const = 0;
    virtual ImageFormat getFormat() const = 0;
    virtual ImageUsage getUsage() const = 0;

    // 数据操作
    virtual void write(const void* data, size_t size) = 0;
    virtual void read(void* data, size_t size) = 0;

    // 原生句柄
    virtual void* getNativeHandle() const = 0;
};

/**
 * @brief 管线接口
 */
class IPipeline {
public:
    virtual ~IPipeline() = default;

    // 管线属性
    virtual PipelineType getType() const = 0;
    virtual std::string getName() const = 0;

    // 绑定操作
    virtual void bind() = 0;
    virtual void unbind() = 0;

    // 原生句柄
    virtual void* getNativeHandle() const = 0;
};

} // namespace vsg_abstraction
