name: CI
on:
  push:
  pull_request:
env:
  BuildDocEnabled: ${{ github.event_name == 'push' && github.ref == 'refs/heads/master' }}
jobs:
  build:
    runs-on: ${{ matrix.os }}
    strategy:
      matrix:
        os: [ubuntu-latest, macos-latest, windows-latest]
        vulkan-version: [1.3.250.1]
        build-shared: [OFF]
        include:
          - build-shared: ON
            os: windows-latest
            vulkan-version: 1.3.250.1
    continue-on-error: ${{ matrix.vulkan-version == 'latest' }}

    steps:
    - uses: actions/checkout@v2
    - name: Setup cmake
      uses: jwlawson/actions-setup-cmake@v1.12
      with:
        cmake-version: ${{ env.CMakeVersion }}
    - name: Install Vulkan SDK
      uses: humbletim/install-vulkan-sdk@c2aa128094d42ba02959a660f03e0a4e012192f9
      with:
        version: ${{ matrix.vulkan-version }}
        cache: true
    - name: Add MSBuild to PATH
      uses: microsoft/setup-msbuild@v2
      if: startsWith(matrix.os, 'windows')
    - name: Build and Install VSG
      shell: bash
      run: |
         if [ "$RUNNER_OS" == "Windows" ]; then
              cmake . -DBUILD_SHARED_LIBS=${{matrix.build-shared}} -A x64
              cmake --build .
         elif [ "$RUNNER_OS" == "Linux" ]; then
              sudo apt-get -qq update
              sudo apt-get -qq install libxcb1-dev
              cmake . -DBUILD_SHARED_LIBS=${{matrix.build-shared}}
              make -j 3 && sudo make install
         elif [ "$RUNNER_OS" == "macOS" ]; then
              cmake . -DBUILD_SHARED_LIBS=${{matrix.build-shared}}
              make -j 4 && sudo make install
         else
              echo "$RUNNER_OS not supported"
              exit 1
         fi
