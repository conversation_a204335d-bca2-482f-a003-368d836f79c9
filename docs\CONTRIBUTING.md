# How to contribute
Dev<PERSON><PERSON> can help contribute to the VSG through testing, reporting issues, bug fixing, feature development and creation of tutorials and documentation.

## Report a bug

Please use the Issue tracker on github. We provide two templates, a bug report template and a feature request template to help guide what type of information is useful to us.

## Bug fixes

If you have made a bug fix please make a pull request on github. With the PR please use a descriptive but short title line, followed by a paragraph explaining the bug and changes, with references to the github Issue if one has been raised for it.

## Feature request

If you wish to make a feature request, this can be done via our github Issue tracker. Please be prepared to step forward to help with this feature development, or help fund others to do this feature development. If features are not aligned well with the projects scope and goals then these requests will be closed and a path forward for developing this as a 3rd party development will be suggested.

## Feature development

If you have refined existing features or added new features please a make pull request.

## In source documentation

In source documentation can be provided in the form of markdown (.MD) files to be found in the docs/ directory, or as doxygen style comments within the header files. Please use a pull request.

## 3rd Party tutorials, documentation, libraries and program

If you have written documentation, tutorials, libraries or programs and wish to inform developers then please consider adding details about them to the docs/3rdParty files and generating a pull request.
