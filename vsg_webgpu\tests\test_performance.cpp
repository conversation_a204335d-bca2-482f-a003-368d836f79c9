/* <editor-fold desc="MIT License">

Copyright(c) 2024 VSG WebGPU Extension

Permission is hereby granted, free of charge, to any person obtaining a copy of this software and associated documentation files (the "Software"), to deal in the Software without restriction, including without limitation the rights to use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of the Software, and to permit persons to whom the Software is furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.

</editor-fold> */

#include <vsg_webgpu/all.h>
#include <vsg/all.h>
#include <iostream>
#include <chrono>
#include <vector>

using namespace vsg_webgpu;

class PerformanceTest
{
public:
    bool runTests()
    {
        std::cout << "=== VSG WebGPU Performance Test ===" << std::endl;
        
        bool allPassed = true;
        
        allPassed &= testDeviceCreationPerformance();
        allPassed &= testCommandBufferPerformance();
        allPassed &= testSceneGraphTraversalPerformance();
        allPassed &= testResourceCachePerformance();
        
        std::cout << "\n=== Test Results ===" << std::endl;
        std::cout << "Overall: " << (allPassed ? "PASSED" : "FAILED") << std::endl;
        
        return allPassed;
    }

private:
    bool testDeviceCreationPerformance()
    {
        std::cout << "\n--- Test: Device Creation Performance ---" << std::endl;
        
        try
        {
            const int iterations = 10;
            auto startTime = std::chrono::high_resolution_clock::now();
            
            for (int i = 0; i < iterations; ++i)
            {
                initializeWebGPU();
                auto device = Device::create();
                device->initialize();
                shutdownWebGPU();
            }
            
            auto endTime = std::chrono::high_resolution_clock::now();
            auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(endTime - startTime);
            
            double avgTime = duration.count() / static_cast<double>(iterations);
            
            std::cout << "Device creation performance:" << std::endl;
            std::cout << "  Iterations: " << iterations << std::endl;
            std::cout << "  Total time: " << duration.count() << "ms" << std::endl;
            std::cout << "  Average time: " << avgTime << "ms per device" << std::endl;
            
            if (avgTime > 1000.0) // 超过1秒认为性能不佳
            {
                std::cout << "WARNING: Device creation is slow" << std::endl;
            }
            
            std::cout << "PASSED: Device creation performance test completed" << std::endl;
            return true;
        }
        catch (const std::exception& e)
        {
            std::cout << "FAILED: Exception - " << e.what() << std::endl;
            return false;
        }
    }
    
    bool testCommandBufferPerformance()
    {
        std::cout << "\n--- Test: Command Buffer Performance ---" << std::endl;
        
        try
        {
            initializeWebGPU();
            auto device = Device::create();
            device->initialize();
            
            auto commandPool = device->getCommandPool();
            if (!commandPool)
            {
                std::cout << "FAILED: Could not get command pool" << std::endl;
                return false;
            }
            
            const int iterations = 1000;
            auto startTime = std::chrono::high_resolution_clock::now();
            
            std::vector<vsg::ref_ptr<CommandBuffer>> commandBuffers;
            commandBuffers.reserve(iterations);
            
            // 分配命令缓冲区
            for (int i = 0; i < iterations; ++i)
            {
                auto cb = commandPool->allocate();
                if (cb)
                {
                    commandBuffers.push_back(cb);
                }
            }
            
            // 释放命令缓冲区
            for (auto& cb : commandBuffers)
            {
                commandPool->free(cb.get());
            }
            
            auto endTime = std::chrono::high_resolution_clock::now();
            auto duration = std::chrono::duration_cast<std::chrono::microseconds>(endTime - startTime);
            
            double avgTime = duration.count() / static_cast<double>(iterations * 2); // 分配+释放
            
            std::cout << "Command buffer performance:" << std::endl;
            std::cout << "  Iterations: " << iterations << " (alloc + free)" << std::endl;
            std::cout << "  Total time: " << duration.count() << "μs" << std::endl;
            std::cout << "  Average time: " << avgTime << "μs per operation" << std::endl;
            
            shutdownWebGPU();
            
            std::cout << "PASSED: Command buffer performance test completed" << std::endl;
            return true;
        }
        catch (const std::exception& e)
        {
            std::cout << "FAILED: Exception - " << e.what() << std::endl;
            return false;
        }
    }
    
    bool testSceneGraphTraversalPerformance()
    {
        std::cout << "\n--- Test: Scene Graph Traversal Performance ---" << std::endl;
        
        try
        {
            // 创建复杂的场景图
            auto root = vsg::Group::create();
            
            const int numGroups = 100;
            const int numGeometries = 10;
            
            for (int i = 0; i < numGroups; ++i)
            {
                auto group = vsg::Group::create();
                
                for (int j = 0; j < numGeometries; ++j)
                {
                    auto vertices = vsg::vec3Array::create({
                        {0.0f, 0.5f, 0.0f},
                        {-0.5f, -0.5f, 0.0f},
                        {0.5f, -0.5f, 0.0f}
                    });
                    
                    auto indices = vsg::ushortArray::create({0, 1, 2});
                    
                    auto geometry = vsg::Geometry::create();
                    geometry->arrays = {vertices};
                    geometry->indices = indices;
                    geometry->commands = {vsg::DrawIndexed::create(3, 1, 0, 0, 0)};
                    
                    group->addChild(geometry);
                }
                
                root->addChild(group);
            }
            
            // 创建遍历器
            auto device = Device::create();
            device->initialize();
            
            auto recordTraversal = RecordTraversal::create();
            
            const int iterations = 100;
            auto startTime = std::chrono::high_resolution_clock::now();
            
            for (int i = 0; i < iterations; ++i)
            {
                recordTraversal->resetStatistics();
                root->traverse(*recordTraversal);
            }
            
            auto endTime = std::chrono::high_resolution_clock::now();
            auto duration = std::chrono::duration_cast<std::chrono::microseconds>(endTime - startTime);
            
            double avgTime = duration.count() / static_cast<double>(iterations);
            
            std::cout << "Scene graph traversal performance:" << std::endl;
            std::cout << "  Scene complexity: " << numGroups << " groups, " << (numGroups * numGeometries) << " geometries" << std::endl;
            std::cout << "  Iterations: " << iterations << std::endl;
            std::cout << "  Total time: " << duration.count() << "μs" << std::endl;
            std::cout << "  Average time: " << avgTime << "μs per traversal" << std::endl;
            
            shutdownWebGPU();
            
            std::cout << "PASSED: Scene graph traversal performance test completed" << std::endl;
            return true;
        }
        catch (const std::exception& e)
        {
            std::cout << "FAILED: Exception - " << e.what() << std::endl;
            return false;
        }
    }
    
    bool testResourceCachePerformance()
    {
        std::cout << "\n--- Test: Resource Cache Performance ---" << std::endl;
        
        try
        {
            initializeWebGPU();
            auto device = Device::create();
            device->initialize();
            
            auto& cache = device->getResourceCache();
            
            const int iterations = 1000;
            std::string shaderCode = R"(
                @vertex
                fn vs_main() -> @builtin(position) vec4<f32> {
                    return vec4<f32>(0.0, 0.0, 0.0, 1.0);
                }
            )";
            
            auto startTime = std::chrono::high_resolution_clock::now();
            
            // 测试着色器模块缓存
            for (int i = 0; i < iterations; ++i)
            {
                std::string key = "shader_" + std::to_string(i % 10); // 重复使用10个不同的着色器
                cache.getOrCreateShaderModule(key, shaderCode);
            }
            
            auto endTime = std::chrono::high_resolution_clock::now();
            auto duration = std::chrono::duration_cast<std::chrono::microseconds>(endTime - startTime);
            
            double avgTime = duration.count() / static_cast<double>(iterations);
            
            std::cout << "Resource cache performance:" << std::endl;
            std::cout << "  Iterations: " << iterations << std::endl;
            std::cout << "  Total time: " << duration.count() << "μs" << std::endl;
            std::cout << "  Average time: " << avgTime << "μs per cache operation" << std::endl;
            
            shutdownWebGPU();
            
            std::cout << "PASSED: Resource cache performance test completed" << std::endl;
            return true;
        }
        catch (const std::exception& e)
        {
            std::cout << "FAILED: Exception - " << e.what() << std::endl;
            return false;
        }
    }
};

int main()
{
    try
    {
        PerformanceTest test;
        bool success = test.runTests();
        
        return success ? 0 : 1;
    }
    catch (const std::exception& e)
    {
        std::cerr << "Fatal error: " << e.what() << std::endl;
        return 1;
    }
}
