/* <editor-fold desc="MIT License">

Copyright(c) 2024 VSG WebGPU Extension

Permission is hereby granted, free of charge, to any person obtaining a copy of this software and associated documentation files (the "Software"), to deal in the Software without restriction, including without limitation the rights to use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of the Software, and to permit persons to whom the Software is furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.

</editor-fold> */

#include <vsg_webgpu/all.h>
#include <vsg/all.h>
#include <iostream>
#include <chrono>

using namespace vsg_webgpu;

class DeviceTest
{
public:
    bool runTests()
    {
        std::cout << "=== VSG WebGPU Device Test ===" << std::endl;
        
        bool allPassed = true;
        
        allPassed &= testDeviceCreation();
        allPassed &= testQueueAccess();
        allPassed &= testCommandPoolCreation();
        allPassed &= testResourceCache();
        allPassed &= testErrorHandling();
        
        std::cout << "\n=== Test Results ===" << std::endl;
        std::cout << "Overall: " << (allPassed ? "PASSED" : "FAILED") << std::endl;
        
        return allPassed;
    }

private:
    bool testDeviceCreation()
    {
        std::cout << "\n--- Test: Device Creation ---" << std::endl;
        
        try
        {
            // 初始化WebGPU
            initializeWebGPU();
            
            // 创建设备
            auto device = Device::create();
            if (!device)
            {
                std::cout << "FAILED: Could not create device" << std::endl;
                return false;
            }
            
            // 初始化设备
            if (!device->initialize())
            {
                std::cout << "WARNING: Device initialization failed (expected in mock mode)" << std::endl;
#if VSG_WEBGPU_USE_MOCK
                std::cout << "PASSED: Mock device created successfully" << std::endl;
                return true;
#else
                return false;
#endif
            }
            
            std::cout << "PASSED: Device created and initialized successfully" << std::endl;
            std::cout << "Device ID: " << device->deviceID << std::endl;
            
            // 测试功能查询
            auto features = device->getSupportedFeatures();
            std::cout << "Supported features: " << features.size() << std::endl;
            for (const auto& feature : features)
            {
                std::cout << "  - " << feature << std::endl;
            }
            
            return true;
        }
        catch (const std::exception& e)
        {
            std::cout << "FAILED: Exception - " << e.what() << std::endl;
            return false;
        }
    }
    
    bool testQueueAccess()
    {
        std::cout << "\n--- Test: Queue Access ---" << std::endl;
        
        try
        {
            auto device = Device::create();
            if (!device || !device->initialize())
            {
#if VSG_WEBGPU_USE_MOCK
                std::cout << "PASSED: Mock queue access test" << std::endl;
                return true;
#else
                std::cout << "FAILED: Could not initialize device" << std::endl;
                return false;
#endif
            }
            
            // 获取默认队列
            auto queue = device->getQueue();
            if (!queue)
            {
                std::cout << "FAILED: Could not get default queue" << std::endl;
                return false;
            }
            
            std::cout << "PASSED: Queue access successful" << std::endl;
            std::cout << "Queue family index: " << queue->queueFamilyIndex() << std::endl;
            std::cout << "Queue index: " << queue->queueIndex() << std::endl;
            
            return true;
        }
        catch (const std::exception& e)
        {
            std::cout << "FAILED: Exception - " << e.what() << std::endl;
            return false;
        }
    }
    
    bool testCommandPoolCreation()
    {
        std::cout << "\n--- Test: Command Pool Creation ---" << std::endl;
        
        try
        {
            auto device = Device::create();
            if (!device || !device->initialize())
            {
#if VSG_WEBGPU_USE_MOCK
                std::cout << "PASSED: Mock command pool test" << std::endl;
                return true;
#else
                std::cout << "FAILED: Could not initialize device" << std::endl;
                return false;
#endif
            }
            
            // 获取命令池
            auto commandPool = device->getCommandPool();
            if (!commandPool)
            {
                std::cout << "FAILED: Could not get command pool" << std::endl;
                return false;
            }
            
            // 分配命令缓冲区
            auto commandBuffer = commandPool->allocate();
            if (!commandBuffer)
            {
                std::cout << "FAILED: Could not allocate command buffer" << std::endl;
                return false;
            }
            
            std::cout << "PASSED: Command pool and buffer creation successful" << std::endl;
            std::cout << "Command pool queue family: " << commandPool->queueFamilyIndex << std::endl;
            
            return true;
        }
        catch (const std::exception& e)
        {
            std::cout << "FAILED: Exception - " << e.what() << std::endl;
            return false;
        }
    }
    
    bool testResourceCache()
    {
        std::cout << "\n--- Test: Resource Cache ---" << std::endl;
        
        try
        {
            auto device = Device::create();
            if (!device || !device->initialize())
            {
#if VSG_WEBGPU_USE_MOCK
                std::cout << "PASSED: Mock resource cache test" << std::endl;
                return true;
#else
                std::cout << "FAILED: Could not initialize device" << std::endl;
                return false;
#endif
            }
            
            auto& cache = device->getResourceCache();
            
            // 测试着色器模块缓存
            std::string wgslCode = R"(
                @vertex
                fn vs_main() -> @builtin(position) vec4<f32> {
                    return vec4<f32>(0.0, 0.0, 0.0, 1.0);
                }
                
                @fragment
                fn fs_main() -> @location(0) vec4<f32> {
                    return vec4<f32>(1.0, 0.0, 0.0, 1.0);
                }
            )";
            
            auto shader1 = cache.getOrCreateShaderModule("test_shader", wgslCode);
            auto shader2 = cache.getOrCreateShaderModule("test_shader", wgslCode);
            
            // 在mock模式下，这些可能都是空的，但不应该崩溃
            std::cout << "PASSED: Resource cache operations completed" << std::endl;
            
            return true;
        }
        catch (const std::exception& e)
        {
            std::cout << "FAILED: Exception - " << e.what() << std::endl;
            return false;
        }
    }
    
    bool testErrorHandling()
    {
        std::cout << "\n--- Test: Error Handling ---" << std::endl;
        
        try
        {
            auto device = Device::create();
            
            bool errorCallbackCalled = false;
            device->setErrorCallback([&errorCallbackCalled](const std::string& message) {
                errorCallbackCalled = true;
                std::cout << "Error callback triggered: " << message << std::endl;
            });
            
            // 触发一个错误
            device->logError("Test error message");
            
            if (!errorCallbackCalled)
            {
                std::cout << "FAILED: Error callback was not called" << std::endl;
                return false;
            }
            
            std::cout << "PASSED: Error handling works correctly" << std::endl;
            return true;
        }
        catch (const std::exception& e)
        {
            std::cout << "FAILED: Exception - " << e.what() << std::endl;
            return false;
        }
    }
};

int main()
{
    try
    {
        DeviceTest test;
        bool success = test.runTests();
        
        shutdownWebGPU();
        
        return success ? 0 : 1;
    }
    catch (const std::exception& e)
    {
        std::cerr << "Fatal error: " << e.what() << std::endl;
        return 1;
    }
}
