# Rocky Render Engine Refactor - 测试程序构建配置

# ========== 测试程序源文件 ==========

set(ROCKY_RENDER_TEST_SOURCES
    # 基础功能测试
    test_render_engine_manager.cpp
    test_render_engine_factory.cpp
    test_mock_backend.cpp
    
    # 场景图测试
    test_scene_node.cpp
    test_scene_graph_operations.cpp
    
    # 资源管理测试
    test_texture_management.cpp
    test_buffer_management.cpp
    test_shader_management.cpp
    
    # 渲染测试
    test_render_graph.cpp
    test_rendering_pipeline.cpp
    
    # 性能测试
    test_performance_benchmarks.cpp
    
    # 集成测试
    test_multi_backend_consistency.cpp
    test_thread_safety.cpp
)

# ========== 示例程序源文件 ==========

set(ROCKY_RENDER_EXAMPLE_SOURCES
    # 基础示例
    example_basic_rendering.cpp
    example_scene_graph.cpp
    example_multi_backend.cpp
    
    # 高级示例
    example_texture_loading.cpp
    example_performance_test.cpp
    example_thread_rendering.cpp
)

# ========== 创建测试可执行文件 ==========

foreach(test_source ${ROCKY_RENDER_TEST_SOURCES})
    get_filename_component(test_name ${test_source} NAME_WE)
    add_executable(${test_name} ${test_source})
    
    # 链接库
    target_link_libraries(${test_name} PRIVATE rocky_render)
    
    # 设置属性
    set_target_properties(${test_name} PROPERTIES
        CXX_STANDARD 20
        CXX_STANDARD_REQUIRED ON
        RUNTIME_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/bin/tests
    )
    
    # 添加到CTest
    add_test(NAME ${test_name} COMMAND ${test_name})
    
    # 设置测试属性
    set_tests_properties(${test_name} PROPERTIES
        TIMEOUT 60
        WORKING_DIRECTORY ${CMAKE_BINARY_DIR}/bin/tests
    )
endforeach()

# ========== 创建示例可执行文件 ==========

foreach(example_source ${ROCKY_RENDER_EXAMPLE_SOURCES})
    get_filename_component(example_name ${example_source} NAME_WE)
    add_executable(${example_name} ${example_source})
    
    # 链接库
    target_link_libraries(${example_name} PRIVATE rocky_render)
    
    # 设置属性
    set_target_properties(${example_name} PROPERTIES
        CXX_STANDARD 20
        CXX_STANDARD_REQUIRED ON
        RUNTIME_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/bin/examples
    )
endforeach()

# ========== 特殊测试配置 ==========

# 性能测试需要更长的超时时间
if(TARGET test_performance_benchmarks)
    set_tests_properties(test_performance_benchmarks PROPERTIES TIMEOUT 300)
endif()

# 多后端测试需要特殊环境
if(TARGET test_multi_backend_consistency)
    set_tests_properties(test_multi_backend_consistency PROPERTIES
        ENVIRONMENT "ROCKY_TEST_ALL_BACKENDS=1"
    )
endif()

# ========== 测试数据 ==========

# 复制测试数据文件
file(COPY ${CMAKE_CURRENT_SOURCE_DIR}/data
     DESTINATION ${CMAKE_BINARY_DIR}/bin/tests)

# ========== 自定义测试目标 ==========

# 快速测试（只运行基础测试）
add_custom_target(test_quick
    COMMAND ${CMAKE_CTEST_COMMAND} -L "quick" --output-on-failure
    DEPENDS rocky_render
    WORKING_DIRECTORY ${CMAKE_BINARY_DIR}
)

# 完整测试（运行所有测试）
add_custom_target(test_full
    COMMAND ${CMAKE_CTEST_COMMAND} --output-on-failure
    DEPENDS rocky_render
    WORKING_DIRECTORY ${CMAKE_BINARY_DIR}
)

# 性能测试
add_custom_target(test_performance
    COMMAND ${CMAKE_CTEST_COMMAND} -L "performance" --output-on-failure
    DEPENDS rocky_render
    WORKING_DIRECTORY ${CMAKE_BINARY_DIR}
)

# ========== 测试标签 ==========

# 为测试添加标签
set_tests_properties(test_render_engine_manager PROPERTIES LABELS "quick;core")
set_tests_properties(test_render_engine_factory PROPERTIES LABELS "quick;core")
set_tests_properties(test_mock_backend PROPERTIES LABELS "quick;backend")
set_tests_properties(test_scene_node PROPERTIES LABELS "quick;scenegraph")
set_tests_properties(test_performance_benchmarks PROPERTIES LABELS "performance")
set_tests_properties(test_multi_backend_consistency PROPERTIES LABELS "integration")
set_tests_properties(test_thread_safety PROPERTIES LABELS "integration;threading")

# ========== 构建信息 ==========

message(STATUS "Rocky Render Tests Configuration:")
message(STATUS "  Test programs: ${ROCKY_RENDER_TEST_SOURCES}")
message(STATUS "  Example programs: ${ROCKY_RENDER_EXAMPLE_SOURCES}")
message(STATUS "  Test output directory: ${CMAKE_BINARY_DIR}/bin/tests")
message(STATUS "  Example output directory: ${CMAKE_BINARY_DIR}/bin/examples")
