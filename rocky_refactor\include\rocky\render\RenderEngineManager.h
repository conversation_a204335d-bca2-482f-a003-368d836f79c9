#pragma once

/* <editor-fold desc="MIT License">

Copyright(c) 2024 Rocky Render Engine Refactor

Permission is hereby granted, free of charge, to any person obtaining a copy of this software and associated documentation files (the "Software"), to deal in the Software without restriction, including without limitation the rights to use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of the Software, and to permit persons to whom the Software is furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.

</editor-fold> */

#include <memory>
#include <mutex>
#include <functional>
#include <rocky/render/IRenderEngine.h>
#include <rocky/render/RenderTypes.h>

namespace rocky {
namespace render {

/**
 * @brief 渲染引擎管理器
 * 
 * 全局单例类，负责管理渲染引擎的创建、切换和生命周期。
 * 这是整个渲染系统的入口点，所有渲染相关的操作都通过这个管理器进行。
 */
class RenderEngineManager {
public:
    /**
     * @brief 获取单例实例
     * @return 渲染引擎管理器实例引用
     */
    static RenderEngineManager& instance();

    // 禁用拷贝构造和赋值
    RenderEngineManager(const RenderEngineManager&) = delete;
    RenderEngineManager& operator=(const RenderEngineManager&) = delete;

    // ========== 引擎管理 ==========

    /**
     * @brief 设置渲染引擎
     * @param engine 渲染引擎实例
     * @param autoInitialize 是否自动初始化引擎
     * @return 成功返回true
     */
    bool setEngine(std::unique_ptr<IRenderEngine> engine, bool autoInitialize = true);

    /**
     * @brief 获取当前渲染引擎
     * @return 渲染引擎指针，如果未设置则返回nullptr
     */
    IRenderEngine* getEngine() const;

    /**
     * @brief 检查是否有可用的渲染引擎
     * @return 有可用引擎返回true
     */
    bool hasEngine() const;

    /**
     * @brief 获取当前渲染后端类型
     * @return 渲染后端类型，如果未设置引擎则返回Mock
     */
    RenderBackend getCurrentBackend() const;

    /**
     * @brief 切换渲染后端
     * @param backend 目标渲染后端
     * @param config 引擎配置（可选）
     * @return 成功返回true
     */
    bool switchBackend(RenderBackend backend, const void* config = nullptr);

    // ========== 生命周期管理 ==========

    /**
     * @brief 初始化渲染引擎
     * @return 成功返回true
     */
    bool initialize();

    /**
     * @brief 关闭渲染引擎
     */
    void shutdown();

    /**
     * @brief 检查渲染引擎是否已初始化
     * @return 已初始化返回true
     */
    bool isInitialized() const;

    // ========== 便捷接口 ==========

    /**
     * @brief 创建窗口
     * @param config 窗口配置
     * @return 窗口对象指针
     */
    std::shared_ptr<IWindow> createWindow(const WindowConfig& config = {});

    /**
     * @brief 创建场景图根节点
     * @return 根节点指针
     */
    std::shared_ptr<ISceneNode> createSceneRoot();

    /**
     * @brief 创建渲染图
     * @param window 目标窗口
     * @return 渲染图指针
     */
    std::shared_ptr<IRenderGraph> createRenderGraph(std::shared_ptr<IWindow> window);

    /**
     * @brief 渲染一帧
     * @param renderGraph 渲染图
     * @return 成功返回true
     */
    bool render(std::shared_ptr<IRenderGraph> renderGraph);

    // ========== 回调和事件 ==========

    /**
     * @brief 引擎切换回调函数类型
     */
    using EngineChangeCallback = std::function<void(RenderBackend oldBackend, RenderBackend newBackend)>;

    /**
     * @brief 设置引擎切换回调
     * @param callback 回调函数
     */
    void setEngineChangeCallback(EngineChangeCallback callback);

    /**
     * @brief 设置错误回调
     * @param callback 错误回调函数
     */
    void setErrorCallback(ErrorCallback callback);

    // ========== 调试和诊断 ==========

    /**
     * @brief 获取渲染统计信息
     * @return 渲染统计信息
     */
    RenderStatistics getRenderStatistics() const;

    /**
     * @brief 重置渲染统计信息
     */
    void resetRenderStatistics();

    /**
     * @brief 获取引擎信息
     * @return 引擎信息，如果未设置引擎则返回空信息
     */
    RenderEngineInfo getEngineInfo() const;

    /**
     * @brief 获取设备能力信息
     * @return 设备能力信息
     */
    DeviceCapabilities getDeviceCapabilities() const;

    /**
     * @brief 开始性能分析标记
     * @param name 标记名称
     */
    void beginProfileMarker(const std::string& name);

    /**
     * @brief 结束性能分析标记
     */
    void endProfileMarker();

    // ========== 高级功能 ==========

    /**
     * @brief 等待渲染完成
     */
    void waitIdle();

    /**
     * @brief 执行自定义渲染命令
     * @param command 自定义命令函数
     */
    void executeCustomCommand(std::function<void(IRenderEngine*)> command);

    /**
     * @brief 获取原生渲染引擎句柄
     * @return 原生句柄，类型取决于具体实现
     */
    void* getNativeEngineHandle() const;

private:
    // 私有构造函数（单例模式）
    RenderEngineManager() = default;
    ~RenderEngineManager();

    // 内部方法
    void _notifyEngineChange(RenderBackend oldBackend, RenderBackend newBackend);
    void _handleError(const std::string& error);

    // 成员变量
    mutable std::mutex mutex_;                          ///< 线程安全锁
    std::unique_ptr<IRenderEngine> engine_;             ///< 当前渲染引擎
    bool initialized_ = false;                          ///< 初始化状态
    RenderBackend currentBackend_ = RenderBackend::Mock; ///< 当前后端类型

    // 回调函数
    EngineChangeCallback engineChangeCallback_;
    ErrorCallback errorCallback_;

    // 统计信息
    mutable RenderStatistics statistics_;
};

// ========== 全局便捷函数 ==========

/**
 * @brief 获取全局渲染引擎管理器
 * @return 渲染引擎管理器引用
 */
inline RenderEngineManager& getRenderManager() {
    return RenderEngineManager::instance();
}

/**
 * @brief 获取当前渲染引擎
 * @return 渲染引擎指针
 */
inline IRenderEngine* getRenderEngine() {
    return RenderEngineManager::instance().getEngine();
}

/**
 * @brief 检查渲染引擎是否可用
 * @return 可用返回true
 */
inline bool isRenderEngineAvailable() {
    return RenderEngineManager::instance().hasEngine() && 
           RenderEngineManager::instance().isInitialized();
}

// ========== 宏定义 ==========

/**
 * @brief 安全获取渲染引擎的宏
 * 如果引擎不可用，会记录错误并返回
 */
#define ROCKY_GET_RENDER_ENGINE() \
    auto* engine = rocky::render::getRenderEngine(); \
    if (!engine) { \
        if (auto& manager = rocky::render::getRenderManager(); manager.hasEngine()) { \
            /* 引擎存在但未初始化 */ \
            if (!manager.initialize()) { \
                return; /* 或者返回适当的错误值 */ \
            } \
            engine = manager.getEngine(); \
        } else { \
            /* 没有设置引擎 */ \
            return; /* 或者返回适当的错误值 */ \
        } \
    }

/**
 * @brief 带返回值的安全获取渲染引擎的宏
 */
#define ROCKY_GET_RENDER_ENGINE_OR_RETURN(retval) \
    auto* engine = rocky::render::getRenderEngine(); \
    if (!engine) { \
        if (auto& manager = rocky::render::getRenderManager(); manager.hasEngine()) { \
            if (!manager.initialize()) { \
                return retval; \
            } \
            engine = manager.getEngine(); \
        } else { \
            return retval; \
        } \
    }

} // namespace render
} // namespace rocky
