# CMake generated Testfile for 
# Source directory: F:/cmo-dev/my_osgearth_web/osgearth_origin/VulkanSceneGraph/vsg_render_abstraction/tests
# Build directory: F:/cmo-dev/my_osgearth_web/osgearth_origin/VulkanSceneGraph/vsg_render_abstraction/build_test/tests
# 
# This file includes the relevant testing commands required for 
# testing this directory and lists subdirectories to be tested as well.
if(CTEST_CONFIGURATION_TYPE MATCHES "^([Dd][Ee][Bb][Uu][Gg])$")
  add_test([=[BasicFunctionality]=] "F:/cmo-dev/my_osgearth_web/osgearth_origin/VulkanSceneGraph/vsg_render_abstraction/build_test/tests/Debug/test_basic.exe")
  set_tests_properties([=[BasicFunctionality]=] PROPERTIES  LABELS "basic;core" TIMEOUT "30" _BACKTRACE_TRIPLES "F:/cmo-dev/my_osgearth_web/osgearth_origin/VulkanSceneGraph/vsg_render_abstraction/tests/CMakeLists.txt;14;add_test;F:/cmo-dev/my_osgearth_web/osgearth_origin/VulkanSceneGraph/vsg_render_abstraction/tests/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Rr][Ee][Ll][Ee][Aa][Ss][Ee])$")
  add_test([=[BasicFunctionality]=] "F:/cmo-dev/my_osgearth_web/osgearth_origin/VulkanSceneGraph/vsg_render_abstraction/build_test/tests/Release/test_basic.exe")
  set_tests_properties([=[BasicFunctionality]=] PROPERTIES  LABELS "basic;core" TIMEOUT "30" _BACKTRACE_TRIPLES "F:/cmo-dev/my_osgearth_web/osgearth_origin/VulkanSceneGraph/vsg_render_abstraction/tests/CMakeLists.txt;14;add_test;F:/cmo-dev/my_osgearth_web/osgearth_origin/VulkanSceneGraph/vsg_render_abstraction/tests/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Mm][Ii][Nn][Ss][Ii][Zz][Ee][Rr][Ee][Ll])$")
  add_test([=[BasicFunctionality]=] "F:/cmo-dev/my_osgearth_web/osgearth_origin/VulkanSceneGraph/vsg_render_abstraction/build_test/tests/MinSizeRel/test_basic.exe")
  set_tests_properties([=[BasicFunctionality]=] PROPERTIES  LABELS "basic;core" TIMEOUT "30" _BACKTRACE_TRIPLES "F:/cmo-dev/my_osgearth_web/osgearth_origin/VulkanSceneGraph/vsg_render_abstraction/tests/CMakeLists.txt;14;add_test;F:/cmo-dev/my_osgearth_web/osgearth_origin/VulkanSceneGraph/vsg_render_abstraction/tests/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Rr][Ee][Ll][Ww][Ii][Tt][Hh][Dd][Ee][Bb][Ii][Nn][Ff][Oo])$")
  add_test([=[BasicFunctionality]=] "F:/cmo-dev/my_osgearth_web/osgearth_origin/VulkanSceneGraph/vsg_render_abstraction/build_test/tests/RelWithDebInfo/test_basic.exe")
  set_tests_properties([=[BasicFunctionality]=] PROPERTIES  LABELS "basic;core" TIMEOUT "30" _BACKTRACE_TRIPLES "F:/cmo-dev/my_osgearth_web/osgearth_origin/VulkanSceneGraph/vsg_render_abstraction/tests/CMakeLists.txt;14;add_test;F:/cmo-dev/my_osgearth_web/osgearth_origin/VulkanSceneGraph/vsg_render_abstraction/tests/CMakeLists.txt;0;")
else()
  add_test([=[BasicFunctionality]=] NOT_AVAILABLE)
endif()
if(CTEST_CONFIGURATION_TYPE MATCHES "^([Dd][Ee][Bb][Uu][Gg])$")
  add_test([=[MultiBackend]=] "F:/cmo-dev/my_osgearth_web/osgearth_origin/VulkanSceneGraph/vsg_render_abstraction/build_test/tests/Debug/test_multi_backend.exe")
  set_tests_properties([=[MultiBackend]=] PROPERTIES  LABELS "backend;switching" TIMEOUT "45" _BACKTRACE_TRIPLES "F:/cmo-dev/my_osgearth_web/osgearth_origin/VulkanSceneGraph/vsg_render_abstraction/tests/CMakeLists.txt;63;add_test;F:/cmo-dev/my_osgearth_web/osgearth_origin/VulkanSceneGraph/vsg_render_abstraction/tests/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Rr][Ee][Ll][Ee][Aa][Ss][Ee])$")
  add_test([=[MultiBackend]=] "F:/cmo-dev/my_osgearth_web/osgearth_origin/VulkanSceneGraph/vsg_render_abstraction/build_test/tests/Release/test_multi_backend.exe")
  set_tests_properties([=[MultiBackend]=] PROPERTIES  LABELS "backend;switching" TIMEOUT "45" _BACKTRACE_TRIPLES "F:/cmo-dev/my_osgearth_web/osgearth_origin/VulkanSceneGraph/vsg_render_abstraction/tests/CMakeLists.txt;63;add_test;F:/cmo-dev/my_osgearth_web/osgearth_origin/VulkanSceneGraph/vsg_render_abstraction/tests/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Mm][Ii][Nn][Ss][Ii][Zz][Ee][Rr][Ee][Ll])$")
  add_test([=[MultiBackend]=] "F:/cmo-dev/my_osgearth_web/osgearth_origin/VulkanSceneGraph/vsg_render_abstraction/build_test/tests/MinSizeRel/test_multi_backend.exe")
  set_tests_properties([=[MultiBackend]=] PROPERTIES  LABELS "backend;switching" TIMEOUT "45" _BACKTRACE_TRIPLES "F:/cmo-dev/my_osgearth_web/osgearth_origin/VulkanSceneGraph/vsg_render_abstraction/tests/CMakeLists.txt;63;add_test;F:/cmo-dev/my_osgearth_web/osgearth_origin/VulkanSceneGraph/vsg_render_abstraction/tests/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Rr][Ee][Ll][Ww][Ii][Tt][Hh][Dd][Ee][Bb][Ii][Nn][Ff][Oo])$")
  add_test([=[MultiBackend]=] "F:/cmo-dev/my_osgearth_web/osgearth_origin/VulkanSceneGraph/vsg_render_abstraction/build_test/tests/RelWithDebInfo/test_multi_backend.exe")
  set_tests_properties([=[MultiBackend]=] PROPERTIES  LABELS "backend;switching" TIMEOUT "45" _BACKTRACE_TRIPLES "F:/cmo-dev/my_osgearth_web/osgearth_origin/VulkanSceneGraph/vsg_render_abstraction/tests/CMakeLists.txt;63;add_test;F:/cmo-dev/my_osgearth_web/osgearth_origin/VulkanSceneGraph/vsg_render_abstraction/tests/CMakeLists.txt;0;")
else()
  add_test([=[MultiBackend]=] NOT_AVAILABLE)
endif()
if(CTEST_CONFIGURATION_TYPE MATCHES "^([Dd][Ee][Bb][Uu][Gg])$")
  add_test([=[Performance]=] "F:/cmo-dev/my_osgearth_web/osgearth_origin/VulkanSceneGraph/vsg_render_abstraction/build_test/tests/Debug/test_performance.exe")
  set_tests_properties([=[Performance]=] PROPERTIES  LABELS "performance;benchmark" TIMEOUT "120" _BACKTRACE_TRIPLES "F:/cmo-dev/my_osgearth_web/osgearth_origin/VulkanSceneGraph/vsg_render_abstraction/tests/CMakeLists.txt;79;add_test;F:/cmo-dev/my_osgearth_web/osgearth_origin/VulkanSceneGraph/vsg_render_abstraction/tests/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Rr][Ee][Ll][Ee][Aa][Ss][Ee])$")
  add_test([=[Performance]=] "F:/cmo-dev/my_osgearth_web/osgearth_origin/VulkanSceneGraph/vsg_render_abstraction/build_test/tests/Release/test_performance.exe")
  set_tests_properties([=[Performance]=] PROPERTIES  LABELS "performance;benchmark" TIMEOUT "120" _BACKTRACE_TRIPLES "F:/cmo-dev/my_osgearth_web/osgearth_origin/VulkanSceneGraph/vsg_render_abstraction/tests/CMakeLists.txt;79;add_test;F:/cmo-dev/my_osgearth_web/osgearth_origin/VulkanSceneGraph/vsg_render_abstraction/tests/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Mm][Ii][Nn][Ss][Ii][Zz][Ee][Rr][Ee][Ll])$")
  add_test([=[Performance]=] "F:/cmo-dev/my_osgearth_web/osgearth_origin/VulkanSceneGraph/vsg_render_abstraction/build_test/tests/MinSizeRel/test_performance.exe")
  set_tests_properties([=[Performance]=] PROPERTIES  LABELS "performance;benchmark" TIMEOUT "120" _BACKTRACE_TRIPLES "F:/cmo-dev/my_osgearth_web/osgearth_origin/VulkanSceneGraph/vsg_render_abstraction/tests/CMakeLists.txt;79;add_test;F:/cmo-dev/my_osgearth_web/osgearth_origin/VulkanSceneGraph/vsg_render_abstraction/tests/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Rr][Ee][Ll][Ww][Ii][Tt][Hh][Dd][Ee][Bb][Ii][Nn][Ff][Oo])$")
  add_test([=[Performance]=] "F:/cmo-dev/my_osgearth_web/osgearth_origin/VulkanSceneGraph/vsg_render_abstraction/build_test/tests/RelWithDebInfo/test_performance.exe")
  set_tests_properties([=[Performance]=] PROPERTIES  LABELS "performance;benchmark" TIMEOUT "120" _BACKTRACE_TRIPLES "F:/cmo-dev/my_osgearth_web/osgearth_origin/VulkanSceneGraph/vsg_render_abstraction/tests/CMakeLists.txt;79;add_test;F:/cmo-dev/my_osgearth_web/osgearth_origin/VulkanSceneGraph/vsg_render_abstraction/tests/CMakeLists.txt;0;")
else()
  add_test([=[Performance]=] NOT_AVAILABLE)
endif()
if(CTEST_CONFIGURATION_TYPE MATCHES "^([Dd][Ee][Bb][Uu][Gg])$")
  add_test([=[Memory]=] "F:/cmo-dev/my_osgearth_web/osgearth_origin/VulkanSceneGraph/vsg_render_abstraction/build_test/tests/Debug/test_memory.exe")
  set_tests_properties([=[Memory]=] PROPERTIES  LABELS "memory;leak" TIMEOUT "60" _BACKTRACE_TRIPLES "F:/cmo-dev/my_osgearth_web/osgearth_origin/VulkanSceneGraph/vsg_render_abstraction/tests/CMakeLists.txt;95;add_test;F:/cmo-dev/my_osgearth_web/osgearth_origin/VulkanSceneGraph/vsg_render_abstraction/tests/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Rr][Ee][Ll][Ee][Aa][Ss][Ee])$")
  add_test([=[Memory]=] "F:/cmo-dev/my_osgearth_web/osgearth_origin/VulkanSceneGraph/vsg_render_abstraction/build_test/tests/Release/test_memory.exe")
  set_tests_properties([=[Memory]=] PROPERTIES  LABELS "memory;leak" TIMEOUT "60" _BACKTRACE_TRIPLES "F:/cmo-dev/my_osgearth_web/osgearth_origin/VulkanSceneGraph/vsg_render_abstraction/tests/CMakeLists.txt;95;add_test;F:/cmo-dev/my_osgearth_web/osgearth_origin/VulkanSceneGraph/vsg_render_abstraction/tests/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Mm][Ii][Nn][Ss][Ii][Zz][Ee][Rr][Ee][Ll])$")
  add_test([=[Memory]=] "F:/cmo-dev/my_osgearth_web/osgearth_origin/VulkanSceneGraph/vsg_render_abstraction/build_test/tests/MinSizeRel/test_memory.exe")
  set_tests_properties([=[Memory]=] PROPERTIES  LABELS "memory;leak" TIMEOUT "60" _BACKTRACE_TRIPLES "F:/cmo-dev/my_osgearth_web/osgearth_origin/VulkanSceneGraph/vsg_render_abstraction/tests/CMakeLists.txt;95;add_test;F:/cmo-dev/my_osgearth_web/osgearth_origin/VulkanSceneGraph/vsg_render_abstraction/tests/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Rr][Ee][Ll][Ww][Ii][Tt][Hh][Dd][Ee][Bb][Ii][Nn][Ff][Oo])$")
  add_test([=[Memory]=] "F:/cmo-dev/my_osgearth_web/osgearth_origin/VulkanSceneGraph/vsg_render_abstraction/build_test/tests/RelWithDebInfo/test_memory.exe")
  set_tests_properties([=[Memory]=] PROPERTIES  LABELS "memory;leak" TIMEOUT "60" _BACKTRACE_TRIPLES "F:/cmo-dev/my_osgearth_web/osgearth_origin/VulkanSceneGraph/vsg_render_abstraction/tests/CMakeLists.txt;95;add_test;F:/cmo-dev/my_osgearth_web/osgearth_origin/VulkanSceneGraph/vsg_render_abstraction/tests/CMakeLists.txt;0;")
else()
  add_test([=[Memory]=] NOT_AVAILABLE)
endif()
if(CTEST_CONFIGURATION_TYPE MATCHES "^([Dd][Ee][Bb][Uu][Gg])$")
  add_test([=[SceneGraph]=] "F:/cmo-dev/my_osgearth_web/osgearth_origin/VulkanSceneGraph/vsg_render_abstraction/build_test/tests/Debug/test_scene_graph.exe")
  set_tests_properties([=[SceneGraph]=] PROPERTIES  LABELS "scene;graph" TIMEOUT "45" _BACKTRACE_TRIPLES "F:/cmo-dev/my_osgearth_web/osgearth_origin/VulkanSceneGraph/vsg_render_abstraction/tests/CMakeLists.txt;111;add_test;F:/cmo-dev/my_osgearth_web/osgearth_origin/VulkanSceneGraph/vsg_render_abstraction/tests/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Rr][Ee][Ll][Ee][Aa][Ss][Ee])$")
  add_test([=[SceneGraph]=] "F:/cmo-dev/my_osgearth_web/osgearth_origin/VulkanSceneGraph/vsg_render_abstraction/build_test/tests/Release/test_scene_graph.exe")
  set_tests_properties([=[SceneGraph]=] PROPERTIES  LABELS "scene;graph" TIMEOUT "45" _BACKTRACE_TRIPLES "F:/cmo-dev/my_osgearth_web/osgearth_origin/VulkanSceneGraph/vsg_render_abstraction/tests/CMakeLists.txt;111;add_test;F:/cmo-dev/my_osgearth_web/osgearth_origin/VulkanSceneGraph/vsg_render_abstraction/tests/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Mm][Ii][Nn][Ss][Ii][Zz][Ee][Rr][Ee][Ll])$")
  add_test([=[SceneGraph]=] "F:/cmo-dev/my_osgearth_web/osgearth_origin/VulkanSceneGraph/vsg_render_abstraction/build_test/tests/MinSizeRel/test_scene_graph.exe")
  set_tests_properties([=[SceneGraph]=] PROPERTIES  LABELS "scene;graph" TIMEOUT "45" _BACKTRACE_TRIPLES "F:/cmo-dev/my_osgearth_web/osgearth_origin/VulkanSceneGraph/vsg_render_abstraction/tests/CMakeLists.txt;111;add_test;F:/cmo-dev/my_osgearth_web/osgearth_origin/VulkanSceneGraph/vsg_render_abstraction/tests/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Rr][Ee][Ll][Ww][Ii][Tt][Hh][Dd][Ee][Bb][Ii][Nn][Ff][Oo])$")
  add_test([=[SceneGraph]=] "F:/cmo-dev/my_osgearth_web/osgearth_origin/VulkanSceneGraph/vsg_render_abstraction/build_test/tests/RelWithDebInfo/test_scene_graph.exe")
  set_tests_properties([=[SceneGraph]=] PROPERTIES  LABELS "scene;graph" TIMEOUT "45" _BACKTRACE_TRIPLES "F:/cmo-dev/my_osgearth_web/osgearth_origin/VulkanSceneGraph/vsg_render_abstraction/tests/CMakeLists.txt;111;add_test;F:/cmo-dev/my_osgearth_web/osgearth_origin/VulkanSceneGraph/vsg_render_abstraction/tests/CMakeLists.txt;0;")
else()
  add_test([=[SceneGraph]=] NOT_AVAILABLE)
endif()
