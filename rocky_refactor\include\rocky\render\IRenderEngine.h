#pragma once

/* <editor-fold desc="MIT License">

Copyright(c) 2024 Rocky Render Engine Refactor

Permission is hereby granted, free of charge, to any person obtaining a copy of this software and associated documentation files (the "Software"), to deal in the Software without restriction, including without limitation the rights to use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of the Software, and to permit persons to whom the Software is furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.

</editor-fold> */

#include <memory>
#include <string>
#include <vector>
#include <functional>
#include <rocky/render/RenderTypes.h>

namespace rocky {
namespace render {

// 前向声明
class ISceneNode;
class IRenderGraph;
class ITexture;
class IBuffer;
class IShader;
class IPipeline;
class IWindow;
class ICamera;

/**
 * @brief 渲染引擎抽象接口
 * 
 * 这是渲染引擎的核心抽象接口，定义了所有渲染相关的操作。
 * 不同的渲染后端（Vulkan、WebGPU、OpenGL等）都需要实现这个接口。
 */
class IRenderEngine {
public:
    virtual ~IRenderEngine() = default;

    // ========== 生命周期管理 ==========
    
    /**
     * @brief 初始化渲染引擎
     * @return 成功返回true，失败返回false
     */
    virtual bool initialize() = 0;
    
    /**
     * @brief 关闭渲染引擎，释放所有资源
     */
    virtual void shutdown() = 0;
    
    /**
     * @brief 检查渲染引擎是否已初始化
     */
    virtual bool isInitialized() const = 0;

    // ========== 设备和上下文管理 ==========
    
    /**
     * @brief 获取渲染引擎类型
     */
    virtual RenderBackend getBackendType() const = 0;
    
    /**
     * @brief 获取渲染引擎信息
     */
    virtual RenderEngineInfo getEngineInfo() const = 0;
    
    /**
     * @brief 获取设备能力信息
     */
    virtual DeviceCapabilities getDeviceCapabilities() const = 0;

    // ========== 窗口和表面管理 ==========
    
    /**
     * @brief 创建渲染窗口
     * @param config 窗口配置
     * @return 窗口对象指针
     */
    virtual std::shared_ptr<IWindow> createWindow(const WindowConfig& config) = 0;
    
    /**
     * @brief 销毁窗口
     * @param window 要销毁的窗口
     */
    virtual void destroyWindow(std::shared_ptr<IWindow> window) = 0;

    // ========== 场景图管理 ==========
    
    /**
     * @brief 创建组节点
     * @return 组节点指针
     */
    virtual std::shared_ptr<ISceneNode> createGroup() = 0;
    
    /**
     * @brief 创建变换节点
     * @return 变换节点指针
     */
    virtual std::shared_ptr<ISceneNode> createTransform() = 0;
    
    /**
     * @brief 创建几何节点
     * @param geometry 几何数据
     * @return 几何节点指针
     */
    virtual std::shared_ptr<ISceneNode> createGeometry(const GeometryData& geometry) = 0;
    
    /**
     * @brief 创建状态组节点
     * @return 状态组节点指针
     */
    virtual std::shared_ptr<ISceneNode> createStateGroup() = 0;

    // ========== 渲染图管理 ==========
    
    /**
     * @brief 创建渲染图
     * @param window 目标窗口
     * @return 渲染图指针
     */
    virtual std::shared_ptr<IRenderGraph> createRenderGraph(std::shared_ptr<IWindow> window) = 0;
    
    /**
     * @brief 渲染一帧
     * @param renderGraph 渲染图
     * @return 成功返回true
     */
    virtual bool render(std::shared_ptr<IRenderGraph> renderGraph) = 0;

    // ========== 资源管理 ==========
    
    /**
     * @brief 创建纹理
     * @param config 纹理配置
     * @return 纹理对象指针
     */
    virtual std::shared_ptr<ITexture> createTexture(const TextureConfig& config) = 0;
    
    /**
     * @brief 创建缓冲区
     * @param config 缓冲区配置
     * @return 缓冲区对象指针
     */
    virtual std::shared_ptr<IBuffer> createBuffer(const BufferConfig& config) = 0;
    
    /**
     * @brief 创建着色器
     * @param config 着色器配置
     * @return 着色器对象指针
     */
    virtual std::shared_ptr<IShader> createShader(const ShaderConfig& config) = 0;
    
    /**
     * @brief 创建渲染管线
     * @param config 管线配置
     * @return 管线对象指针
     */
    virtual std::shared_ptr<IPipeline> createPipeline(const PipelineConfig& config) = 0;

    // ========== 相机管理 ==========
    
    /**
     * @brief 创建相机
     * @param config 相机配置
     * @return 相机对象指针
     */
    virtual std::shared_ptr<ICamera> createCamera(const CameraConfig& config) = 0;

    // ========== 同步和性能 ==========
    
    /**
     * @brief 等待设备空闲
     */
    virtual void waitIdle() = 0;
    
    /**
     * @brief 获取渲染统计信息
     */
    virtual RenderStatistics getRenderStatistics() const = 0;
    
    /**
     * @brief 重置渲染统计信息
     */
    virtual void resetRenderStatistics() = 0;

    // ========== 调试和诊断 ==========
    
    /**
     * @brief 设置调试回调
     * @param callback 调试回调函数
     */
    virtual void setDebugCallback(DebugCallback callback) = 0;
    
    /**
     * @brief 开始调试标记
     * @param name 标记名称
     */
    virtual void beginDebugMarker(const std::string& name) = 0;
    
    /**
     * @brief 结束调试标记
     */
    virtual void endDebugMarker() = 0;

    // ========== 扩展接口 ==========
    
    /**
     * @brief 获取原生渲染引擎对象（用于高级用法）
     * @return 原生对象指针，类型取决于具体实现
     */
    virtual void* getNativeHandle() const = 0;
    
    /**
     * @brief 执行自定义渲染命令
     * @param command 自定义命令函数
     */
    virtual void executeCustomCommand(std::function<void(void*)> command) = 0;
};

} // namespace render
} // namespace rocky
