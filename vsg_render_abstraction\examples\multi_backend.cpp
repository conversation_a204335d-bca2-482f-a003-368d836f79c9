/* <editor-fold desc="MIT License">

Copyright(c) 2024 VSG Render Engine Abstraction

Permission is hereby granted, free of charge, to any person obtaining a copy of this software and associated documentation files (the "Software"), to deal in the Software without restriction, including without limitation the rights to use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of the Software, and to permit persons to whom the Software is furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.

</editor-fold> */

#include <vsg_abstraction/core/RenderEngineManager.h>
#include <vsg_abstraction/core/RenderEngineFactory.h>
#include <vsg_abstraction/core/Config.h>
#include <iostream>

using namespace vsg_abstraction;

int main() {
    std::cout << "=== Multi-Backend Example ===" << std::endl;
    std::cout << "Version: " << VERSION_STRING << std::endl;
    std::cout << "Platform: " << PLATFORM_NAME << std::endl;
    std::cout << std::endl;
    
    auto& manager = getRenderManager();
    
    // Get supported backends
    auto backends = RenderEngineFactory::getSupportedBackends();
    std::cout << "Supported backends (" << backends.size() << "):" << std::endl;
    
    for (auto backend : backends) {
        std::cout << "  - " << getRenderBackendName(backend) << std::endl;
    }
    std::cout << std::endl;
    
    // Test each backend
    for (auto backend : backends) {
        std::cout << "Testing backend: " << getRenderBackendName(backend) << std::endl;
        
        if (manager.switchBackend(backend)) {
            std::cout << "  ✓ Successfully switched to " << getRenderBackendName(backend) << std::endl;
            
            // Get engine info
            auto info = manager.getEngineInfo();
            std::cout << "  Engine: " << info.name << " v" << info.version << std::endl;
            std::cout << "  Vendor: " << info.vendor << std::endl;
            
            // Test basic functionality
            auto group = manager.createGroup();
            auto transform = manager.createTransform();
            auto geometry = manager.createGeometry();
            
            if (group && transform && geometry) {
                std::cout << "  ✓ Basic object creation successful" << std::endl;
                
                // Build simple scene
                transform->addChild(geometry);
                group->addChild(transform);
                
                std::cout << "  ✓ Scene graph construction successful" << std::endl;
            } else {
                std::cout << "  ✗ Basic object creation failed" << std::endl;
            }
            
            // Get statistics
            auto stats = manager.getRenderStatistics();
            std::cout << "  Statistics: frames=" << stats.frameCount 
                      << ", memory=" << (stats.memoryUsed / 1024) << "KB" << std::endl;
            
        } else {
            std::cout << "  ✗ Failed to switch to " << getRenderBackendName(backend) << std::endl;
        }
        
        std::cout << std::endl;
    }
    
    // Demonstrate backend switching
    std::cout << "Demonstrating runtime backend switching..." << std::endl;
    
    for (int i = 0; i < 3; ++i) {
        for (auto backend : backends) {
            if (manager.switchBackend(backend)) {
                std::cout << "  Switched to " << getRenderBackendName(backend) << std::endl;
                
                // Create some objects
                auto group = manager.createGroup();
                if (group) {
                    group->setName("TestGroup_" + std::to_string(i));
                }
            }
        }
    }
    
    std::cout << std::endl;
    std::cout << "🎉 Multi-backend example completed successfully!" << std::endl;
    
    return 0;
}
