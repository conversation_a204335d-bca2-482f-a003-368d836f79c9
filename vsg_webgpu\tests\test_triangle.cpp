/* <editor-fold desc="MIT License">

Copyright(c) 2024 VSG WebGPU Extension

Permission is hereby granted, free of charge, to any person obtaining a copy of this software and associated documentation files (the "Software"), to deal in the Software without restriction, including without limitation the rights to use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of the Software, and to permit persons to whom the Software is furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.

</editor-fold> */

#include <vsg_webgpu/all.h>
#include <vsg/all.h>
#include <iostream>
#include <chrono>
#include <thread>

using namespace vsg_webgpu;

class TriangleTest
{
public:
    bool runTest()
    {
        std::cout << "=== VSG WebGPU Triangle Rendering Test ===" << std::endl;
        
        try
        {
            if (!initialize())
            {
                std::cout << "FAILED: Initialization failed" << std::endl;
                return false;
            }
            
            if (!createResources())
            {
                std::cout << "FAILED: Resource creation failed" << std::endl;
                return false;
            }
            
            if (!render())
            {
                std::cout << "FAILED: Rendering failed" << std::endl;
                return false;
            }
            
            cleanup();
            
            std::cout << "PASSED: Triangle rendering test completed successfully" << std::endl;
            return true;
        }
        catch (const std::exception& e)
        {
            std::cout << "FAILED: Exception - " << e.what() << std::endl;
            return false;
        }
    }

private:
    vsg::ref_ptr<Device> device;
    vsg::ref_ptr<Window> window;
    vsg::ref_ptr<RenderGraph> renderGraph;
    vsg::ref_ptr<RecordTraversal> recordTraversal;
    vsg::ref_ptr<vsg::Group> sceneGraph;
    
    bool initialize()
    {
        std::cout << "\n--- Initializing WebGPU ---" << std::endl;
        
        // 初始化WebGPU
        initializeWebGPU();
        
        // 创建设备
        device = Device::create();
        if (!device)
        {
            std::cout << "ERROR: Could not create device" << std::endl;
            return false;
        }
        
        // 创建窗口
        window = Window::create("VSG WebGPU Triangle Test", 800, 600);
        if (!window)
        {
            std::cout << "ERROR: Could not create window" << std::endl;
            return false;
        }
        
        window->device = device;
        
        // 在mock模式下，我们跳过实际的初始化
#if VSG_WEBGPU_USE_MOCK
        std::cout << "Mock mode: Skipping actual WebGPU initialization" << std::endl;
        return true;
#endif
        
        if (!window->create())
        {
            std::cout << "ERROR: Could not create window" << std::endl;
            return false;
        }
        
        if (!device->initialize(window->surface))
        {
            std::cout << "ERROR: Could not initialize device" << std::endl;
            return false;
        }
        
        std::cout << "WebGPU initialized successfully" << std::endl;
        return true;
    }
    
    bool createResources()
    {
        std::cout << "\n--- Creating Resources ---" << std::endl;
        
        // 创建场景图
        sceneGraph = vsg::Group::create();
        
        // 创建三角形几何体
        auto vertices = vsg::vec3Array::create({
            {0.0f, 0.5f, 0.0f},   // 顶部
            {-0.5f, -0.5f, 0.0f}, // 左下
            {0.5f, -0.5f, 0.0f}   // 右下
        });
        
        auto colors = vsg::vec3Array::create({
            {1.0f, 0.0f, 0.0f}, // 红色
            {0.0f, 1.0f, 0.0f}, // 绿色
            {0.0f, 0.0f, 1.0f}  // 蓝色
        });
        
        auto indices = vsg::ushortArray::create({0, 1, 2});
        
        // 创建几何体
        auto geometry = vsg::Geometry::create();
        geometry->arrays = {vertices, colors};
        geometry->indices = indices;
        geometry->commands = {vsg::DrawIndexed::create(3, 1, 0, 0, 0)};
        
        // 创建着色器
        std::string vertexShader = R"(
            @vertex
            fn vs_main(@location(0) position: vec3<f32>, 
                      @location(1) color: vec3<f32>) -> @builtin(position) vec4<f32> {
                return vec4<f32>(position, 1.0);
            }
        )";
        
        std::string fragmentShader = R"(
            @fragment
            fn fs_main() -> @location(0) vec4<f32> {
                return vec4<f32>(1.0, 0.0, 0.0, 1.0);
            }
        )";
        
        // 在mock模式下，我们只是创建基本的VSG对象
        sceneGraph->addChild(geometry);
        
        // 创建渲染图
        renderGraph = RenderGraph::create(device.get(), window.get());
        
        // 添加渲染通道
        renderGraph->addRenderPass("main", sceneGraph);
        
        // 创建记录遍历器
        recordTraversal = RecordTraversal::create();
        
        std::cout << "Resources created successfully" << std::endl;
        return true;
    }
    
    bool render()
    {
        std::cout << "\n--- Rendering Triangle ---" << std::endl;
        
#if VSG_WEBGPU_USE_MOCK
        std::cout << "Mock mode: Simulating rendering..." << std::endl;
        
        // 模拟渲染循环
        for (int frame = 0; frame < 5; ++frame)
        {
            std::cout << "Frame " << frame + 1 << "/5" << std::endl;
            
            // 模拟渲染时间
            std::this_thread::sleep_for(std::chrono::milliseconds(16)); // ~60 FPS
            
            // 模拟渲染统计
            auto stats = recordTraversal->getStatistics();
            std::cout << "  Draw calls: " << stats.numDrawCalls << std::endl;
            std::cout << "  Triangles: " << stats.numTriangles << std::endl;
        }
        
        std::cout << "Mock rendering completed" << std::endl;
        return true;
#endif
        
        // 实际渲染循环
        auto startTime = std::chrono::high_resolution_clock::now();
        int frameCount = 0;
        const int maxFrames = 60; // 渲染60帧
        
        while (frameCount < maxFrames && window->valid())
        {
            // 处理窗口事件
            if (!window->pollEvents())
            {
                break;
            }
            
            // 渲染帧
            renderGraph->render(recordTraversal);
            
            // 呈现
            window->present();
            
            frameCount++;
            
            // 每10帧输出一次统计信息
            if (frameCount % 10 == 0)
            {
                auto stats = recordTraversal->getStatistics();
                std::cout << "Frame " << frameCount << " - Draw calls: " << stats.numDrawCalls 
                         << ", Triangles: " << stats.numTriangles << std::endl;
            }
        }
        
        auto endTime = std::chrono::high_resolution_clock::now();
        auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(endTime - startTime);
        
        std::cout << "Rendered " << frameCount << " frames in " << duration.count() << "ms" << std::endl;
        std::cout << "Average FPS: " << (frameCount * 1000.0 / duration.count()) << std::endl;
        
        return true;
    }
    
    void cleanup()
    {
        std::cout << "\n--- Cleaning Up ---" << std::endl;
        
        recordTraversal.reset();
        renderGraph.reset();
        sceneGraph.reset();
        
        if (window)
        {
            window->destroy();
            window.reset();
        }
        
        device.reset();
        
        shutdownWebGPU();
        
        std::cout << "Cleanup completed" << std::endl;
    }
};

int main()
{
    try
    {
        TriangleTest test;
        bool success = test.runTest();
        
        return success ? 0 : 1;
    }
    catch (const std::exception& e)
    {
        std::cerr << "Fatal error: " << e.what() << std::endl;
        return 1;
    }
}
