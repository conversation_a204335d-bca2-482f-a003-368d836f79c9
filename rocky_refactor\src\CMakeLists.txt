# Rocky Render Engine Refactor - 源代码构建配置

# ========== 核心库源文件 ==========

set(ROCKY_RENDER_CORE_SOURCES
    # 核心管理
    core/RenderEngineManager.cpp
    core/RenderEngineFactory.cpp
    
    # 基础接口实现
    base/BaseSceneNode.cpp
    base/BaseRenderGraph.cpp
    base/BaseWindow.cpp
    base/BaseCamera.cpp
    base/BaseTexture.cpp
    base/BaseBuffer.cpp
    base/BaseShader.cpp
    base/BasePipeline.cpp
    
    # 访问者模式
    visitors/RenderVisitor.cpp
    visitors/UpdateVisitor.cpp
    visitors/CullVisitor.cpp
    
    # 工具类
    utils/MathUtils.cpp
    utils/ResourceManager.cpp
    utils/PerformanceProfiler.cpp
)

set(ROCKY_RENDER_CORE_HEADERS
    ${CMAKE_CURRENT_SOURCE_DIR}/../include/rocky/render/IRenderEngine.h
    ${CMAKE_CURRENT_SOURCE_DIR}/../include/rocky/render/RenderTypes.h
    ${CMAKE_CURRENT_SOURCE_DIR}/../include/rocky/render/ISceneNode.h
    ${CMAKE_CURRENT_SOURCE_DIR}/../include/rocky/render/RenderEngineManager.h
)

# ========== Mock后端源文件 ==========

if(ROCKY_BUILD_MOCK_BACKEND)
    set(ROCKY_RENDER_MOCK_SOURCES
        backends/mock/MockRenderEngine.cpp
        backends/mock/MockSceneNode.cpp
        backends/mock/MockRenderGraph.cpp
        backends/mock/MockWindow.cpp
        backends/mock/MockResources.cpp
    )
endif()

# ========== Vulkan后端源文件 ==========

if(ROCKY_BUILD_VULKAN_BACKEND AND ROCKY_HAS_VSG)
    set(ROCKY_RENDER_VULKAN_SOURCES
        backends/vulkan/VulkanRenderEngine.cpp
        backends/vulkan/VulkanSceneNode.cpp
        backends/vulkan/VulkanRenderGraph.cpp
        backends/vulkan/VulkanWindow.cpp
        backends/vulkan/VulkanResources.cpp
        backends/vulkan/VSGAdapter.cpp
    )
endif()

# ========== WebGPU后端源文件 ==========

if(ROCKY_BUILD_WEBGPU_BACKEND)
    set(ROCKY_RENDER_WEBGPU_SOURCES
        backends/webgpu/WebGPURenderEngine.cpp
        backends/webgpu/WebGPUSceneNode.cpp
        backends/webgpu/WebGPURenderGraph.cpp
        backends/webgpu/WebGPUWindow.cpp
        backends/webgpu/WebGPUResources.cpp
    )
endif()

# ========== OpenGL后端源文件 ==========

if(ROCKY_BUILD_OPENGL_BACKEND AND ROCKY_HAS_OPENGL)
    set(ROCKY_RENDER_OPENGL_SOURCES
        backends/opengl/OpenGLRenderEngine.cpp
        backends/opengl/OpenGLSceneNode.cpp
        backends/opengl/OpenGLRenderGraph.cpp
        backends/opengl/OpenGLWindow.cpp
        backends/opengl/OpenGLResources.cpp
    )
endif()

# ========== 合并所有源文件 ==========

set(ROCKY_RENDER_ALL_SOURCES
    ${ROCKY_RENDER_CORE_SOURCES}
    ${ROCKY_RENDER_MOCK_SOURCES}
    ${ROCKY_RENDER_VULKAN_SOURCES}
    ${ROCKY_RENDER_WEBGPU_SOURCES}
    ${ROCKY_RENDER_OPENGL_SOURCES}
)

# ========== 创建库目标 ==========

add_library(rocky_render ${ROCKY_RENDER_ALL_SOURCES} ${ROCKY_RENDER_CORE_HEADERS})

# 设置目标属性
set_target_properties(rocky_render PROPERTIES
    CXX_STANDARD 20
    CXX_STANDARD_REQUIRED ON
    CXX_EXTENSIONS OFF
    VERSION ${PROJECT_VERSION}
    SOVERSION ${PROJECT_VERSION_MAJOR}
)

# 导出符号定义
target_compile_definitions(rocky_render PRIVATE ROCKY_RENDER_EXPORTS)

# ========== 包含目录 ==========

target_include_directories(rocky_render
    PUBLIC
        $<BUILD_INTERFACE:${CMAKE_CURRENT_SOURCE_DIR}/../include>
        $<BUILD_INTERFACE:${CMAKE_CURRENT_BINARY_DIR}/../include>
        $<INSTALL_INTERFACE:include>
    PRIVATE
        ${CMAKE_CURRENT_SOURCE_DIR}
)

# ========== 链接依赖 ==========

# VSG依赖（Vulkan后端）
if(ROCKY_BUILD_VULKAN_BACKEND AND ROCKY_HAS_VSG)
    target_link_libraries(rocky_render PUBLIC vsg::vsg)
    target_compile_definitions(rocky_render PUBLIC ROCKY_HAS_VSG)
endif()

# OpenGL依赖
if(ROCKY_BUILD_OPENGL_BACKEND AND ROCKY_HAS_OPENGL)
    target_link_libraries(rocky_render PUBLIC OpenGL::GL)
    target_compile_definitions(rocky_render PUBLIC ROCKY_HAS_OPENGL)
endif()

# 线程支持
find_package(Threads REQUIRED)
target_link_libraries(rocky_render PUBLIC Threads::Threads)

# ========== 编译选项 ==========

# 警告设置
if(MSVC)
    target_compile_options(rocky_render PRIVATE /W4)
else()
    target_compile_options(rocky_render PRIVATE -Wall -Wextra -Wpedantic)
endif()

# 优化设置
if(CMAKE_BUILD_TYPE STREQUAL "Release")
    if(MSVC)
        target_compile_options(rocky_render PRIVATE /O2)
    else()
        target_compile_options(rocky_render PRIVATE -O3)
    endif()
endif()

# ========== 安装配置 ==========

# 安装库文件
install(TARGETS rocky_render
    EXPORT RockyRenderTargets
    LIBRARY DESTINATION lib
    ARCHIVE DESTINATION lib
    RUNTIME DESTINATION bin
    INCLUDES DESTINATION include
)

# 导出目标
install(EXPORT RockyRenderTargets
    FILE RockyRenderTargets.cmake
    NAMESPACE RockyRender::
    DESTINATION lib/cmake/RockyRender
)

# ========== 构建信息 ==========

message(STATUS "Rocky Render Library Configuration:")
message(STATUS "  Core sources: ${ROCKY_RENDER_CORE_SOURCES}")
if(ROCKY_BUILD_MOCK_BACKEND)
    message(STATUS "  Mock backend: ENABLED")
endif()
if(ROCKY_BUILD_VULKAN_BACKEND AND ROCKY_HAS_VSG)
    message(STATUS "  Vulkan backend: ENABLED")
endif()
if(ROCKY_BUILD_WEBGPU_BACKEND)
    message(STATUS "  WebGPU backend: ENABLED")
endif()
if(ROCKY_BUILD_OPENGL_BACKEND AND ROCKY_HAS_OPENGL)
    message(STATUS "  OpenGL backend: ENABLED")
endif()
