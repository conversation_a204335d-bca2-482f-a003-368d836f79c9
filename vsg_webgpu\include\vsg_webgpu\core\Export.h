#pragma once

/* <editor-fold desc="MIT License">

Copyright(c) 2024 VSG WebGPU Extension

Permission is hereby granted, free of charge, to any person obtaining a copy of this software and associated documentation files (the "Software"), to deal in the Software without restriction, including without limitation the rights to use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of the Software, and to permit persons to whom the Software is furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.

</editor-fold> */

// 导出宏定义
#ifdef _WIN32
    #ifdef VSG_WEBGPU_EXPORTS
        #define VSG_WEBGPU_DECLSPEC __declspec(dllexport)
    #else
        #define VSG_WEBGPU_DECLSPEC __declspec(dllimport)
    #endif
#else
    #define VSG_WEBGPU_DECLSPEC
#endif

// 版本信息
#define VSG_WEBGPU_VERSION_MAJOR 1
#define VSG_WEBGPU_VERSION_MINOR 0
#define VSG_WEBGPU_VERSION_PATCH 0

// 平台检测
#ifdef EMSCRIPTEN
    #define VSG_WEBGPU_PLATFORM_EMSCRIPTEN 1
#else
    #define VSG_WEBGPU_PLATFORM_EMSCRIPTEN 0
#endif

#ifdef VSG_WEBGPU_MOCK_IMPLEMENTATION
    #define VSG_WEBGPU_USE_MOCK 1
#else
    #define VSG_WEBGPU_USE_MOCK 0
#endif

// 调试宏
#ifdef _DEBUG
    #define VSG_WEBGPU_DEBUG 1
#else
    #define VSG_WEBGPU_DEBUG 0
#endif

// 日志宏
#include <iostream>
#include <vsg/io/Logger.h>

#define VSG_WEBGPU_LOG_INFO(msg) vsg::info(msg)
#define VSG_WEBGPU_LOG_WARN(msg) vsg::warn(msg)
#define VSG_WEBGPU_LOG_ERROR(msg) vsg::warn("ERROR: ", msg)

#if VSG_WEBGPU_DEBUG
    #define VSG_WEBGPU_LOG_DEBUG(msg) vsg::debug(msg)
#else
    #define VSG_WEBGPU_LOG_DEBUG(msg)
#endif
