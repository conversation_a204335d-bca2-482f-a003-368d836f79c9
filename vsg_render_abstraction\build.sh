#!/bin/bash

# VSG渲染引擎抽象层构建脚本
# 支持Linux和macOS平台的自动化构建

set -e  # 遇到错误时退出

echo "========================================"
echo "VSG渲染引擎抽象层构建脚本"
echo "========================================"
echo

# 默认参数
BUILD_TYPE="Release"
BUILD_DIR="build"
INSTALL_DIR="install"
CLEAN_BUILD=0
JOBS=$(nproc 2>/dev/null || sysctl -n hw.ncpu 2>/dev/null || echo 4)

# 解析命令行参数
show_help() {
    echo "用法: $0 [选项]"
    echo
    echo "选项:"
    echo "  --debug     构建Debug版本 (默认: Release)"
    echo "  --clean     清理后重新构建"
    echo "  --jobs N    并行构建任务数 (默认: $JOBS)"
    echo "  --help      显示此帮助信息"
    echo
    echo "示例:"
    echo "  $0                构建Release版本"
    echo "  $0 --debug       构建Debug版本"
    echo "  $0 --clean       清理后构建"
    echo "  $0 --jobs 8      使用8个并行任务构建"
    echo
}

while [[ $# -gt 0 ]]; do
    case $1 in
        --debug)
            BUILD_TYPE="Debug"
            shift
            ;;
        --clean)
            CLEAN_BUILD=1
            shift
            ;;
        --jobs)
            JOBS="$2"
            shift 2
            ;;
        --help)
            show_help
            exit 0
            ;;
        *)
            echo "未知选项: $1"
            show_help
            exit 1
            ;;
    esac
done

# 检查依赖
check_dependencies() {
    echo "检查构建依赖..."
    
    # 检查CMake
    if ! command -v cmake &> /dev/null; then
        echo "错误: 未找到CMake，请安装CMake 3.20或更高版本"
        exit 1
    fi
    
    CMAKE_VERSION=$(cmake --version | head -n1 | cut -d' ' -f3)
    echo "  CMake版本: $CMAKE_VERSION"
    
    # 检查编译器
    if command -v g++ &> /dev/null; then
        GCC_VERSION=$(g++ --version | head -n1)
        echo "  编译器: $GCC_VERSION"
    elif command -v clang++ &> /dev/null; then
        CLANG_VERSION=$(clang++ --version | head -n1)
        echo "  编译器: $CLANG_VERSION"
    else
        echo "错误: 未找到C++编译器 (g++或clang++)"
        exit 1
    fi
    
    # 检查VSG (可选)
    if pkg-config --exists vsg 2>/dev/null; then
        VSG_VERSION=$(pkg-config --modversion vsg)
        echo "  VSG版本: $VSG_VERSION"
    else
        echo "  VSG: 未找到 (将禁用VSG后端)"
    fi
    
    echo "依赖检查完成"
    echo
}

# 主构建函数
main() {
    echo "构建配置:"
    echo "  构建类型: $BUILD_TYPE"
    echo "  构建目录: $BUILD_DIR"
    echo "  安装目录: $INSTALL_DIR"
    echo "  并行任务: $JOBS"
    echo
    
    # 检查依赖
    check_dependencies
    
    # 清理构建目录（如果需要）
    if [[ $CLEAN_BUILD -eq 1 ]]; then
        echo "清理构建目录..."
        rm -rf "$BUILD_DIR" "$INSTALL_DIR"
        echo "清理完成"
        echo
    fi
    
    # 创建构建目录
    mkdir -p "$BUILD_DIR"
    cd "$BUILD_DIR"
    
    echo "配置CMake..."
    cmake .. \
        -DCMAKE_BUILD_TYPE="$BUILD_TYPE" \
        -DCMAKE_INSTALL_PREFIX="../$INSTALL_DIR" \
        -DVSG_ABSTRACTION_BUILD_VSG_BACKEND=ON \
        -DVSG_ABSTRACTION_BUILD_WEBGPU_BACKEND=OFF \
        -DVSG_ABSTRACTION_BUILD_OPENGL_BACKEND=OFF \
        -DVSG_ABSTRACTION_BUILD_MOCK_BACKEND=ON \
        -DVSG_ABSTRACTION_BUILD_TESTS=ON \
        -DVSG_ABSTRACTION_BUILD_EXAMPLES=ON \
        -DVSG_ABSTRACTION_BUILD_SHARED_LIBS=ON
    
    echo
    echo "开始构建..."
    cmake --build . --config "$BUILD_TYPE" --parallel "$JOBS"
    
    echo
    echo "运行测试..."
    if ! ctest --output-on-failure --build-config "$BUILD_TYPE"; then
        echo "警告: 部分测试失败"
    fi
    
    echo
    echo "安装库文件..."
    cmake --install . --config "$BUILD_TYPE"
    
    # 返回根目录
    cd ..
    
    echo
    echo "========================================"
    echo "构建完成!"
    echo "========================================"
    echo
    echo "构建输出:"
    echo "  库文件: $BUILD_DIR/lib/"
    echo "  可执行文件: $BUILD_DIR/bin/"
    echo "  测试程序: $BUILD_DIR/tests/"
    echo "  示例程序: $BUILD_DIR/examples/"
    echo
    echo "安装输出:"
    echo "  头文件: $INSTALL_DIR/include/"
    echo "  库文件: $INSTALL_DIR/lib/"
    echo "  可执行文件: $INSTALL_DIR/bin/"
    echo
    
    # 显示可用的示例程序
    echo "可用的示例程序:"
    for example in "$BUILD_DIR/examples"/*; do
        if [[ -x "$example" && -f "$example" ]]; then
            echo "  $example"
        fi
    done
    
    echo
    echo "运行示例:"
    echo "  cd $BUILD_DIR/examples"
    echo "  ./basic_example"
    echo
    
    # 设置库路径提示
    if [[ "$OSTYPE" == "linux-gnu"* ]]; then
        echo "注意: 如果运行时找不到共享库，请设置LD_LIBRARY_PATH:"
        echo "  export LD_LIBRARY_PATH=\$PWD/$BUILD_DIR/lib:\$LD_LIBRARY_PATH"
    elif [[ "$OSTYPE" == "darwin"* ]]; then
        echo "注意: 如果运行时找不到共享库，请设置DYLD_LIBRARY_PATH:"
        echo "  export DYLD_LIBRARY_PATH=\$PWD/$BUILD_DIR/lib:\$DYLD_LIBRARY_PATH"
    fi
    echo
}

# 错误处理
trap 'echo "构建失败!"; exit 1' ERR

# 运行主函数
main "$@"
