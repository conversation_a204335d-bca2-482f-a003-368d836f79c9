#pragma once

/* <editor-fold desc="MIT License">

Copyright(c) 2024 Rocky Render Engine Refactor

Permission is hereby granted, free of charge, to any person obtaining a copy of this software and associated documentation files (the "Software"), to deal in the Software without restriction, including without limitation the rights to use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of the Software, and to permit persons to whom the Software is furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.

</editor-fold> */

#include <memory>
#include <vector>
#include <string>
#include <rocky/render/RenderTypes.h>

namespace rocky {
namespace render {

// 前向声明
class IVisitor;
class ITexture;
class IBuffer;
class IPipeline;

/**
 * @brief 场景节点类型
 */
enum class NodeType {
    Group,          ///< 组节点
    Transform,      ///< 变换节点
    Geometry,       ///< 几何节点
    StateGroup,     ///< 状态组节点
    LOD,            ///< 细节层次节点
    Switch,         ///< 开关节点
    Billboard,      ///< 广告牌节点
    Light,          ///< 光源节点
    Camera          ///< 相机节点
};

/**
 * @brief 场景节点抽象接口
 * 
 * 这是场景图中所有节点的基类，定义了场景图的基本操作。
 * 采用访问者模式支持不同的遍历操作。
 */
class ISceneNode {
public:
    virtual ~ISceneNode() = default;

    // ========== 基本属性 ==========
    
    /**
     * @brief 获取节点类型
     */
    virtual NodeType getNodeType() const = 0;
    
    /**
     * @brief 获取节点名称
     */
    virtual const std::string& getName() const = 0;
    
    /**
     * @brief 设置节点名称
     */
    virtual void setName(const std::string& name) = 0;
    
    /**
     * @brief 获取节点ID
     */
    virtual uint64_t getNodeId() const = 0;

    // ========== 层次结构管理 ==========
    
    /**
     * @brief 添加子节点
     * @param child 要添加的子节点
     */
    virtual void addChild(std::shared_ptr<ISceneNode> child) = 0;
    
    /**
     * @brief 移除子节点
     * @param child 要移除的子节点
     */
    virtual void removeChild(std::shared_ptr<ISceneNode> child) = 0;
    
    /**
     * @brief 移除指定索引的子节点
     * @param index 子节点索引
     */
    virtual void removeChild(size_t index) = 0;
    
    /**
     * @brief 移除所有子节点
     */
    virtual void removeAllChildren() = 0;
    
    /**
     * @brief 获取子节点数量
     */
    virtual size_t getChildCount() const = 0;
    
    /**
     * @brief 获取指定索引的子节点
     * @param index 子节点索引
     */
    virtual std::shared_ptr<ISceneNode> getChild(size_t index) const = 0;
    
    /**
     * @brief 获取所有子节点
     */
    virtual const std::vector<std::shared_ptr<ISceneNode>>& getChildren() const = 0;
    
    /**
     * @brief 获取父节点
     */
    virtual std::weak_ptr<ISceneNode> getParent() const = 0;

    // ========== 变换操作 ==========
    
    /**
     * @brief 设置局部变换矩阵
     * @param matrix 4x4变换矩阵
     */
    virtual void setTransform(const Mat4& matrix) = 0;
    
    /**
     * @brief 获取局部变换矩阵
     */
    virtual const Mat4& getTransform() const = 0;
    
    /**
     * @brief 获取世界变换矩阵
     */
    virtual Mat4 getWorldTransform() const = 0;
    
    /**
     * @brief 设置位置
     * @param position 位置向量
     */
    virtual void setPosition(const Vec3& position) = 0;
    
    /**
     * @brief 获取位置
     */
    virtual Vec3 getPosition() const = 0;
    
    /**
     * @brief 设置旋转（四元数）
     * @param rotation 旋转四元数 [x, y, z, w]
     */
    virtual void setRotation(const Vec4& rotation) = 0;
    
    /**
     * @brief 获取旋转
     */
    virtual Vec4 getRotation() const = 0;
    
    /**
     * @brief 设置缩放
     * @param scale 缩放向量
     */
    virtual void setScale(const Vec3& scale) = 0;
    
    /**
     * @brief 获取缩放
     */
    virtual Vec3 getScale() const = 0;

    // ========== 可见性和状态 ==========
    
    /**
     * @brief 设置可见性
     * @param visible 是否可见
     */
    virtual void setVisible(bool visible) = 0;
    
    /**
     * @brief 获取可见性
     */
    virtual bool isVisible() const = 0;
    
    /**
     * @brief 设置启用状态
     * @param enabled 是否启用
     */
    virtual void setEnabled(bool enabled) = 0;
    
    /**
     * @brief 获取启用状态
     */
    virtual bool isEnabled() const = 0;

    // ========== 包围盒 ==========
    
    /**
     * @brief 获取局部包围盒
     * @return 包围盒 [min_x, min_y, min_z, max_x, max_y, max_z]
     */
    virtual std::array<float, 6> getLocalBounds() const = 0;
    
    /**
     * @brief 获取世界包围盒
     */
    virtual std::array<float, 6> getWorldBounds() const = 0;
    
    /**
     * @brief 计算包围盒（递归计算所有子节点）
     */
    virtual void computeBounds() = 0;

    // ========== 访问者模式 ==========
    
    /**
     * @brief 接受访问者
     * @param visitor 访问者对象
     */
    virtual void accept(IVisitor& visitor) = 0;
    
    /**
     * @brief 遍历子节点
     * @param visitor 访问者对象
     */
    virtual void traverse(IVisitor& visitor) = 0;

    // ========== 用户数据 ==========
    
    /**
     * @brief 设置用户数据
     * @param key 数据键
     * @param data 数据指针
     */
    virtual void setUserData(const std::string& key, void* data) = 0;
    
    /**
     * @brief 获取用户数据
     * @param key 数据键
     */
    virtual void* getUserData(const std::string& key) const = 0;
    
    /**
     * @brief 移除用户数据
     * @param key 数据键
     */
    virtual void removeUserData(const std::string& key) = 0;

    // ========== 原生对象访问 ==========
    
    /**
     * @brief 获取原生场景节点对象（用于高级用法）
     * @return 原生对象指针，类型取决于具体实现
     */
    virtual void* getNativeHandle() const = 0;
};

/**
 * @brief 几何节点接口
 * 
 * 扩展场景节点，添加几何体相关的功能
 */
class IGeometryNode : public ISceneNode {
public:
    /**
     * @brief 设置几何数据
     * @param geometry 几何数据
     */
    virtual void setGeometry(const GeometryData& geometry) = 0;
    
    /**
     * @brief 获取几何数据
     */
    virtual const GeometryData& getGeometry() const = 0;
    
    /**
     * @brief 设置顶点缓冲区
     * @param buffer 顶点缓冲区
     */
    virtual void setVertexBuffer(std::shared_ptr<IBuffer> buffer) = 0;
    
    /**
     * @brief 设置索引缓冲区
     * @param buffer 索引缓冲区
     */
    virtual void setIndexBuffer(std::shared_ptr<IBuffer> buffer) = 0;
    
    /**
     * @brief 设置渲染管线
     * @param pipeline 渲染管线
     */
    virtual void setPipeline(std::shared_ptr<IPipeline> pipeline) = 0;
    
    /**
     * @brief 添加纹理
     * @param slot 纹理槽位
     * @param texture 纹理对象
     */
    virtual void setTexture(uint32_t slot, std::shared_ptr<ITexture> texture) = 0;
};

/**
 * @brief 状态组节点接口
 * 
 * 扩展场景节点，添加渲染状态管理功能
 */
class IStateGroupNode : public ISceneNode {
public:
    /**
     * @brief 设置渲染管线
     * @param pipeline 渲染管线
     */
    virtual void setPipeline(std::shared_ptr<IPipeline> pipeline) = 0;
    
    /**
     * @brief 获取渲染管线
     */
    virtual std::shared_ptr<IPipeline> getPipeline() const = 0;
    
    /**
     * @brief 设置纹理
     * @param slot 纹理槽位
     * @param texture 纹理对象
     */
    virtual void setTexture(uint32_t slot, std::shared_ptr<ITexture> texture) = 0;
    
    /**
     * @brief 获取纹理
     * @param slot 纹理槽位
     */
    virtual std::shared_ptr<ITexture> getTexture(uint32_t slot) const = 0;
    
    /**
     * @brief 设置统一缓冲区
     * @param slot 缓冲区槽位
     * @param buffer 统一缓冲区
     */
    virtual void setUniformBuffer(uint32_t slot, std::shared_ptr<IBuffer> buffer) = 0;
};

} // namespace render
} // namespace rocky
