/* <editor-fold desc="MIT License">

Copyright(c) 2024 VSG Render Engine Abstraction

Permission is hereby granted, free of charge, to any person obtaining a copy of this software and associated documentation files (the "Software"), to deal in the Software without restriction, including without limitation the rights to use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of the Software, and to permit persons to whom the Software is furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.

</editor-fold> */

#include <vsg_abstraction/core/RenderEngineManager.h>
#include <vsg_abstraction/core/RenderEngineFactory.h>
#include <vsg_abstraction/backends/VSGRenderEngine.h>

#include <iostream>
#include <cassert>

using namespace vsg_abstraction;

void testVSGEngineCreation()
{
    std::cout << "=== VSG Engine Creation Test ===" << std::endl;
    
    // 测试VSG引擎创建
    auto engine = RenderEngineFactory::create(RenderBackend::Vulkan);
    assert(engine != nullptr);
    
    // 验证引擎类型
    assert(engine->getBackendType() == RenderBackend::Vulkan);
    
    // 获取引擎信息
    auto info = engine->getEngineInfo();
    std::cout << "Engine Name: " << info.name << std::endl;
    std::cout << "Engine Version: " << info.version << std::endl;
    std::cout << "Engine Vendor: " << info.vendor << std::endl;
    
    std::cout << "✅ VSG Engine Creation Test Passed" << std::endl;
}

void testVSGEngineInitialization()
{
    std::cout << "=== VSG Engine Initialization Test ===" << std::endl;
    
    auto engine = RenderEngineFactory::create(RenderBackend::Vulkan);
    assert(engine != nullptr);
    
    // 测试初始化
    bool initialized = engine->initialize();
    if (initialized)
    {
        std::cout << "✅ VSG Engine initialized successfully" << std::endl;
        
        // 测试设备获取
        auto device = engine->getDevice();
        if (device)
        {
            std::cout << "✅ VSG Device obtained successfully" << std::endl;
        }
        
        // 测试实例获取
        auto instance = engine->getInstance();
        if (instance)
        {
            std::cout << "✅ VSG Instance obtained successfully" << std::endl;
        }
        
        // 测试物理设备获取
        auto physicalDevices = engine->getPhysicalDevices();
        std::cout << "Physical devices found: " << physicalDevices.size() << std::endl;
        
        engine->shutdown();
        std::cout << "✅ VSG Engine shutdown successfully" << std::endl;
    }
    else
    {
        std::cout << "⚠️ VSG Engine initialization failed (may be expected if no Vulkan support)" << std::endl;
    }
    
    std::cout << "✅ VSG Engine Initialization Test Completed" << std::endl;
}

void testVSGSceneGraphCreation()
{
    std::cout << "=== VSG Scene Graph Creation Test ===" << std::endl;
    
    auto engine = RenderEngineFactory::create(RenderBackend::Vulkan);
    assert(engine != nullptr);
    
    if (engine->initialize())
    {
        // 测试场景图节点创建
        auto group = engine->createGroup();
        assert(group != nullptr);
        std::cout << "✅ Group created successfully" << std::endl;
        
        auto transform = engine->createTransform();
        assert(transform != nullptr);
        std::cout << "✅ Transform created successfully" << std::endl;
        
        auto geometry = engine->createGeometry();
        assert(geometry != nullptr);
        std::cout << "✅ Geometry created successfully" << std::endl;
        
        auto stateGroup = engine->createStateGroup();
        assert(stateGroup != nullptr);
        std::cout << "✅ StateGroup created successfully" << std::endl;
        
        engine->shutdown();
    }
    else
    {
        std::cout << "⚠️ Skipping scene graph test due to initialization failure" << std::endl;
    }
    
    std::cout << "✅ VSG Scene Graph Creation Test Completed" << std::endl;
}

void testVSGCompatibilityInterface()
{
    std::cout << "=== VSG Compatibility Interface Test ===" << std::endl;
    
    auto engine = RenderEngineFactory::create(RenderBackend::Vulkan);
    assert(engine != nullptr);
    
    if (engine->initialize())
    {
        // 测试VSG兼容性接口
        auto vsgGroup = engine->createVSGGroup();
        if (vsgGroup)
        {
            std::cout << "✅ VSG Group created successfully" << std::endl;
        }
        
        auto vsgTransform = engine->createVSGTransform();
        if (vsgTransform)
        {
            std::cout << "✅ VSG Transform created successfully" << std::endl;
        }
        
        auto vsgGeometry = engine->createVSGGeometry();
        if (vsgGeometry)
        {
            std::cout << "✅ VSG Geometry created successfully" << std::endl;
        }
        
        auto vsgStateGroup = engine->createVSGStateGroup();
        if (vsgStateGroup)
        {
            std::cout << "✅ VSG StateGroup created successfully" << std::endl;
        }
        
        // 测试VSG对象获取
        auto vsgDevice = engine->getVSGDevice();
        if (vsgDevice)
        {
            std::cout << "✅ VSG Device obtained successfully" << std::endl;
        }
        
        engine->shutdown();
    }
    else
    {
        std::cout << "⚠️ Skipping compatibility test due to initialization failure" << std::endl;
    }
    
    std::cout << "✅ VSG Compatibility Interface Test Completed" << std::endl;
}

void testVSGRenderStatistics()
{
    std::cout << "=== VSG Render Statistics Test ===" << std::endl;
    
    auto engine = RenderEngineFactory::create(RenderBackend::Vulkan);
    assert(engine != nullptr);
    
    if (engine->initialize())
    {
        // 测试统计信息
        auto stats = engine->getRenderStatistics();
        std::cout << "Frame count: " << stats.frameCount << std::endl;
        std::cout << "Memory used: " << stats.memoryUsed << " bytes" << std::endl;
        
        // 重置统计信息
        engine->resetRenderStatistics();
        auto resetStats = engine->getRenderStatistics();
        assert(resetStats.frameCount == 0);
        std::cout << "✅ Statistics reset successfully" << std::endl;
        
        engine->shutdown();
    }
    else
    {
        std::cout << "⚠️ Skipping statistics test due to initialization failure" << std::endl;
    }
    
    std::cout << "✅ VSG Render Statistics Test Completed" << std::endl;
}

int main()
{
    std::cout << "Starting VSG Compatibility Tests..." << std::endl;
    std::cout << "========================================" << std::endl;
    
    try
    {
        testVSGEngineCreation();
        testVSGEngineInitialization();
        testVSGSceneGraphCreation();
        testVSGCompatibilityInterface();
        testVSGRenderStatistics();
        
        std::cout << "========================================" << std::endl;
        std::cout << "🎉 All VSG Compatibility Tests Completed Successfully!" << std::endl;
        return 0;
    }
    catch (const std::exception& e)
    {
        std::cerr << "❌ Test failed with exception: " << e.what() << std::endl;
        return 1;
    }
    catch (...)
    {
        std::cerr << "❌ Test failed with unknown exception" << std::endl;
        return 1;
    }
}
