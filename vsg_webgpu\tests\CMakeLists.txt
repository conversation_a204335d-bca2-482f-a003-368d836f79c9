# VSG WebGPU测试程序

# 基础测试程序
add_executable(test_device
    test_device.cpp
)

target_link_libraries(test_device
    PRIVATE
        vsg_webgpu
        vsg::vsg
)

# 渲染测试程序
add_executable(test_triangle
    test_triangle.cpp
)

target_link_libraries(test_triangle
    PRIVATE
        vsg_webgpu
        vsg::vsg
)

# 状态管理测试
add_executable(test_state
    test_state.cpp
)

target_link_libraries(test_state
    PRIVATE
        vsg_webgpu
        vsg::vsg
)

# 管线测试
add_executable(test_pipeline
    test_pipeline.cpp
)

target_link_libraries(test_pipeline
    PRIVATE
        vsg_webgpu
        vsg::vsg
)

# 场景图测试
add_executable(test_scenegraph
    test_scenegraph.cpp
)

target_link_libraries(test_scenegraph
    PRIVATE
        vsg_webgpu
        vsg::vsg
)

# 性能测试
add_executable(test_performance
    test_performance.cpp
)

target_link_libraries(test_performance
    PRIVATE
        vsg_webgpu
        vsg::vsg
)

# 添加测试
add_test(NAME DeviceTest COMMAND test_device)
add_test(NAME TriangleTest COMMAND test_triangle)
add_test(NAME StateTest COMMAND test_state)
add_test(NAME PipelineTest COMMAND test_pipeline)
add_test(NAME SceneGraphTest COMMAND test_scenegraph)
add_test(NAME PerformanceTest COMMAND test_performance)

# 设置测试属性
set_tests_properties(DeviceTest PROPERTIES TIMEOUT 30)
set_tests_properties(TriangleTest PROPERTIES TIMEOUT 60)
set_tests_properties(StateTest PROPERTIES TIMEOUT 30)
set_tests_properties(PipelineTest PROPERTIES TIMEOUT 30)
set_tests_properties(SceneGraphTest PROPERTIES TIMEOUT 60)
set_tests_properties(PerformanceTest PROPERTIES TIMEOUT 120)

# 复制测试资源
if(EXISTS ${CMAKE_CURRENT_SOURCE_DIR}/data)
    file(COPY ${CMAKE_CURRENT_SOURCE_DIR}/data 
         DESTINATION ${CMAKE_CURRENT_BINARY_DIR})
endif()

# Emscripten特定配置
if(VSG_WEBGPU_USE_EMSCRIPTEN)
    # 为Emscripten构建配置HTML文件
    foreach(target test_device test_triangle test_state test_pipeline test_scenegraph test_performance)
        set_target_properties(${target} PROPERTIES
            SUFFIX ".html"
            LINK_FLAGS "-sUSE_WEBGPU=1 -sASYNCIFY -sALLOW_MEMORY_GROWTH=1 --shell-file ${CMAKE_CURRENT_SOURCE_DIR}/shell.html"
        )
    endforeach()
endif()

message(STATUS "VSG WebGPU Tests configured:")
message(STATUS "  Device Test: test_device")
message(STATUS "  Triangle Test: test_triangle")
message(STATUS "  State Test: test_state")
message(STATUS "  Pipeline Test: test_pipeline")
message(STATUS "  Scene Graph Test: test_scenegraph")
message(STATUS "  Performance Test: test_performance")
