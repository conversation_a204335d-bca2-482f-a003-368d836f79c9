/* <editor-fold desc="MIT License">

Copyright(c) 2024 VSG Render Engine Abstraction

Permission is hereby granted, free of charge, to any person obtaining a copy of this software and associated documentation files (the "Software"), to deal in the Software without restriction, including without limitation the rights to use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of the Software, and to permit persons to whom the Software is furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.

</editor-fold> */

#include <iostream>
#include <mutex>
#include <vsg_abstraction/core/IDevice.h>
#include <vsg_abstraction/core/RenderEngineFactory.h>
#include <vsg_abstraction/core/RenderEngineManager.h>

namespace vsg_abstraction
{

    // ========== RenderEngineManager Implementation ==========

    RenderEngineManager::RenderEngineManager()
    {
        std::cout << "[VSG_ABSTRACTION][DEBUG] RenderEngineManager created" << std::endl;
    }

    RenderEngineManager::~RenderEngineManager()
    {
        shutdown();
        std::cout << "[VSG_ABSTRACTION][DEBUG] RenderEngineManager destroyed" << std::endl;
    }

    RenderEngineManager& RenderEngineManager::instance()
    {
        static RenderEngineManager instance;
        return instance;
    }

    bool RenderEngineManager::initialize()
    {
        std::lock_guard<std::mutex> lock(mutex_);

        if (initialized_)
        {
            return true;
        }

        // 如果没有当前引擎，尝试创建默认引擎
        if (!engine_)
        {
            if (!switchBackend(RenderBackend::Mock))
            {
                _handleError("Failed to initialize with Mock backend");
                return false;
            }
        }

        initialized_ = true;
        std::cout << "[VSG_ABSTRACTION][DEBUG] RenderEngineManager initialized" << std::endl;
        return true;
    }

    void RenderEngineManager::shutdown()
    {
        std::lock_guard<std::mutex> lock(mutex_);

        if (engine_)
        {
            engine_->shutdown();
            engine_.reset();
        }

        initialized_ = false;
        std::cout << "[VSG_ABSTRACTION][DEBUG] RenderEngineManager shutdown" << std::endl;
    }

    bool RenderEngineManager::switchBackend(RenderBackend backend, const void* config)
    {
        std::lock_guard<std::mutex> lock(mutex_);

        // 如果已经是当前后端，直接返回成功
        if (engine_ && currentBackend_ == backend)
        {
            return true;
        }

        // 关闭当前引擎
        if (engine_)
        {
            engine_->shutdown();
            engine_.reset();
        }

        // 创建新引擎
        try
        {
            engine_ = RenderEngineFactory::create(backend, static_cast<const RenderEngineConfig*>(config));
            if (!engine_)
            {
                _handleError("Failed to create engine for backend: " + getRenderBackendName(backend));
                return false;
            }

            // 初始化新引擎
            if (!engine_->initialize())
            {
                _handleError("Failed to initialize engine for backend: " + getRenderBackendName(backend));
                engine_.reset();
                return false;
            }

            // 设置回调
            if (errorCallback_)
            {
                engine_->setErrorCallback(errorCallback_);
            }
            if (debugCallback_)
            {
                engine_->setDebugCallback(debugCallback_);
            }

            currentBackend_ = backend;
            std::cout << "[VSG_ABSTRACTION][DEBUG] Switched to backend: " << getRenderBackendName(backend) << std::endl;

            // 通知引擎变更
            _notifyEngineChange(currentBackend_, backend);

            return true;
        }
        catch (const std::exception& e)
        {
            _handleError("Exception while switching backend: " + std::string(e.what()));
            return false;
        }
    }

    bool RenderEngineManager::hasEngine() const
    {
        std::lock_guard<std::mutex> lock(mutex_);
        return engine_ != nullptr;
    }

    IRenderEngine* RenderEngineManager::getEngine() const
    {
        std::lock_guard<std::mutex> lock(mutex_);
        return engine_.get();
    }

    RenderBackend RenderEngineManager::getCurrentBackend() const
    {
        std::lock_guard<std::mutex> lock(mutex_);
        return currentBackend_;
    }

    // ========== 场景图创建方法 ==========

    ref_ptr<IGroup> RenderEngineManager::createGroup()
    {
        std::lock_guard<std::mutex> lock(mutex_);
        _checkEngine();
        return engine_->createGroup();
    }

    ref_ptr<ITransform> RenderEngineManager::createTransform()
    {
        std::lock_guard<std::mutex> lock(mutex_);
        _checkEngine();
        return engine_->createTransform();
    }

    ref_ptr<ITransform> RenderEngineManager::createMatrixTransform()
    {
        std::lock_guard<std::mutex> lock(mutex_);
        _checkEngine();
        return engine_->createMatrixTransform();
    }

    ref_ptr<IGeometry> RenderEngineManager::createGeometry()
    {
        std::lock_guard<std::mutex> lock(mutex_);
        _checkEngine();
        return engine_->createGeometry();
    }

    ref_ptr<IStateGroup> RenderEngineManager::createStateGroup()
    {
        std::lock_guard<std::mutex> lock(mutex_);
        _checkEngine();
        return engine_->createStateGroup();
    }

    // ========== 窗口和渲染管理 ==========

    ref_ptr<IWindow> RenderEngineManager::createWindow(const WindowTraits& traits)
    {
        std::lock_guard<std::mutex> lock(mutex_);
        _checkEngine();
        return engine_->createWindow(traits);
    }

    ref_ptr<IRenderGraph> RenderEngineManager::createRenderGraph(ref_ptr<IWindow> window, ref_ptr<INode> view)
    {
        std::lock_guard<std::mutex> lock(mutex_);
        _checkEngine();
        return engine_->createRenderGraph(window, view);
    }

    ref_ptr<IRecordTraversal> RenderEngineManager::createRecordTraversal()
    {
        std::lock_guard<std::mutex> lock(mutex_);
        _checkEngine();
        return engine_->createRecordTraversal();
    }

    // ========== 同步和性能 ==========

    void RenderEngineManager::waitIdle()
    {
        std::lock_guard<std::mutex> lock(mutex_);
        if (engine_)
        {
            engine_->waitIdle();
        }
    }

    RenderStatistics RenderEngineManager::getRenderStatistics() const
    {
        std::lock_guard<std::mutex> lock(mutex_);
        if (engine_)
        {
            return engine_->getRenderStatistics();
        }
        return statistics_;
    }

    void RenderEngineManager::resetRenderStatistics()
    {
        std::lock_guard<std::mutex> lock(mutex_);
        if (engine_)
        {
            engine_->resetRenderStatistics();
        }
        statistics_ = RenderStatistics{};
    }

    EngineInfo RenderEngineManager::getEngineInfo() const
    {
        std::lock_guard<std::mutex> lock(mutex_);
        if (engine_)
        {
            return engine_->getEngineInfo();
        }
        return EngineInfo{};
    }

    DeviceCapabilities RenderEngineManager::getDeviceCapabilities() const
    {
        std::lock_guard<std::mutex> lock(mutex_);
        if (engine_)
        {
            auto physicalDevices = engine_->getPhysicalDevices();
            if (!physicalDevices.empty())
            {
                return physicalDevices[0]->getCapabilities();
            }
        }
        return DeviceCapabilities{};
    }

    // ========== 调试和诊断 ==========

    void RenderEngineManager::setErrorCallback(ErrorCallback callback)
    {
        std::lock_guard<std::mutex> lock(mutex_);
        errorCallback_ = callback;
        if (engine_)
        {
            engine_->setErrorCallback(callback);
        }
    }

    void RenderEngineManager::setDebugCallback(DebugCallback callback)
    {
        std::lock_guard<std::mutex> lock(mutex_);
        debugCallback_ = callback;
        if (engine_)
        {
            engine_->setDebugCallback(callback);
        }
    }

    void RenderEngineManager::beginProfileMarker(const std::string& name)
    {
        std::lock_guard<std::mutex> lock(mutex_);
        if (engine_)
        {
            engine_->beginDebugMarker(name);
        }
    }

    void RenderEngineManager::endProfileMarker()
    {
        std::lock_guard<std::mutex> lock(mutex_);
        if (engine_)
        {
            engine_->endDebugMarker();
        }
    }

#ifdef VSG_ABSTRACTION_HAS_VSG
    // ========== VSG兼容性接口实现 ==========

    vsg::ref_ptr<vsg::Group> RenderEngineManager::createVSGGroup()
    {
        std::lock_guard<std::mutex> lock(mutex_);
        _checkEngine();
        return engine_->createVSGGroup();
    }

    vsg::ref_ptr<vsg::MatrixTransform> RenderEngineManager::createVSGTransform()
    {
        std::lock_guard<std::mutex> lock(mutex_);
        _checkEngine();
        return engine_->createVSGTransform();
    }

    vsg::ref_ptr<vsg::Geometry> RenderEngineManager::createVSGGeometry()
    {
        std::lock_guard<std::mutex> lock(mutex_);
        _checkEngine();
        return engine_->createVSGGeometry();
    }

    vsg::ref_ptr<vsg::StateGroup> RenderEngineManager::createVSGStateGroup()
    {
        std::lock_guard<std::mutex> lock(mutex_);
        _checkEngine();
        return engine_->createVSGStateGroup();
    }

    vsg::ref_ptr<vsg::Device> RenderEngineManager::getVSGDevice()
    {
        std::lock_guard<std::mutex> lock(mutex_);
        _checkEngine();
        return engine_->getVSGDevice();
    }

    vsg::ref_ptr<vsg::Instance> RenderEngineManager::getVSGInstance()
    {
        std::lock_guard<std::mutex> lock(mutex_);
        _checkEngine();
        return engine_->getVSGInstance();
    }
#endif

    // ========== 内部方法 ==========

    void RenderEngineManager::_notifyEngineChange(RenderBackend oldBackend, RenderBackend newBackend)
    {
        if (engineChangeCallback_)
        {
            engineChangeCallback_(oldBackend, newBackend);
        }
    }

    void RenderEngineManager::_handleError(const std::string& error)
    {
        if (errorCallback_)
        {
            errorCallback_(error);
        }
        else
        {
            std::cerr << "[VSG_ABSTRACTION][ERROR] " << error << std::endl;
        }
    }

    void RenderEngineManager::_checkEngine() const
    {
        if (!engine_)
        {
            throw std::runtime_error("No render engine is currently active");
        }
    }

    // ========== 全局实例 ==========
    // getRenderManager函数已在头文件中定义为内联函数

} // namespace vsg_abstraction
