/* <editor-fold desc="MIT License">

Copyright(c) 2024 VSG Render Engine Abstraction

Permission is hereby granted, free of charge, to any person obtaining a copy of this software and associated documentation files (the "Software"), to deal in the Software without restriction, including without limitation the rights to use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of the Software, and to permit persons to whom the Software is furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.

</editor-fold> */

#include <vsg_abstraction/core/RenderEngineManager.h>
#include <vsg_abstraction/core/RenderEngineFactory.h>
#include <vsg_abstraction/core/Config.h>

#include <iostream>
#include <mutex>

namespace vsg_abstraction {

// ========== RenderEngineManager Implementation ==========

class RenderEngineManager::Impl {
public:
    std::unique_ptr<IRenderEngine> currentEngine_;
    RenderBackend currentBackend_ = RenderBackend::Mock;
    ErrorCallback errorCallback_;
    DebugCallback debugCallback_;
    mutable std::mutex engineMutex_;
    bool initialized_ = false;

    void logError(const std::string& message) {
        if (errorCallback_) {
            errorCallback_(message);
        } else {
            std::cerr << "[VSG_ABSTRACTION][ERROR] " << message << std::endl;
        }
    }

    void logDebug(const std::string& message, int severity = 0) {
        if (debugCallback_) {
            debugCallback_(message, severity);
        } else {
            std::cout << "[VSG_ABSTRACTION][DEBUG] " << message << std::endl;
        }
    }
};

RenderEngineManager::RenderEngineManager() : impl_(std::make_unique<Impl>()) {
    impl_->logDebug("RenderEngineManager created");
}

RenderEngineManager::~RenderEngineManager() {
    shutdown();
    impl_->logDebug("RenderEngineManager destroyed");
}

bool RenderEngineManager::initialize() {
    std::lock_guard<std::mutex> lock(impl_->engineMutex_);
    
    if (impl_->initialized_) {
        return true;
    }
    
    // 如果没有当前引擎，尝试创建默认引擎
    if (!impl_->currentEngine_) {
        auto recommendedBackend = RenderEngineFactory::getRecommendedBackend();
        if (!switchBackend(recommendedBackend)) {
            impl_->logError("Failed to initialize with recommended backend");
            return false;
        }
    }
    
    impl_->initialized_ = true;
    impl_->logDebug("RenderEngineManager initialized");
    return true;
}

void RenderEngineManager::shutdown() {
    std::lock_guard<std::mutex> lock(impl_->engineMutex_);
    
    if (impl_->currentEngine_) {
        impl_->currentEngine_->shutdown();
        impl_->currentEngine_.reset();
    }
    
    impl_->initialized_ = false;
    impl_->logDebug("RenderEngineManager shutdown");
}

bool RenderEngineManager::switchBackend(RenderBackend backend) {
    std::lock_guard<std::mutex> lock(impl_->engineMutex_);
    
    // 检查后端是否支持
    if (!RenderEngineFactory::isBackendSupported(backend)) {
        impl_->logError("Backend not supported: " + getRenderBackendName(backend));
        return false;
    }
    
    // 如果已经是当前后端，直接返回成功
    if (impl_->currentEngine_ && impl_->currentBackend_ == backend) {
        return true;
    }
    
    // 关闭当前引擎
    if (impl_->currentEngine_) {
        impl_->currentEngine_->shutdown();
        impl_->currentEngine_.reset();
    }
    
    // 创建新引擎
    try {
        impl_->currentEngine_ = RenderEngineFactory::create(backend);
        if (!impl_->currentEngine_) {
            impl_->logError("Failed to create engine for backend: " + getRenderBackendName(backend));
            return false;
        }
        
        // 初始化新引擎
        if (!impl_->currentEngine_->initialize()) {
            impl_->logError("Failed to initialize engine for backend: " + getRenderBackendName(backend));
            impl_->currentEngine_.reset();
            return false;
        }
        
        // 设置回调
        if (impl_->errorCallback_) {
            impl_->currentEngine_->setErrorCallback(impl_->errorCallback_);
        }
        if (impl_->debugCallback_) {
            impl_->currentEngine_->setDebugCallback(impl_->debugCallback_);
        }
        
        impl_->currentBackend_ = backend;
        impl_->logDebug("Switched to backend: " + getRenderBackendName(backend));
        return true;
        
    } catch (const std::exception& e) {
        impl_->logError("Exception while switching backend: " + std::string(e.what()));
        return false;
    }
}

bool RenderEngineManager::hasEngine() const {
    std::lock_guard<std::mutex> lock(impl_->engineMutex_);
    return impl_->currentEngine_ != nullptr;
}

IRenderEngine* RenderEngineManager::getEngine() const {
    std::lock_guard<std::mutex> lock(impl_->engineMutex_);
    return impl_->currentEngine_.get();
}

RenderBackend RenderEngineManager::getCurrentBackend() const {
    std::lock_guard<std::mutex> lock(impl_->engineMutex_);
    return impl_->currentBackend_;
}

EngineInfo RenderEngineManager::getEngineInfo() const {
    std::lock_guard<std::mutex> lock(impl_->engineMutex_);
    if (impl_->currentEngine_) {
        return impl_->currentEngine_->getEngineInfo();
    }
    return EngineInfo{};
}

DeviceCapabilities RenderEngineManager::getDeviceCapabilities() const {
    std::lock_guard<std::mutex> lock(impl_->engineMutex_);
    if (impl_->currentEngine_) {
        auto physicalDevices = impl_->currentEngine_->getPhysicalDevices();
        if (!physicalDevices.empty()) {
            return physicalDevices[0]->getCapabilities();
        }
    }
    return DeviceCapabilities{};
}

// ========== 场景图创建方法 ==========

ref_ptr<IGroup> RenderEngineManager::createGroup() {
    std::lock_guard<std::mutex> lock(impl_->engineMutex_);
    if (!impl_->currentEngine_) {
        impl_->logError("No render engine available");
        return nullptr;
    }
    return impl_->currentEngine_->createGroup();
}

ref_ptr<ITransform> RenderEngineManager::createTransform() {
    std::lock_guard<std::mutex> lock(impl_->engineMutex_);
    if (!impl_->currentEngine_) {
        impl_->logError("No render engine available");
        return nullptr;
    }
    return impl_->currentEngine_->createTransform();
}

ref_ptr<ITransform> RenderEngineManager::createMatrixTransform() {
    std::lock_guard<std::mutex> lock(impl_->engineMutex_);
    if (!impl_->currentEngine_) {
        impl_->logError("No render engine available");
        return nullptr;
    }
    return impl_->currentEngine_->createMatrixTransform();
}

ref_ptr<IGeometry> RenderEngineManager::createGeometry() {
    std::lock_guard<std::mutex> lock(impl_->engineMutex_);
    if (!impl_->currentEngine_) {
        impl_->logError("No render engine available");
        return nullptr;
    }
    return impl_->currentEngine_->createGeometry();
}

ref_ptr<IStateGroup> RenderEngineManager::createStateGroup() {
    std::lock_guard<std::mutex> lock(impl_->engineMutex_);
    if (!impl_->currentEngine_) {
        impl_->logError("No render engine available");
        return nullptr;
    }
    return impl_->currentEngine_->createStateGroup();
}

// ========== 窗口和渲染管理 ==========

ref_ptr<IWindow> RenderEngineManager::createWindow(const WindowTraits& traits) {
    std::lock_guard<std::mutex> lock(impl_->engineMutex_);
    if (!impl_->currentEngine_) {
        impl_->logError("No render engine available");
        return nullptr;
    }
    return impl_->currentEngine_->createWindow(traits);
}

ref_ptr<IRenderGraph> RenderEngineManager::createRenderGraph(ref_ptr<IWindow> window, ref_ptr<INode> view) {
    std::lock_guard<std::mutex> lock(impl_->engineMutex_);
    if (!impl_->currentEngine_) {
        impl_->logError("No render engine available");
        return nullptr;
    }
    return impl_->currentEngine_->createRenderGraph(window, view);
}

ref_ptr<IRecordTraversal> RenderEngineManager::createRecordTraversal() {
    std::lock_guard<std::mutex> lock(impl_->engineMutex_);
    if (!impl_->currentEngine_) {
        impl_->logError("No render engine available");
        return nullptr;
    }
    return impl_->currentEngine_->createRecordTraversal();
}

// ========== 同步和性能 ==========

void RenderEngineManager::waitIdle() {
    std::lock_guard<std::mutex> lock(impl_->engineMutex_);
    if (impl_->currentEngine_) {
        impl_->currentEngine_->waitIdle();
    }
}

RenderStatistics RenderEngineManager::getRenderStatistics() const {
    std::lock_guard<std::mutex> lock(impl_->engineMutex_);
    if (impl_->currentEngine_) {
        return impl_->currentEngine_->getRenderStatistics();
    }
    return RenderStatistics{};
}

void RenderEngineManager::resetRenderStatistics() {
    std::lock_guard<std::mutex> lock(impl_->engineMutex_);
    if (impl_->currentEngine_) {
        impl_->currentEngine_->resetRenderStatistics();
    }
}

// ========== 调试和诊断 ==========

void RenderEngineManager::setErrorCallback(ErrorCallback callback) {
    std::lock_guard<std::mutex> lock(impl_->engineMutex_);
    impl_->errorCallback_ = callback;
    if (impl_->currentEngine_) {
        impl_->currentEngine_->setErrorCallback(callback);
    }
}

void RenderEngineManager::setDebugCallback(DebugCallback callback) {
    std::lock_guard<std::mutex> lock(impl_->engineMutex_);
    impl_->debugCallback_ = callback;
    if (impl_->currentEngine_) {
        impl_->currentEngine_->setDebugCallback(callback);
    }
}

void RenderEngineManager::beginProfileMarker(const std::string& name) {
    std::lock_guard<std::mutex> lock(impl_->engineMutex_);
    if (impl_->currentEngine_) {
        impl_->currentEngine_->beginDebugMarker(name);
    }
}

void RenderEngineManager::endProfileMarker() {
    std::lock_guard<std::mutex> lock(impl_->engineMutex_);
    if (impl_->currentEngine_) {
        impl_->currentEngine_->endDebugMarker();
    }
}

// ========== VSG兼容性接口 ==========

#ifdef VSG_ABSTRACTION_HAS_VSG
vsg::ref_ptr<vsg::Group> RenderEngineManager::createVSGGroup() {
    std::lock_guard<std::mutex> lock(impl_->engineMutex_);
    if (!impl_->currentEngine_) {
        impl_->logError("No render engine available");
        return nullptr;
    }
    return impl_->currentEngine_->createVSGGroup();
}

vsg::ref_ptr<vsg::MatrixTransform> RenderEngineManager::createVSGTransform() {
    std::lock_guard<std::mutex> lock(impl_->engineMutex_);
    if (!impl_->currentEngine_) {
        impl_->logError("No render engine available");
        return nullptr;
    }
    return impl_->currentEngine_->createVSGTransform();
}

vsg::ref_ptr<vsg::Geometry> RenderEngineManager::createVSGGeometry() {
    std::lock_guard<std::mutex> lock(impl_->engineMutex_);
    if (!impl_->currentEngine_) {
        impl_->logError("No render engine available");
        return nullptr;
    }
    return impl_->currentEngine_->createVSGGeometry();
}

vsg::ref_ptr<vsg::StateGroup> RenderEngineManager::createVSGStateGroup() {
    std::lock_guard<std::mutex> lock(impl_->engineMutex_);
    if (!impl_->currentEngine_) {
        impl_->logError("No render engine available");
        return nullptr;
    }
    return impl_->currentEngine_->createVSGStateGroup();
}
#endif

// ========== 全局实例 ==========

RenderEngineManager& getRenderManager() {
    static RenderEngineManager instance;
    return instance;
}

} // namespace vsg_abstraction
