/* <editor-fold desc="MIT License">

Copyright(c) 2024 Rocky Render Engine Refactor

Permission is hereby granted, free of charge, to any person obtaining a copy of this software and associated documentation files (the "Software"), to deal in the Software without restriction, including without limitation the rights to use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of the Software, and to permit persons to whom the Software is furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.

</editor-fold> */

#include <rocky/render/RenderEngineManager.h>
#include <rocky/render/Config.h>
#include <iostream>
#include <cassert>
#include <chrono>
#include <thread>

using namespace rocky::render;

// 简单的测试框架
class TestFramework {
public:
    static void assertTrue(bool condition, const std::string& message) {
        if (!condition) {
            std::cerr << "ASSERTION FAILED: " << message << std::endl;
            exit(1);
        }
        std::cout << "✓ " << message << std::endl;
    }
    
    static void assertFalse(bool condition, const std::string& message) {
        assertTrue(!condition, message);
    }
    
    static void assertNotNull(void* ptr, const std::string& message) {
        assertTrue(ptr != nullptr, message);
    }
    
    static void assertEqual(int expected, int actual, const std::string& message) {
        assertTrue(expected == actual, message + " (expected: " + std::to_string(expected) + ", actual: " + std::to_string(actual) + ")");
    }
};

// 测试渲染引擎管理器基本功能
void testBasicFunctionality() {
    std::cout << "\n=== Testing Basic Functionality ===" << std::endl;
    
    auto& manager = RenderEngineManager::instance();
    
    // 测试单例
    auto& manager2 = RenderEngineManager::instance();
    TestFramework::assertTrue(&manager == &manager2, "Singleton instance should be the same");
    
    // 初始状态检查
    TestFramework::assertFalse(manager.hasEngine(), "Should not have engine initially");
    TestFramework::assertFalse(manager.isInitialized(), "Should not be initialized initially");
    TestFramework::assertEqual(static_cast<int>(RenderBackend::Mock), 
                              static_cast<int>(manager.getCurrentBackend()), 
                              "Default backend should be Mock");
}

// 测试Mock后端
void testMockBackend() {
    std::cout << "\n=== Testing Mock Backend ===" << std::endl;
    
    auto& manager = RenderEngineManager::instance();
    
    // 切换到Mock后端
    bool success = manager.switchBackend(RenderBackend::Mock);
    TestFramework::assertTrue(success, "Should successfully switch to Mock backend");
    
    // 检查状态
    TestFramework::assertTrue(manager.hasEngine(), "Should have engine after switching");
    TestFramework::assertTrue(manager.isInitialized(), "Should be initialized after switching");
    TestFramework::assertEqual(static_cast<int>(RenderBackend::Mock), 
                              static_cast<int>(manager.getCurrentBackend()), 
                              "Current backend should be Mock");
    
    // 获取引擎
    auto* engine = manager.getEngine();
    TestFramework::assertNotNull(engine, "Engine pointer should not be null");
    
    // 检查引擎信息
    auto info = manager.getEngineInfo();
    TestFramework::assertTrue(info.backend == RenderBackend::Mock, "Engine info should indicate Mock backend");
    TestFramework::assertTrue(!info.name.empty(), "Engine name should not be empty");
    TestFramework::assertTrue(!info.version.empty(), "Engine version should not be empty");
}

// 测试窗口创建
void testWindowCreation() {
    std::cout << "\n=== Testing Window Creation ===" << std::endl;
    
    auto& manager = RenderEngineManager::instance();
    
    // 确保有可用的引擎
    if (!manager.hasEngine()) {
        manager.switchBackend(RenderBackend::Mock);
    }
    
    // 创建窗口
    WindowConfig config;
    config.title = "Test Window";
    config.width = 800;
    config.height = 600;
    config.vsync = true;
    
    auto window = manager.createWindow(config);
    TestFramework::assertNotNull(window.get(), "Window should be created successfully");
    
    // 创建多个窗口
    auto window2 = manager.createWindow(config);
    TestFramework::assertNotNull(window2.get(), "Second window should be created successfully");
    TestFramework::assertTrue(window != window2, "Windows should be different instances");
}

// 测试场景图创建
void testSceneGraphCreation() {
    std::cout << "\n=== Testing Scene Graph Creation ===" << std::endl;
    
    auto& manager = RenderEngineManager::instance();
    
    // 确保有可用的引擎
    if (!manager.hasEngine()) {
        manager.switchBackend(RenderBackend::Mock);
    }
    
    // 创建场景根节点
    auto sceneRoot = manager.createSceneRoot();
    TestFramework::assertNotNull(sceneRoot.get(), "Scene root should be created successfully");
    TestFramework::assertTrue(sceneRoot->getNodeType() == NodeType::Group, "Scene root should be a Group node");
    
    // 创建子节点
    auto* engine = manager.getEngine();
    auto transform = engine->createTransform();
    auto geometry = engine->createGeometry({});
    
    TestFramework::assertNotNull(transform.get(), "Transform node should be created successfully");
    TestFramework::assertNotNull(geometry.get(), "Geometry node should be created successfully");
    
    // 构建层次结构
    transform->addChild(geometry);
    sceneRoot->addChild(transform);
    
    TestFramework::assertEqual(1, static_cast<int>(sceneRoot->getChildCount()), "Scene root should have 1 child");
    TestFramework::assertEqual(1, static_cast<int>(transform->getChildCount()), "Transform should have 1 child");
}

// 测试渲染图创建
void testRenderGraphCreation() {
    std::cout << "\n=== Testing Render Graph Creation ===" << std::endl;
    
    auto& manager = RenderEngineManager::instance();
    
    // 确保有可用的引擎
    if (!manager.hasEngine()) {
        manager.switchBackend(RenderBackend::Mock);
    }
    
    // 创建窗口
    auto window = manager.createWindow();
    TestFramework::assertNotNull(window.get(), "Window should be created for render graph");
    
    // 创建渲染图
    auto renderGraph = manager.createRenderGraph(window);
    TestFramework::assertNotNull(renderGraph.get(), "Render graph should be created successfully");
}

// 测试基本渲染
void testBasicRendering() {
    std::cout << "\n=== Testing Basic Rendering ===" << std::endl;
    
    auto& manager = RenderEngineManager::instance();
    
    // 确保有可用的引擎
    if (!manager.hasEngine()) {
        manager.switchBackend(RenderBackend::Mock);
    }
    
    // 创建完整的渲染设置
    auto window = manager.createWindow();
    auto sceneRoot = manager.createSceneRoot();
    auto renderGraph = manager.createRenderGraph(window);
    
    // 执行渲染
    bool renderSuccess = manager.render(renderGraph);
    TestFramework::assertTrue(renderSuccess, "Rendering should succeed");
    
    // 检查统计信息
    auto stats = manager.getRenderStatistics();
    TestFramework::assertTrue(stats.frameCount > 0, "Frame count should be greater than 0");
}

// 测试统计信息
void testStatistics() {
    std::cout << "\n=== Testing Statistics ===" << std::endl;
    
    auto& manager = RenderEngineManager::instance();
    
    // 重置统计信息
    manager.resetRenderStatistics();
    auto stats = manager.getRenderStatistics();
    TestFramework::assertEqual(0, static_cast<int>(stats.frameCount), "Frame count should be 0 after reset");
    
    // 执行一些渲染操作
    if (!manager.hasEngine()) {
        manager.switchBackend(RenderBackend::Mock);
    }
    
    auto window = manager.createWindow();
    auto renderGraph = manager.createRenderGraph(window);
    
    for (int i = 0; i < 5; ++i) {
        manager.render(renderGraph);
    }
    
    // 检查统计信息更新
    stats = manager.getRenderStatistics();
    TestFramework::assertEqual(5, static_cast<int>(stats.frameCount), "Frame count should be 5 after 5 renders");
    TestFramework::assertTrue(stats.frameTime >= 0.0, "Frame time should be non-negative");
}

// 测试错误处理
void testErrorHandling() {
    std::cout << "\n=== Testing Error Handling ===" << std::endl;
    
    auto& manager = RenderEngineManager::instance();
    
    // 测试错误回调
    bool errorCallbackCalled = false;
    std::string lastError;
    
    manager.setErrorCallback([&](const std::string& error) {
        errorCallbackCalled = true;
        lastError = error;
    });
    
    // 关闭引擎
    manager.shutdown();
    
    // 尝试在没有引擎的情况下创建窗口
    auto window = manager.createWindow();
    TestFramework::assertTrue(window == nullptr, "Window creation should fail without engine");
    
    // 重新初始化
    manager.switchBackend(RenderBackend::Mock);
}

// 测试设备能力
void testDeviceCapabilities() {
    std::cout << "\n=== Testing Device Capabilities ===" << std::endl;
    
    auto& manager = RenderEngineManager::instance();
    
    // 确保有可用的引擎
    if (!manager.hasEngine()) {
        manager.switchBackend(RenderBackend::Mock);
    }
    
    auto capabilities = manager.getDeviceCapabilities();
    TestFramework::assertTrue(capabilities.maxTextureSize > 0, "Max texture size should be greater than 0");
    TestFramework::assertTrue(capabilities.maxVertexAttributes > 0, "Max vertex attributes should be greater than 0");
    TestFramework::assertTrue(!capabilities.supportedFormats.empty(), "Should have supported texture formats");
}

// 主测试函数
int main() {
    std::cout << "Rocky Render Engine Manager Test" << std::endl;
    std::cout << "=================================" << std::endl;
    
    try {
        testBasicFunctionality();
        testMockBackend();
        testWindowCreation();
        testSceneGraphCreation();
        testRenderGraphCreation();
        testBasicRendering();
        testStatistics();
        testErrorHandling();
        testDeviceCapabilities();
        
        std::cout << "\n🎉 All tests passed!" << std::endl;
        return 0;
    }
    catch (const std::exception& e) {
        std::cerr << "\n❌ Test failed with exception: " << e.what() << std::endl;
        return 1;
    }
    catch (...) {
        std::cerr << "\n❌ Test failed with unknown exception" << std::endl;
        return 1;
    }
}
