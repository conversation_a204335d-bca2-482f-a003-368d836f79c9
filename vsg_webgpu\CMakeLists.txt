cmake_minimum_required(VERSION 3.20)

project(VSG_WebGPU
    VERSION 1.0.0
    DESCRIPTION "VulkanSceneGraph WebGPU Backend Extension"
    LANGUAGES CXX
)

# 设置C++标准
set(CMAKE_CXX_STANDARD 20)
set(CMAKE_CXX_STANDARD_REQUIRED ON)
set(CMAKE_CXX_EXTENSIONS OFF)

# 设置编译选项
if(MSVC)
    add_compile_options(/utf-8 /bigobj)
    add_compile_definitions(_CRT_SECURE_NO_WARNINGS)
else()
    add_compile_options(-Wall -Wextra)
endif()

# 设置输出目录
set(CMAKE_RUNTIME_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/bin)
set(CMAKE_LIBRARY_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/lib)
set(CMAKE_ARCHIVE_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/lib)

# 选项配置
option(VSG_WEBGPU_BUILD_TESTS "Build test programs" ON)
option(VSG_WEBGPU_BUILD_EXAMPLES "Build example programs" ON)
option(VSG_WEBGPU_USE_EMSCRIPTEN "Build for Emscripten/WebAssembly" OFF)
option(VSG_WEBGPU_USE_DAWN "Use Dawn WebGPU implementation" ON)

# 平台检测
if(EMSCRIPTEN)
    set(VSG_WEBGPU_USE_EMSCRIPTEN ON)
    set(VSG_WEBGPU_USE_DAWN OFF)
endif()

# 查找依赖库
find_package(vsg QUIET)
if(NOT vsg_FOUND)
    message(WARNING "VSG not found. Creating mock VSG target for testing.")
    add_library(vsg INTERFACE)
    add_library(vsg::vsg ALIAS vsg)
    target_include_directories(vsg INTERFACE ${CMAKE_CURRENT_SOURCE_DIR}/mock_vsg/include)
    set(VSG_WEBGPU_USE_MOCK_VSG ON)
else()
    message(STATUS "Found VSG: ${vsg_DIR}")
    set(VSG_WEBGPU_USE_MOCK_VSG OFF)
endif()

# Vulkan支持
if(NOT VSG_WEBGPU_USE_EMSCRIPTEN)
    find_package(Vulkan REQUIRED)
endif()

# WebGPU支持
if(VSG_WEBGPU_USE_EMSCRIPTEN)
    # Emscripten WebGPU
    set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -sUSE_WEBGPU=1")
    set(CMAKE_EXE_LINKER_FLAGS "${CMAKE_EXE_LINKER_FLAGS} -sUSE_WEBGPU=1 -sASYNCIFY")
elseif(VSG_WEBGPU_USE_DAWN)
    # Dawn WebGPU - 尝试多种方式查找
    find_package(PkgConfig QUIET)
    if(PkgConfig_FOUND)
        pkg_check_modules(DAWN QUIET IMPORTED_TARGET dawn)
    endif()
    
    if(NOT DAWN_FOUND)
        # 手动查找Dawn
        find_path(DAWN_INCLUDE_DIR
            NAMES webgpu/webgpu_cpp.h dawn/webgpu_cpp.h
            PATHS
                ${CMAKE_PREFIX_PATH}/include
                /usr/local/include
                /usr/include
                C:/dev/dawn/include
                C:/dev/vcpkg/installed/x64-windows/include
        )
        
        find_library(DAWN_NATIVE_LIBRARY
            NAMES dawn_native
            PATHS
                ${CMAKE_PREFIX_PATH}/lib
                /usr/local/lib
                /usr/lib
                C:/dev/dawn/lib
                C:/dev/vcpkg/installed/x64-windows/lib
        )
        
        find_library(DAWN_PROC_LIBRARY
            NAMES dawn_proc
            PATHS
                ${CMAKE_PREFIX_PATH}/lib
                /usr/local/lib
                /usr/lib
                C:/dev/dawn/lib
                C:/dev/vcpkg/installed/x64-windows/lib
        )
        
        if(DAWN_INCLUDE_DIR AND DAWN_NATIVE_LIBRARY AND DAWN_PROC_LIBRARY)
            set(DAWN_FOUND TRUE)
            add_library(dawn INTERFACE)
            target_include_directories(dawn INTERFACE ${DAWN_INCLUDE_DIR})
            target_link_libraries(dawn INTERFACE ${DAWN_NATIVE_LIBRARY} ${DAWN_PROC_LIBRARY})
        endif()
    endif()
    
    # 如果还是没找到，创建一个模拟的接口
    if(NOT DAWN_FOUND)
        message(WARNING "Dawn WebGPU not found, creating mock interface")
        add_library(dawn INTERFACE)
        target_compile_definitions(dawn INTERFACE VSG_WEBGPU_MOCK_IMPLEMENTATION)
    endif()
endif()

# 包含目录
include_directories(${CMAKE_CURRENT_SOURCE_DIR}/include)
include_directories(${CMAKE_CURRENT_SOURCE_DIR}/../include)

# 添加子目录
add_subdirectory(src)

if(VSG_WEBGPU_BUILD_TESTS)
    enable_testing()
    add_subdirectory(tests)
endif()

if(VSG_WEBGPU_BUILD_EXAMPLES)
    add_subdirectory(examples)
endif()

# 打印配置信息
message(STATUS "VSG_WebGPU Configuration:")
message(STATUS "  Version: ${PROJECT_VERSION}")
message(STATUS "  C++ Standard: ${CMAKE_CXX_STANDARD}")
message(STATUS "  Build Tests: ${VSG_WEBGPU_BUILD_TESTS}")
message(STATUS "  Build Examples: ${VSG_WEBGPU_BUILD_EXAMPLES}")
message(STATUS "  Use Emscripten: ${VSG_WEBGPU_USE_EMSCRIPTEN}")
message(STATUS "  Use Dawn: ${VSG_WEBGPU_USE_DAWN}")
if(VSG_WEBGPU_USE_DAWN AND DAWN_FOUND)
    message(STATUS "  Dawn Found: YES")
elseif(VSG_WEBGPU_USE_DAWN)
    message(STATUS "  Dawn Found: NO (using mock)")
endif()

# 安装配置
# 安装头文件
install(DIRECTORY include/vsg_webgpu
    DESTINATION include
    FILES_MATCHING PATTERN "*.h"
)

# 安装目标 - 简化版本，避免导出问题
install(TARGETS vsg_webgpu
    LIBRARY DESTINATION lib
    ARCHIVE DESTINATION lib
    RUNTIME DESTINATION bin
    INCLUDES DESTINATION include
)

# 简化配置，暂时不创建配置文件以避免导出问题
