#pragma once

/* <editor-fold desc="MIT License">

Copyright(c) 2018 <PERSON>

Permission is hereby granted, free of charge, to any person obtaining a copy of this software and associated documentation files (the "Software"), to deal in the Software without restriction, including without limitation the rights to use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of the Software, and to permit persons to whom the Software is furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.

</editor-fold> */

#include <vsg/commands/Command.h>

namespace vsg
{

    /// Base class for Vulkan commands associated with state, such as binding graphics
    /// pipelines and descriptor sets for textures, uniform buffers and storage buffers.
    /// StateCommands can be attached directly as nodes in the scene graph, or more typically assigned to StateGroup nodes to enable push/popping of state.
    class VSG_DECLSPEC StateCommand : public Inherit<Command, StateCommand>
    {
    public:
        explicit StateCommand(uint32_t in_slot = 0) :
            slot(in_slot) {}

        StateCommand(const StateCommand& rhs, const CopyOp& copyop);

        int compare(const Object& rhs_object) const override;

        void read(Input& input) override;
        void write(Output& output) const override;

        uint32_t slot = 0;

    protected:
        virtual ~StateCommand() {}
    };
    VSG_type_name(vsg::StateCommand);

    using StateCommands = std::vector<ref_ptr<StateCommand>, allocator_affinity_nodes<ref_ptr<StateCommand>>>;

} // namespace vsg
