#!/bin/bash

# VSG WebGPU Unix Build Script (Linux/macOS)

set -e  # Exit on any error

echo "========================================"
echo "VSG WebGPU Unix Build Script"
echo "========================================"

# 默认设置
BUILD_TYPE="Release"
BUILD_DIR="build_desk"
INSTALL_DIR="redist_desk"
CLEAN_BUILD=false
SHOW_HELP=false
PARALLEL_JOBS=$(nproc 2>/dev/null || sysctl -n hw.ncpu 2>/dev/null || echo 4)

# 解析命令行参数
while [[ $# -gt 0 ]]; do
    case $1 in
        debug)
            BUILD_TYPE="Debug"
            shift
            ;;
        release)
            BUILD_TYPE="Release"
            shift
            ;;
        clean)
            CLEAN_BUILD=true
            shift
            ;;
        help|--help|-h)
            SHOW_HELP=true
            shift
            ;;
        -j)
            PARALLEL_JOBS="$2"
            shift 2
            ;;
        *)
            echo "Unknown option: $1"
            echo "Use 'help' for usage information"
            exit 1
            ;;
    esac
done

# 显示帮助
if [ "$SHOW_HELP" = true ]; then
    echo "Usage: $0 [options]"
    echo ""
    echo "Options:"
    echo "  debug     - Build in Debug mode"
    echo "  release   - Build in Release mode (default)"
    echo "  clean     - Clean build and install directories"
    echo "  help      - Show this help message"
    echo "  -j N      - Use N parallel jobs (default: $PARALLEL_JOBS)"
    echo ""
    echo "Examples:"
    echo "  $0              - Build in Release mode"
    echo "  $0 debug        - Build in Debug mode"
    echo "  $0 clean        - Clean all build artifacts"
    echo "  $0 -j 8         - Build with 8 parallel jobs"
    echo ""
    exit 0
fi

# 清理构建
if [ "$CLEAN_BUILD" = true ]; then
    echo "Cleaning build directory..."
    rm -rf "$BUILD_DIR"
    rm -rf "$INSTALL_DIR"
    echo "Clean completed."
    exit 0
fi

echo "Build Type: $BUILD_TYPE"
echo "Build Directory: $BUILD_DIR"
echo "Install Directory: $INSTALL_DIR"
echo "Parallel Jobs: $PARALLEL_JOBS"
echo ""

# 检查必要的工具
echo "Checking required tools..."

if ! command -v cmake &> /dev/null; then
    echo "ERROR: CMake not found"
    echo "Please install CMake"
    exit 1
fi

if ! command -v git &> /dev/null; then
    echo "WARNING: Git not found"
    echo "Some features may not work properly"
fi

if ! command -v pkg-config &> /dev/null; then
    echo "WARNING: pkg-config not found"
    echo "Package detection may not work properly"
fi

echo "Tools check completed."
echo ""

# 检查VSG
echo "Checking VSG installation..."
if ! pkg-config --exists vsg 2>/dev/null; then
    echo "WARNING: VSG not found via pkg-config"
    echo "Please ensure VSG is properly installed"
fi

# 创建构建目录
echo "Creating build directory..."
mkdir -p "$BUILD_DIR"
mkdir -p "$INSTALL_DIR"

# 配置CMake
echo ""
echo "========================================"
echo "Configuring CMake..."
echo "========================================"

cd "$BUILD_DIR"

CMAKE_ARGS=(
    -DCMAKE_BUILD_TYPE="$BUILD_TYPE"
    -DCMAKE_INSTALL_PREFIX="../$INSTALL_DIR"
    -DVSSG_WEBGPU_BUILD_TESTS=ON
    -DVSSG_WEBGPU_BUILD_EXAMPLES=ON
    -DVSSG_WEBGPU_USE_DAWN=ON
)

# 平台特定的配置
if [[ "$OSTYPE" == "darwin"* ]]; then
    # macOS
    echo "Configuring for macOS..."
    CMAKE_ARGS+=(
        -DCMAKE_OSX_DEPLOYMENT_TARGET=10.15
    )
elif [[ "$OSTYPE" == "linux-gnu"* ]]; then
    # Linux
    echo "Configuring for Linux..."
    CMAKE_ARGS+=(
        -DCMAKE_POSITION_INDEPENDENT_CODE=ON
    )
fi

cmake .. "${CMAKE_ARGS[@]}"

if [ $? -ne 0 ]; then
    echo "ERROR: CMake configuration failed"
    cd ..
    exit 1
fi

echo "CMake configuration completed successfully."
echo ""

# 构建项目
echo "========================================"
echo "Building project..."
echo "========================================"

cmake --build . --config "$BUILD_TYPE" --parallel "$PARALLEL_JOBS"

if [ $? -ne 0 ]; then
    echo "ERROR: Build failed"
    cd ..
    exit 1
fi

echo "Build completed successfully."
echo ""

# 安装项目
echo "========================================"
echo "Installing project..."
echo "========================================"

cmake --install . --config "$BUILD_TYPE"

if [ $? -ne 0 ]; then
    echo "ERROR: Installation failed"
    cd ..
    exit 1
fi

echo "Installation completed successfully."
echo ""

# 运行测试
echo "========================================"
echo "Running tests..."
echo "========================================"

ctest --build-config "$BUILD_TYPE" --output-on-failure --parallel "$PARALLEL_JOBS"

if [ $? -ne 0 ]; then
    echo "WARNING: Some tests failed"
    echo "Check the test output above for details"
else
    echo "All tests passed successfully."
fi

cd ..

echo ""
echo "========================================"
echo "Build Summary"
echo "========================================"
echo "Build Type: $BUILD_TYPE"
echo "Build Directory: $BUILD_DIR"
echo "Install Directory: $INSTALL_DIR"
echo "Parallel Jobs: $PARALLEL_JOBS"
echo ""
echo "Build artifacts:"
if [ -d "$INSTALL_DIR/bin" ] && [ "$(ls -A $INSTALL_DIR/bin)" ]; then
    echo "  Executables: $INSTALL_DIR/bin/"
fi
if [ -d "$INSTALL_DIR/lib" ] && [ "$(ls -A $INSTALL_DIR/lib)" ]; then
    echo "  Libraries: $INSTALL_DIR/lib/"
fi
if [ -d "$INSTALL_DIR/include/vsg_webgpu" ]; then
    echo "  Headers: $INSTALL_DIR/include/vsg_webgpu/"
fi
echo ""
echo "Build completed successfully!"
