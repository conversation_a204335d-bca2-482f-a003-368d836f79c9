#pragma once

/* <editor-fold desc="MIT License">

Copyright(c) 2024 VSG Render Engine Abstraction

Permission is hereby granted, free of charge, to any person obtaining a copy of this software and associated documentation files (the "Software"), to deal in the Software without restriction, including without limitation the rights to use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of the Software, and to permit persons to whom the Software is furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND <PERSON>NINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.

</editor-fold> */

#include <memory>
#include <string>
#include <vsg_abstraction/core/Export.h>
#include <vsg_abstraction/core/Types.h>

namespace vsg_abstraction
{

    // Forward declarations
    class IVisitor;

    /**
 * @brief 节点接口基类
 */
    class VSG_ABSTRACTION_API INode
    {
    public:
        virtual ~INode() = default;

        // 基础属性
        virtual std::string getName() const = 0;
        virtual void setName(const std::string& name) = 0;
        virtual uint32_t getNodeId() const = 0;
        virtual NodeType getNodeType() const = 0;

        // 访问者模式
        virtual void accept(IVisitor& visitor) = 0;
        virtual void traverse(IVisitor& visitor) = 0;

        // 边界框
        virtual BoundingBox getLocalBounds() const = 0;
        virtual BoundingBox getWorldBounds() const = 0;
        virtual void computeBounds() = 0;

        // 用户数据
        virtual void setUserData(const std::string& key, void* data) = 0;
        virtual void* getUserData(const std::string& key) const = 0;
        virtual bool hasUserData(const std::string& key) const = 0;
        virtual void removeUserData(const std::string& key) = 0;

        // 原生句柄
        virtual void* getNativeHandle() const = 0;
    };

    /**
 * @brief 访问者接口
 */
    class VSG_ABSTRACTION_API IVisitor
    {
    public:
        virtual ~IVisitor() = default;
        virtual void apply(INode& node) = 0;
    };

} // namespace vsg_abstraction
