#pragma once

/* <editor-fold desc="MIT License">

Copyright(c) 2024 Rocky Render Engine Refactor

Permission is hereby granted, free of charge, to any person obtaining a copy of this software and associated documentation files (the "Software"), to deal in the Software without restriction, including without limitation the rights to use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of the Software, and to permit persons to whom the Software is furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.

</editor-fold> */

#include <string>
#include <vector>
#include <array>
#include <functional>
#include <cstdint>

namespace rocky {
namespace render {

// ========== 基础类型定义 ==========

using Vec2 = std::array<float, 2>;
using Vec3 = std::array<float, 3>;
using Vec4 = std::array<float, 4>;
using Mat4 = std::array<std::array<float, 4>, 4>;

// ========== 枚举类型 ==========

/**
 * @brief 渲染后端类型
 */
enum class RenderBackend {
    Vulkan,     ///< Vulkan API (通过VSG)
    WebGPU,     ///< WebGPU API
    OpenGL,     ///< OpenGL API
    DirectX12,  ///< DirectX 12 API
    Metal,      ///< Metal API (macOS/iOS)
    Mock        ///< Mock实现（用于测试）
};

/**
 * @brief 纹理格式
 */
enum class TextureFormat {
    RGBA8,
    RGBA16F,
    RGBA32F,
    RGB8,
    RGB16F,
    RGB32F,
    RG8,
    RG16F,
    RG32F,
    R8,
    R16F,
    R32F,
    Depth16,
    Depth24,
    Depth32F,
    Depth24Stencil8
};

/**
 * @brief 缓冲区类型
 */
enum class BufferType {
    Vertex,     ///< 顶点缓冲区
    Index,      ///< 索引缓冲区
    Uniform,    ///< 统一缓冲区
    Storage,    ///< 存储缓冲区
    Staging     ///< 暂存缓冲区
};

/**
 * @brief 着色器类型
 */
enum class ShaderType {
    Vertex,     ///< 顶点着色器
    Fragment,   ///< 片段着色器
    Geometry,   ///< 几何着色器
    Compute,    ///< 计算着色器
    TessControl,///< 细分控制着色器
    TessEval    ///< 细分评估着色器
};

/**
 * @brief 图元类型
 */
enum class PrimitiveType {
    Points,
    Lines,
    LineStrip,
    Triangles,
    TriangleStrip,
    TriangleFan
};

// ========== 配置结构体 ==========

/**
 * @brief 窗口配置
 */
struct WindowConfig {
    std::string title = "Rocky Application";
    uint32_t width = 1920;
    uint32_t height = 1080;
    bool fullscreen = false;
    bool vsync = true;
    bool resizable = true;
    uint32_t samples = 1; ///< 多重采样数量
};

/**
 * @brief 纹理配置
 */
struct TextureConfig {
    uint32_t width = 1;
    uint32_t height = 1;
    uint32_t depth = 1;
    uint32_t mipLevels = 1;
    uint32_t arrayLayers = 1;
    TextureFormat format = TextureFormat::RGBA8;
    bool generateMipmaps = false;
    const void* initialData = nullptr;
    size_t dataSize = 0;
};

/**
 * @brief 缓冲区配置
 */
struct BufferConfig {
    BufferType type = BufferType::Vertex;
    size_t size = 0;
    const void* initialData = nullptr;
    bool dynamic = false; ///< 是否为动态缓冲区
};

/**
 * @brief 着色器配置
 */
struct ShaderConfig {
    ShaderType type = ShaderType::Vertex;
    std::string source;     ///< 着色器源代码
    std::string entryPoint = "main"; ///< 入口点函数名
    std::vector<std::string> defines; ///< 预处理器定义
};

/**
 * @brief 管线配置
 */
struct PipelineConfig {
    std::vector<ShaderConfig> shaders;
    PrimitiveType primitiveType = PrimitiveType::Triangles;
    bool depthTest = true;
    bool depthWrite = true;
    bool blending = false;
    // 更多渲染状态...
};

/**
 * @brief 相机配置
 */
struct CameraConfig {
    Vec3 position = {0.0f, 0.0f, 0.0f};
    Vec3 target = {0.0f, 0.0f, -1.0f};
    Vec3 up = {0.0f, 1.0f, 0.0f};
    float fov = 45.0f;      ///< 视野角度（度）
    float nearPlane = 0.1f;
    float farPlane = 1000.0f;
    float aspectRatio = 16.0f / 9.0f;
};

/**
 * @brief 几何数据
 */
struct GeometryData {
    std::vector<float> vertices;
    std::vector<uint32_t> indices;
    std::vector<Vec3> normals;
    std::vector<Vec2> texCoords;
    PrimitiveType primitiveType = PrimitiveType::Triangles;
};

// ========== 信息结构体 ==========

/**
 * @brief 渲染引擎信息
 */
struct RenderEngineInfo {
    std::string name;
    std::string version;
    std::string vendor;
    RenderBackend backend;
    std::vector<std::string> extensions;
};

/**
 * @brief 设备能力信息
 */
struct DeviceCapabilities {
    uint32_t maxTextureSize;
    uint32_t maxTextureLayers;
    uint32_t maxUniformBufferSize;
    uint32_t maxVertexAttributes;
    uint32_t maxViewports;
    bool supportsGeometryShader;
    bool supportsTessellation;
    bool supportsComputeShader;
    bool supportsMultisampling;
    std::vector<TextureFormat> supportedFormats;
};

/**
 * @brief 渲染统计信息
 */
struct RenderStatistics {
    uint64_t frameCount = 0;
    uint64_t drawCalls = 0;
    uint64_t triangles = 0;
    uint64_t vertices = 0;
    double frameTime = 0.0; ///< 帧时间（毫秒）
    double cpuTime = 0.0;   ///< CPU时间（毫秒）
    double gpuTime = 0.0;   ///< GPU时间（毫秒）
    size_t memoryUsed = 0;  ///< 显存使用量（字节）
};

// ========== 回调函数类型 ==========

/**
 * @brief 调试回调函数类型
 */
using DebugCallback = std::function<void(const std::string& message, int severity)>;

/**
 * @brief 错误回调函数类型
 */
using ErrorCallback = std::function<void(const std::string& error)>;

/**
 * @brief 窗口事件回调函数类型
 */
using WindowEventCallback = std::function<void(int eventType, void* eventData)>;

// ========== 常量定义 ==========

namespace Constants {
    constexpr uint32_t MAX_TEXTURE_UNITS = 32;
    constexpr uint32_t MAX_VERTEX_ATTRIBUTES = 16;
    constexpr uint32_t MAX_UNIFORM_BUFFERS = 16;
    constexpr uint32_t MAX_STORAGE_BUFFERS = 16;
    constexpr uint32_t DEFAULT_ALIGNMENT = 256;
}

// ========== 工具函数 ==========

/**
 * @brief 获取纹理格式的字节大小
 */
inline size_t getTextureFormatSize(TextureFormat format) {
    switch (format) {
        case TextureFormat::RGBA8: return 4;
        case TextureFormat::RGBA16F: return 8;
        case TextureFormat::RGBA32F: return 16;
        case TextureFormat::RGB8: return 3;
        case TextureFormat::RGB16F: return 6;
        case TextureFormat::RGB32F: return 12;
        case TextureFormat::RG8: return 2;
        case TextureFormat::RG16F: return 4;
        case TextureFormat::RG32F: return 8;
        case TextureFormat::R8: return 1;
        case TextureFormat::R16F: return 2;
        case TextureFormat::R32F: return 4;
        default: return 0;
    }
}

/**
 * @brief 获取渲染后端名称
 */
inline std::string getRenderBackendName(RenderBackend backend) {
    switch (backend) {
        case RenderBackend::Vulkan: return "Vulkan";
        case RenderBackend::WebGPU: return "WebGPU";
        case RenderBackend::OpenGL: return "OpenGL";
        case RenderBackend::DirectX12: return "DirectX 12";
        case RenderBackend::Metal: return "Metal";
        case RenderBackend::Mock: return "Mock";
        default: return "Unknown";
    }
}

} // namespace render
} // namespace rocky
