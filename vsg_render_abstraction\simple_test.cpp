/* <editor-fold desc="MIT License">

Copyright(c) 2024 VSG Render Engine Abstraction

Permission is hereby granted, free of charge, to any person obtaining a copy of this software and associated documentation files (the "Software"), to deal in the Software without restriction, including without limitation the rights to use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of the Software, and to permit persons to whom the Software is furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.

</editor-fold> */

/**
 * @file simple_test.cpp
 * @brief 简单的VSG抽象库功能测试
 * 
 * 这个程序测试VSG抽象库的基本功能：
 * 1. 工厂方法创建引擎
 * 2. 管理器单例模式
 * 3. 后端切换
 * 4. 基本信息查询
 */

#include <iostream>
#include <vsg_abstraction/core/RenderEngineFactory.h>
#include <vsg_abstraction/core/RenderEngineManager.h>

using namespace vsg_abstraction;

int main() {
    std::cout << "=== VSG Render Engine Abstraction - 简单功能测试 ===" << std::endl;
    std::cout << std::endl;

    try {
        // 1. 测试工厂方法
        std::cout << "1. 测试工厂方法..." << std::endl;
        
        // 获取支持的后端
        auto supportedBackends = RenderEngineFactory::getSupportedBackends();
        std::cout << "   支持的后端数量: " << supportedBackends.size() << std::endl;
        
        for (auto backend : supportedBackends) {
            std::cout << "   - " << getRenderBackendName(backend) << std::endl;
        }
        
        // 获取推荐后端
        auto recommended = RenderEngineFactory::getRecommendedBackend();
        std::cout << "   推荐后端: " << getRenderBackendName(recommended) << std::endl;
        std::cout << std::endl;

        // 2. 测试Mock引擎创建
        std::cout << "2. 测试Mock引擎创建..." << std::endl;
        auto mockEngine = RenderEngineFactory::create(RenderBackend::Mock);
        if (mockEngine) {
            std::cout << "   ✓ Mock引擎创建成功" << std::endl;
            
            // 初始化引擎
            if (mockEngine->initialize()) {
                std::cout << "   ✓ Mock引擎初始化成功" << std::endl;
                
                // 获取引擎信息
                auto info = mockEngine->getEngineInfo();
                std::cout << "   引擎名称: " << info.name << std::endl;
                std::cout << "   引擎版本: " << info.version << std::endl;
                std::cout << "   引擎厂商: " << info.vendor << std::endl;
                
                // 关闭引擎
                mockEngine->shutdown();
                std::cout << "   ✓ Mock引擎关闭成功" << std::endl;
            } else {
                std::cout << "   ✗ Mock引擎初始化失败" << std::endl;
            }
        } else {
            std::cout << "   ✗ Mock引擎创建失败" << std::endl;
        }
        std::cout << std::endl;

        // 3. 测试管理器单例
        std::cout << "3. 测试管理器单例..." << std::endl;
        auto& manager1 = getRenderManager();
        auto& manager2 = getRenderManager();
        
        if (&manager1 == &manager2) {
            std::cout << "   ✓ 管理器单例模式正常" << std::endl;
        } else {
            std::cout << "   ✗ 管理器单例模式异常" << std::endl;
        }
        std::cout << std::endl;

        // 4. 测试后端切换
        std::cout << "4. 测试后端切换..." << std::endl;
        
        // 切换到Mock后端
        if (manager1.switchBackend(RenderBackend::Mock)) {
            std::cout << "   ✓ 切换到Mock后端成功" << std::endl;
            
            auto currentBackend = manager1.getCurrentBackend();
            std::cout << "   当前后端: " << getRenderBackendName(currentBackend) << std::endl;
            
            // 检查引擎是否可用
            if (manager1.hasEngine()) {
                std::cout << "   ✓ 引擎可用" << std::endl;
                
                auto engine = manager1.getEngine();
                if (engine) {
                    std::cout << "   ✓ 获取引擎成功" << std::endl;
                } else {
                    std::cout << "   ✗ 获取引擎失败" << std::endl;
                }
            } else {
                std::cout << "   ✗ 引擎不可用" << std::endl;
            }
        } else {
            std::cout << "   ✗ 切换到Mock后端失败" << std::endl;
        }
        std::cout << std::endl;

        // 5. 测试后端信息
        std::cout << "5. 测试后端信息..." << std::endl;
        RenderEngineFactory::printBackendInfo(RenderBackend::Mock);
        std::cout << std::endl;

        std::cout << "=== 测试完成 ===" << std::endl;
        std::cout << "✓ 所有基本功能测试通过！" << std::endl;

    } catch (const std::exception& e) {
        std::cerr << "测试过程中发生异常: " << e.what() << std::endl;
        return 1;
    }

    return 0;
}
