/* <editor-fold desc="MIT License">

Copyright(c) 2024 Rocky Render Engine Refactor

Permission is hereby granted, free of charge, to any person obtaining a copy of this software and associated documentation files (the "Software"), to deal in the Software without restriction, including without limitation the rights to use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of the Software, and to permit persons to whom the Software is furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.

</editor-fold> */

#include <rocky/render/RenderEngineManager.h>
#include <rocky/render/Config.h>
#include <iostream>
#include <chrono>
#include <thread>

using namespace rocky::render;

/**
 * @brief Rocky渲染引擎基础渲染示例
 * 
 * 这个示例展示了如何使用重构后的Rocky渲染引擎进行基础渲染：
 * 1. 初始化渲染引擎
 * 2. 创建窗口和场景
 * 3. 构建简单的场景图
 * 4. 执行渲染循环
 */

// 创建一个简单的三角形场景
std::shared_ptr<ISceneNode> createTriangleScene(IRenderEngine* engine) {
    // 创建场景根节点
    auto root = engine->createGroup();
    root->setName("TriangleScene");
    
    // 创建变换节点
    auto transform = engine->createTransform();
    transform->setName("TriangleTransform");
    
    // 设置变换矩阵（单位矩阵）
    Mat4 identity = {{
        {{1.0f, 0.0f, 0.0f, 0.0f}},
        {{0.0f, 1.0f, 0.0f, 0.0f}},
        {{0.0f, 0.0f, 1.0f, 0.0f}},
        {{0.0f, 0.0f, 0.0f, 1.0f}}
    }};
    transform->setTransform(identity);
    
    // 创建三角形几何数据
    GeometryData triangleData;
    triangleData.vertices = {
        // 位置坐标 (x, y, z)
         0.0f,  0.5f, 0.0f,  // 顶点1 (顶部)
        -0.5f, -0.5f, 0.0f,  // 顶点2 (左下)
         0.5f, -0.5f, 0.0f   // 顶点3 (右下)
    };
    
    triangleData.indices = {0, 1, 2}; // 三角形索引
    triangleData.primitiveType = PrimitiveType::Triangles;
    
    // 创建几何节点
    auto geometry = engine->createGeometry(triangleData);
    geometry->setName("Triangle");
    
    // 构建场景层次结构
    transform->addChild(geometry);
    root->addChild(transform);
    
    std::cout << "Created triangle scene with " << root->getChildCount() << " child nodes" << std::endl;
    
    return root;
}

// 打印渲染统计信息
void printRenderStatistics(const RenderStatistics& stats) {
    std::cout << "Render Statistics:" << std::endl;
    std::cout << "  Frame Count: " << stats.frameCount << std::endl;
    std::cout << "  Draw Calls: " << stats.drawCalls << std::endl;
    std::cout << "  Triangles: " << stats.triangles << std::endl;
    std::cout << "  Vertices: " << stats.vertices << std::endl;
    std::cout << "  Frame Time: " << stats.frameTime << "ms" << std::endl;
    std::cout << "  CPU Time: " << stats.cpuTime << "ms" << std::endl;
    std::cout << "  GPU Time: " << stats.gpuTime << "ms" << std::endl;
    std::cout << "  Memory Used: " << (stats.memoryUsed / 1024 / 1024) << "MB" << std::endl;
}

// 主函数
int main() {
    std::cout << "=== Rocky Render Engine Basic Rendering Example ===" << std::endl;
    std::cout << "Rocky Render Version: " << VERSION_STRING << std::endl;
    std::cout << "Platform: " << PLATFORM_NAME << std::endl;
    std::cout << "Compiler: " << COMPILER_NAME << std::endl;
    std::cout << std::endl;
    
    try {
        // 获取渲染引擎管理器
        auto& manager = getRenderManager();
        
        // 显示支持的后端
        auto supportedBackends = RenderEngineFactory::getSupportedBackends();
        std::cout << "Supported render backends:" << std::endl;
        for (auto backend : supportedBackends) {
            std::cout << "  - " << getRenderBackendName(backend) << std::endl;
        }
        std::cout << std::endl;
        
        // 获取推荐的后端
        auto recommendedBackend = RenderEngineFactory::getRecommendedBackend();
        std::cout << "Recommended backend: " << getRenderBackendName(recommendedBackend) << std::endl;
        
        // 切换到推荐的后端
        if (!manager.switchBackend(recommendedBackend)) {
            std::cout << "Failed to switch to recommended backend, trying Mock..." << std::endl;
            if (!manager.switchBackend(RenderBackend::Mock)) {
                std::cerr << "Failed to initialize any render backend!" << std::endl;
                return 1;
            }
        }
        
        std::cout << "Using render backend: " << getRenderBackendName(manager.getCurrentBackend()) << std::endl;
        
        // 获取引擎信息
        auto engineInfo = manager.getEngineInfo();
        std::cout << "Engine: " << engineInfo.name << " v" << engineInfo.version << std::endl;
        std::cout << "Vendor: " << engineInfo.vendor << std::endl;
        std::cout << std::endl;
        
        // 获取设备能力
        auto capabilities = manager.getDeviceCapabilities();
        std::cout << "Device Capabilities:" << std::endl;
        std::cout << "  Max Texture Size: " << capabilities.maxTextureSize << std::endl;
        std::cout << "  Max Vertex Attributes: " << capabilities.maxVertexAttributes << std::endl;
        std::cout << "  Supports Geometry Shader: " << (capabilities.supportsGeometryShader ? "Yes" : "No") << std::endl;
        std::cout << "  Supports Compute Shader: " << (capabilities.supportsComputeShader ? "Yes" : "No") << std::endl;
        std::cout << std::endl;
        
        // 创建窗口
        WindowConfig windowConfig;
        windowConfig.title = "Rocky Basic Rendering Example";
        windowConfig.width = 800;
        windowConfig.height = 600;
        windowConfig.vsync = true;
        
        auto window = manager.createWindow(windowConfig);
        if (!window) {
            std::cerr << "Failed to create window!" << std::endl;
            return 1;
        }
        
        std::cout << "Created window: " << windowConfig.title << " (" 
                  << windowConfig.width << "x" << windowConfig.height << ")" << std::endl;
        
        // 创建场景
        auto* engine = manager.getEngine();
        auto scene = createTriangleScene(engine);
        
        // 创建渲染图
        auto renderGraph = manager.createRenderGraph(window);
        if (!renderGraph) {
            std::cerr << "Failed to create render graph!" << std::endl;
            return 1;
        }
        
        std::cout << "Created render graph" << std::endl;
        
        // 设置错误回调
        manager.setErrorCallback([](const std::string& error) {
            std::cerr << "Render Error: " << error << std::endl;
        });
        
        // 重置统计信息
        manager.resetRenderStatistics();
        
        std::cout << std::endl;
        std::cout << "Starting render loop..." << std::endl;
        
        // 渲染循环
        const int maxFrames = 60; // 渲染60帧
        int frameCount = 0;
        auto startTime = std::chrono::high_resolution_clock::now();
        
        while (frameCount < maxFrames) {
            auto frameStart = std::chrono::high_resolution_clock::now();
            
            // 开始性能标记
            manager.beginProfileMarker("Frame " + std::to_string(frameCount));
            
            // 执行渲染
            if (!manager.render(renderGraph)) {
                std::cerr << "Render failed at frame " << frameCount << std::endl;
                break;
            }
            
            // 结束性能标记
            manager.endProfileMarker();
            
            frameCount++;
            
            // 每10帧打印一次进度
            if (frameCount % 10 == 0) {
                auto stats = manager.getRenderStatistics();
                std::cout << "Frame " << frameCount << " - FPS: " 
                          << (1000.0 / stats.frameTime) << std::endl;
            }
            
            // 模拟帧率限制（60 FPS）
            auto frameEnd = std::chrono::high_resolution_clock::now();
            auto frameDuration = std::chrono::duration_cast<std::chrono::milliseconds>(frameEnd - frameStart);
            auto targetFrameTime = std::chrono::milliseconds(16); // ~60 FPS
            
            if (frameDuration < targetFrameTime) {
                std::this_thread::sleep_for(targetFrameTime - frameDuration);
            }
        }
        
        auto endTime = std::chrono::high_resolution_clock::now();
        auto totalDuration = std::chrono::duration_cast<std::chrono::milliseconds>(endTime - startTime);
        
        std::cout << std::endl;
        std::cout << "Render loop completed!" << std::endl;
        std::cout << "Total frames: " << frameCount << std::endl;
        std::cout << "Total time: " << totalDuration.count() << "ms" << std::endl;
        std::cout << "Average FPS: " << (frameCount * 1000.0 / totalDuration.count()) << std::endl;
        std::cout << std::endl;
        
        // 打印最终统计信息
        auto finalStats = manager.getRenderStatistics();
        printRenderStatistics(finalStats);
        
        // 等待设备空闲
        manager.waitIdle();
        
        std::cout << std::endl;
        std::cout << "Example completed successfully!" << std::endl;
        
        return 0;
    }
    catch (const std::exception& e) {
        std::cerr << "Exception: " << e.what() << std::endl;
        return 1;
    }
    catch (...) {
        std::cerr << "Unknown exception occurred!" << std::endl;
        return 1;
    }
}
