#pragma once

/* <editor-fold desc="MIT License">

Copyright(c) 2024 VSG Render Engine Abstraction

Permission is hereby granted, free of charge, to any person obtaining a copy of this software and associated documentation files (the "Software"), to deal in the Software without restriction, including without limitation the rights to use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of the Software, and to permit persons to whom the Software is furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.

</editor-fold> */

/**
 * @file Export.h
 * @brief VSG Render Engine Abstraction 导出宏定义
 * 
 * 这个文件定义了跨平台的符号导出宏，用于控制动态库的符号可见性。
 */

// ========== 平台检测 ==========

#if defined(_WIN32) || defined(_WIN64)
#    define VSG_ABSTRACTION_PLATFORM_WINDOWS
#elif defined(__linux__)
#    define VSG_ABSTRACTION_PLATFORM_LINUX
#elif defined(__APPLE__)
#    define VSG_ABSTRACTION_PLATFORM_APPLE
#elif defined(__EMSCRIPTEN__)
#    define VSG_ABSTRACTION_PLATFORM_EMSCRIPTEN
#endif

// ========== 编译器检测 ==========

#if defined(_MSC_VER)
#    define VSG_ABSTRACTION_COMPILER_MSVC
#elif defined(__clang__)
#    define VSG_ABSTRACTION_COMPILER_CLANG
#elif defined(__GNUC__)
#    define VSG_ABSTRACTION_COMPILER_GCC
#endif

// ========== 库类型检测 ==========

// 如果没有明确定义库类型，则根据平台默认设置
#if !defined(VSG_ABSTRACTION_SHARED_LIBRARY) && !defined(VSG_ABSTRACTION_STATIC_LIBRARY)
#    ifdef VSG_ABSTRACTION_PLATFORM_WINDOWS
// Windows默认使用静态库
#        define VSG_ABSTRACTION_STATIC_LIBRARY
#    else
// 其他平台默认使用共享库
#        define VSG_ABSTRACTION_SHARED_LIBRARY
#    endif
#endif

// ========== 导出宏定义 ==========

#ifdef VSG_ABSTRACTION_PLATFORM_WINDOWS
// Windows平台
#    ifdef VSG_ABSTRACTION_SHARED_LIBRARY
// 动态库
#        ifdef VSG_ABSTRACTION_EXPORTS
#            define VSG_ABSTRACTION_DECLSPEC __declspec(dllexport)
#        else
#            define VSG_ABSTRACTION_DECLSPEC __declspec(dllimport)
#        endif
#    else
// 静态库
#        define VSG_ABSTRACTION_DECLSPEC
#    endif
#    define VSG_ABSTRACTION_LOCAL
#    define VSG_ABSTRACTION_CALL __cdecl
#    define VSG_ABSTRACTION_STDCALL __stdcall
#else
// Unix-like平台（Linux、macOS等）
#    if defined(VSG_ABSTRACTION_COMPILER_GCC) || defined(VSG_ABSTRACTION_COMPILER_CLANG)
#        ifdef VSG_ABSTRACTION_SHARED_LIBRARY
#            define VSG_ABSTRACTION_DECLSPEC __attribute__((visibility("default")))
#        else
#            define VSG_ABSTRACTION_DECLSPEC
#        endif
#        define VSG_ABSTRACTION_LOCAL __attribute__((visibility("hidden")))
#    else
#        define VSG_ABSTRACTION_DECLSPEC
#        define VSG_ABSTRACTION_LOCAL
#    endif
#    define VSG_ABSTRACTION_CALL
#    define VSG_ABSTRACTION_STDCALL
#endif

// ========== 废弃标记 ==========

#ifdef VSG_ABSTRACTION_COMPILER_MSVC
#    define VSG_ABSTRACTION_DEPRECATED __declspec(deprecated)
#    define VSG_ABSTRACTION_DEPRECATED_MSG(msg) __declspec(deprecated(msg))
#elif defined(VSG_ABSTRACTION_COMPILER_GCC) || defined(VSG_ABSTRACTION_COMPILER_CLANG)
#    define VSG_ABSTRACTION_DEPRECATED __attribute__((deprecated))
#    define VSG_ABSTRACTION_DEPRECATED_MSG(msg) __attribute__((deprecated(msg)))
#else
#    define VSG_ABSTRACTION_DEPRECATED
#    define VSG_ABSTRACTION_DEPRECATED_MSG(msg)
#endif

// ========== 内联控制 ==========

#ifdef VSG_ABSTRACTION_COMPILER_MSVC
#    define VSG_ABSTRACTION_FORCE_INLINE __forceinline
#    define VSG_ABSTRACTION_NO_INLINE __declspec(noinline)
#elif defined(VSG_ABSTRACTION_COMPILER_GCC) || defined(VSG_ABSTRACTION_COMPILER_CLANG)
#    define VSG_ABSTRACTION_FORCE_INLINE __attribute__((always_inline)) inline
#    define VSG_ABSTRACTION_NO_INLINE __attribute__((noinline))
#else
#    define VSG_ABSTRACTION_FORCE_INLINE inline
#    define VSG_ABSTRACTION_NO_INLINE
#endif

// ========== 分支预测 ==========

#if defined(VSG_ABSTRACTION_COMPILER_GCC) || defined(VSG_ABSTRACTION_COMPILER_CLANG)
#    define VSG_ABSTRACTION_LIKELY(x) __builtin_expect(!!(x), 1)
#    define VSG_ABSTRACTION_UNLIKELY(x) __builtin_expect(!!(x), 0)
#else
#    define VSG_ABSTRACTION_LIKELY(x) (x)
#    define VSG_ABSTRACTION_UNLIKELY(x) (x)
#endif

// ========== 内存对齐 ==========

#ifdef VSG_ABSTRACTION_COMPILER_MSVC
#    define VSG_ABSTRACTION_ALIGN(n) __declspec(align(n))
#elif defined(VSG_ABSTRACTION_COMPILER_GCC) || defined(VSG_ABSTRACTION_COMPILER_CLANG)
#    define VSG_ABSTRACTION_ALIGN(n) __attribute__((aligned(n)))
#else
#    define VSG_ABSTRACTION_ALIGN(n)
#endif

// ========== 函数属性 ==========

#ifdef VSG_ABSTRACTION_COMPILER_MSVC
#    define VSG_ABSTRACTION_NORETURN __declspec(noreturn)
#    define VSG_ABSTRACTION_RESTRICT __restrict
#elif defined(VSG_ABSTRACTION_COMPILER_GCC) || defined(VSG_ABSTRACTION_COMPILER_CLANG)
#    define VSG_ABSTRACTION_NORETURN __attribute__((noreturn))
#    define VSG_ABSTRACTION_RESTRICT __restrict__
#else
#    define VSG_ABSTRACTION_NORETURN
#    define VSG_ABSTRACTION_RESTRICT
#endif

// ========== 调试支持 ==========

#ifdef _DEBUG
#    define VSG_ABSTRACTION_DEBUG_BUILD 1
#else
#    define VSG_ABSTRACTION_DEBUG_BUILD 0
#endif

// 断言宏
#if VSG_ABSTRACTION_DEBUG_BUILD
#    include <cassert>
#    define VSG_ABSTRACTION_ASSERT(condition) assert(condition)
#    define VSG_ABSTRACTION_ASSERT_MSG(condition, message) assert((condition) && (message))
#    define VSG_ABSTRACTION_VERIFY(condition) assert(condition)
#else
#    define VSG_ABSTRACTION_ASSERT(condition) ((void)0)
#    define VSG_ABSTRACTION_ASSERT_MSG(condition, message) ((void)0)
#    define VSG_ABSTRACTION_VERIFY(condition) ((void)(condition))
#endif

// ========== 未使用参数 ==========

#define VSG_ABSTRACTION_UNUSED(x) ((void)(x))

// ========== 字符串化宏 ==========

#define VSG_ABSTRACTION_STRINGIFY_IMPL(x) #x
#define VSG_ABSTRACTION_STRINGIFY(x) VSG_ABSTRACTION_STRINGIFY_IMPL(x)

// ========== 连接宏 ==========

#define VSG_ABSTRACTION_CONCAT_IMPL(a, b) a##b
#define VSG_ABSTRACTION_CONCAT(a, b) VSG_ABSTRACTION_CONCAT_IMPL(a, b)

// ========== 唯一标识符生成 ==========

#define VSG_ABSTRACTION_UNIQUE_NAME(prefix) VSG_ABSTRACTION_CONCAT(prefix, __LINE__)

// ========== 作用域守卫 ==========

#define VSG_ABSTRACTION_SCOPE_EXIT(code)                             \
    auto VSG_ABSTRACTION_UNIQUE_NAME(scope_exit_) = [&]() { code; }; \
    (void)VSG_ABSTRACTION_UNIQUE_NAME(scope_exit_)

// ========== 禁用拷贝和移动 ==========

#define VSG_ABSTRACTION_DISABLE_COPY(ClassName) \
    ClassName(const ClassName&) = delete;       \
    ClassName& operator=(const ClassName&) = delete;

#define VSG_ABSTRACTION_DISABLE_MOVE(ClassName) \
    ClassName(ClassName&&) = delete;            \
    ClassName& operator=(ClassName&&) = delete;

#define VSG_ABSTRACTION_DISABLE_COPY_AND_MOVE(ClassName) \
    VSG_ABSTRACTION_DISABLE_COPY(ClassName)              \
    VSG_ABSTRACTION_DISABLE_MOVE(ClassName)

// ========== 默认拷贝和移动 ==========

#define VSG_ABSTRACTION_DEFAULT_COPY(ClassName) \
    ClassName(const ClassName&) = default;      \
    ClassName& operator=(const ClassName&) = default;

#define VSG_ABSTRACTION_DEFAULT_MOVE(ClassName) \
    ClassName(ClassName&&) = default;           \
    ClassName& operator=(ClassName&&) = default;

#define VSG_ABSTRACTION_DEFAULT_COPY_AND_MOVE(ClassName) \
    VSG_ABSTRACTION_DEFAULT_COPY(ClassName)              \
    VSG_ABSTRACTION_DEFAULT_MOVE(ClassName)

// ========== 单例模式辅助宏 ==========

#define VSG_ABSTRACTION_SINGLETON(ClassName) \
public:                                      \
    static ClassName& instance()             \
    {                                        \
        static ClassName instance;           \
        return instance;                     \
    }                                        \
                                             \
private:                                     \
    ClassName() = default;                   \
    VSG_ABSTRACTION_DISABLE_COPY_AND_MOVE(ClassName)

// ========== 工厂模式辅助宏 ==========

#define VSG_ABSTRACTION_DECLARE_FACTORY_CREATE(ClassName)                \
    template<typename... Args>                                           \
    static std::shared_ptr<ClassName> create(Args&&... args)             \
    {                                                                    \
        return std::make_shared<ClassName>(std::forward<Args>(args)...); \
    }

// ========== API导出宏 ==========

// 主要的API导出宏，用于类和函数声明
#define VSG_ABSTRACTION_API VSG_ABSTRACTION_DECLSPEC

// ========== 接口声明辅助宏 ==========

#define VSG_ABSTRACTION_INTERFACE(InterfaceName) \
    class VSG_ABSTRACTION_DECLSPEC InterfaceName \
    {                                            \
    public:                                      \
        virtual ~InterfaceName() = default;      \
                                                 \
    protected:                                   \
        InterfaceName() = default;               \
        VSG_ABSTRACTION_DISABLE_COPY_AND_MOVE(InterfaceName)

#define VSG_ABSTRACTION_END_INTERFACE() \
    }                                   \
    ;

// ========== 命名空间辅助宏 ==========

#define VSG_ABSTRACTION_NAMESPACE_BEGIN \
    namespace vsg_abstraction           \
    {
#define VSG_ABSTRACTION_NAMESPACE_END }

// ========== 版本检查宏 ==========

#define VSG_ABSTRACTION_VERSION_CHECK(major, minor, patch)                                    \
    ((VSG_ABSTRACTION_VERSION_MAJOR > (major)) ||                                             \
     (VSG_ABSTRACTION_VERSION_MAJOR == (major) && VSG_ABSTRACTION_VERSION_MINOR > (minor)) || \
     (VSG_ABSTRACTION_VERSION_MAJOR == (major) && VSG_ABSTRACTION_VERSION_MINOR == (minor) && VSG_ABSTRACTION_VERSION_PATCH >= (patch)))

// ========== 特性检测宏 ==========

#if __cplusplus >= 202002L
#    define VSG_ABSTRACTION_HAS_CPP20 1
#else
#    define VSG_ABSTRACTION_HAS_CPP20 0
#endif

#if __cplusplus >= 201703L
#    define VSG_ABSTRACTION_HAS_CPP17 1
#else
#    define VSG_ABSTRACTION_HAS_CPP17 0
#endif

#if __cplusplus >= 201402L
#    define VSG_ABSTRACTION_HAS_CPP14 1
#else
#    define VSG_ABSTRACTION_HAS_CPP14 0
#endif

// ========== 条件编译辅助 ==========

#ifdef VSG_ABSTRACTION_PLATFORM_WINDOWS
#    define VSG_ABSTRACTION_WINDOWS_ONLY(code) code
#    define VSG_ABSTRACTION_NON_WINDOWS_ONLY(code)
#else
#    define VSG_ABSTRACTION_WINDOWS_ONLY(code)
#    define VSG_ABSTRACTION_NON_WINDOWS_ONLY(code) code
#endif

#ifdef VSG_ABSTRACTION_PLATFORM_LINUX
#    define VSG_ABSTRACTION_LINUX_ONLY(code) code
#else
#    define VSG_ABSTRACTION_LINUX_ONLY(code)
#endif

#ifdef VSG_ABSTRACTION_PLATFORM_APPLE
#    define VSG_ABSTRACTION_APPLE_ONLY(code) code
#else
#    define VSG_ABSTRACTION_APPLE_ONLY(code)
#endif

#ifdef VSG_ABSTRACTION_PLATFORM_EMSCRIPTEN
#    define VSG_ABSTRACTION_EMSCRIPTEN_ONLY(code) code
#else
#    define VSG_ABSTRACTION_EMSCRIPTEN_ONLY(code)
#endif
