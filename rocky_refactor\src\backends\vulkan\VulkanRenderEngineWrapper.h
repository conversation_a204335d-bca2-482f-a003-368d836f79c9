#pragma once

/* <editor-fold desc="MIT License">

Copyright(c) 2024 Rocky Render Engine Refactor

Permission is hereby granted, free of charge, to any person obtaining a copy of this software and associated documentation files (the "Software"), to deal in the Software without restriction, including without limitation the rights to use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of the Software, and to permit persons to whom the Software is furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.

</editor-fold> */

#include <rocky/render/IRenderEngine.h>
#include <rocky/render/Config.h>

// VSG抽象层头文件
#include "../../../vsg_render_abstraction/include/vsg_abstraction/core/IRenderEngine.h"
#include "../../../vsg_render_abstraction/include/vsg_abstraction/backends/VSGRenderEngine.h"

#include <memory>

namespace rocky {
namespace render {

/**
 * @brief Vulkan渲染引擎包装器
 * 
 * 这个类将VSG抽象层的渲染引擎包装为Rocky的IRenderEngine接口，
 * 实现了两个抽象层之间的桥接。
 */
class VulkanRenderEngineWrapper : public IRenderEngine {
public:
    /**
     * @brief 构造函数
     * @param vsgEngine VSG抽象层渲染引擎
     */
    explicit VulkanRenderEngineWrapper(std::unique_ptr<vsg_abstraction::IRenderEngine> vsgEngine);
    
    virtual ~VulkanRenderEngineWrapper();

    // ========== 生命周期管理 ==========
    bool initialize() override;
    void shutdown() override;
    bool isInitialized() const override;

    // ========== 后端信息 ==========
    RenderBackend getBackendType() const override;
    EngineInfo getEngineInfo() const override;

    // ========== 设备管理 ==========
    IDevice* getDevice() override;
    std::vector<IPhysicalDevice*> getPhysicalDevices() override;

    // ========== 场景图创建 ==========
    ref_ptr<IGroup> createGroup() override;
    ref_ptr<ITransform> createTransform() override;
    ref_ptr<IGeometry> createGeometry() override;
    ref_ptr<IStateGroup> createStateGroup() override;

    // ========== 窗口和渲染管理 ==========
    ref_ptr<IWindow> createWindow(const WindowConfig& config) override;
    ref_ptr<IRenderGraph> createRenderGraph(ref_ptr<IWindow> window) override;
    ref_ptr<IRecordTraversal> createRecordTraversal() override;

    // ========== 资源管理 ==========
    ref_ptr<IBuffer> createBuffer(const BufferInfo& info) override;
    ref_ptr<IImage> createImage(const ImageInfo& info) override;
    ref_ptr<IPipeline> createGraphicsPipeline(const GraphicsPipelineInfo& info) override;
    ref_ptr<IPipeline> createComputePipeline(const ComputePipelineInfo& info) override;

    // ========== 同步和性能 ==========
    void waitIdle() override;
    RenderStatistics getRenderStatistics() const override;
    void resetRenderStatistics() override;

    // ========== 调试和诊断 ==========
    void setDebugCallback(DebugCallback callback) override;
    void beginProfileMarker(const std::string& name) override;
    void endProfileMarker() override;

    // ========== 扩展接口 ==========
    void* getNativeHandle() const override;
    void executeCustomCommand(std::function<void(void*)> command) override;
    bool supportsFeature(const std::string& feature) const override;

    // ========== VSG特定接口 ==========
    
    /**
     * @brief 获取底层VSG抽象层引擎
     * @return VSG抽象层引擎指针
     */
    vsg_abstraction::IRenderEngine* getVSGAbstractionEngine() const;
    
    /**
     * @brief 获取VSG渲染引擎（如果可用）
     * @return VSG渲染引擎指针
     */
    vsg_abstraction::VSGRenderEngine* getVSGRenderEngine() const;

private:
    // 类型转换辅助函数
    vsg_abstraction::WindowTraits convertWindowConfig(const WindowConfig& config);
    vsg_abstraction::BufferInfo convertBufferInfo(const BufferInfo& info);
    vsg_abstraction::ImageInfo convertImageInfo(const ImageInfo& info);
    vsg_abstraction::GraphicsPipelineInfo convertGraphicsPipelineInfo(const GraphicsPipelineInfo& info);
    vsg_abstraction::ComputePipelineInfo convertComputePipelineInfo(const ComputePipelineInfo& info);
    
    // 反向类型转换
    EngineInfo convertEngineInfo(const vsg_abstraction::EngineInfo& info);
    RenderStatistics convertRenderStatistics(const vsg_abstraction::RenderStatistics& stats);
    
    // 成员变量
    std::unique_ptr<vsg_abstraction::IRenderEngine> vsgEngine_;
    
    // 适配器缓存
    std::unordered_map<void*, std::weak_ptr<INode>> nodeAdapterCache_;
    std::mutex cacheMapMutex_;
};

/**
 * @brief 节点适配器基类
 * 
 * 将VSG抽象层的节点包装为Rocky的节点接口
 */
class VSGNodeWrapper : public INode {
public:
    explicit VSGNodeWrapper(vsg_abstraction::ref_ptr<vsg_abstraction::INode> vsgNode);
    virtual ~VSGNodeWrapper() = default;

    // ========== 基本属性 ==========
    NodeType getNodeType() const override;
    const std::string& getName() const override;
    void setName(const std::string& name) override;
    uint64_t getNodeId() const override;

    // ========== 访问者模式 ==========
    void accept(IVisitor& visitor) override;
    void traverse(IVisitor& visitor) override;

    // ========== 包围盒 ==========
    std::array<double, 6> getLocalBounds() const override;
    std::array<double, 6> getWorldBounds() const override;
    void computeBounds() override;

    // ========== 用户数据 ==========
    void setUserData(const std::string& key, void* data) override;
    void* getUserData(const std::string& key) const override;
    void removeUserData(const std::string& key) override;
    bool hasUserData(const std::string& key) const override;

    // ========== 原生对象访问 ==========
    void* getNativeHandle() const override;
    
    /**
     * @brief 获取VSG抽象层节点
     */
    vsg_abstraction::ref_ptr<vsg_abstraction::INode> getVSGAbstractionNode() const;

protected:
    vsg_abstraction::ref_ptr<vsg_abstraction::INode> vsgNode_;
    
    // 类型转换辅助
    NodeType convertNodeType(vsg_abstraction::NodeType type) const;
    vsg_abstraction::NodeType convertToVSGNodeType(NodeType type) const;
};

/**
 * @brief 组节点包装器
 */
class VSGGroupWrapper : public VSGNodeWrapper, public IGroup {
public:
    explicit VSGGroupWrapper(vsg_abstraction::ref_ptr<vsg_abstraction::IGroup> vsgGroup);

    // ========== 子节点管理 ==========
    void addChild(ref_ptr<INode> child) override;
    void removeChild(ref_ptr<INode> child) override;
    void removeChild(size_t index) override;
    void removeAllChildren() override;
    size_t getNumChildren() const override;
    ref_ptr<INode> getChild(size_t index) const override;
    const std::vector<ref_ptr<INode>>& getChildren() const override;
    ref_ptr<INode> findChild(const std::string& name) const override;

private:
    vsg_abstraction::ref_ptr<vsg_abstraction::IGroup> vsgGroup_;
    mutable std::vector<ref_ptr<INode>> childrenCache_;
    mutable bool childrenCacheValid_ = false;
    
    void updateChildrenCache() const;
    void invalidateChildrenCache();
};

/**
 * @brief 变换节点包装器
 */
class VSGTransformWrapper : public VSGGroupWrapper, public ITransform {
public:
    explicit VSGTransformWrapper(vsg_abstraction::ref_ptr<vsg_abstraction::ITransform> vsgTransform);

    // ========== 变换操作 ==========
    void setMatrix(const mat4& matrix) override;
    const mat4& getMatrix() const override;
    mat4 getWorldMatrix() const override;
    void setPosition(const vec3& position) override;
    vec3 getPosition() const override;
    void setRotation(const vec4& rotation) override;
    vec4 getRotation() const override;
    void setScale(const vec3& scale) override;
    vec3 getScale() const override;

private:
    vsg_abstraction::ref_ptr<vsg_abstraction::ITransform> vsgTransform_;
    
    // 类型转换辅助
    vsg_abstraction::mat4 convertMatrix(const mat4& matrix) const;
    mat4 convertMatrix(const vsg_abstraction::mat4& matrix) const;
    vsg_abstraction::vec3 convertVec3(const vec3& vec) const;
    vec3 convertVec3(const vsg_abstraction::vec3& vec) const;
    vsg_abstraction::vec4 convertVec4(const vec4& vec) const;
    vec4 convertVec4(const vsg_abstraction::vec4& vec) const;
};

} // namespace render
} // namespace rocky
