# Resources within VulkanSceneGraph repo

* [Project Code of Conduct](CODE_OF_CONDUCT.md)
* [Contribution to VulkanSceneGraph Project](CONTRIBUTING.md)

# Useful 3rd party links to information on Vulkan

* [Introduction to Vulkan | Algorithms for Real-Time Rendering Lecture, Summer Term 2020](https://www.youtube.com/watch?v=isbMMIwmZes)
* [Vulkanised-2021 presentations](https://www.khronos.org/events/vulkanised-2021)
* Vulkan Synchronization:
    - [Guide to Vulkan Synchronization](https://www.lunarg.com/news-insights/white-papers/guide-to-vulkan-synchronization-validation/)
    - [Synchronization2 Validation](https://www.lunarg.com/news-insights/white-papers/vulkan-synchronization2-validation/)
* Building a Vulkan Layer in Symbiose Within the Vulkan Ecosystem:
    - Whitepaper: https://www.lunarg.com/wp-content/uploads/2021/09/Vulkan-Layer-Symbiosis-within-the-Vulkan-Ecosystem.pdf
    - Whitepaper: https://www.lunarg.com/wp-content/uploads/2021/09/Enhanced-Devsim-15Sept2021.pdf
